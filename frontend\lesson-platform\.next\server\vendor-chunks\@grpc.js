"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@grpc";
exports.ids = ["vendor-chunks/@grpc"];
exports.modules = {

/***/ "(ssr)/./node_modules/@grpc/proto-loader/build/src/index.js":
/*!************************************************************!*\
  !*** ./node_modules/@grpc/proto-loader/build/src/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * @license\n * Copyright 2018 gRPC authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.loadFileDescriptorSetFromObject = exports.loadFileDescriptorSetFromBuffer = exports.fromJSON = exports.loadSync = exports.load = exports.IdempotencyLevel = exports.isAnyExtension = exports.Long = void 0;\nconst camelCase = __webpack_require__(/*! lodash.camelcase */ \"(ssr)/./node_modules/lodash.camelcase/index.js\");\nconst Protobuf = __webpack_require__(/*! protobufjs */ \"(ssr)/./node_modules/protobufjs/index.js\");\nconst descriptor = __webpack_require__(/*! protobufjs/ext/descriptor */ \"(ssr)/./node_modules/protobufjs/ext/descriptor/index.js\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/@grpc/proto-loader/build/src/util.js\");\nconst Long = __webpack_require__(/*! long */ \"(ssr)/./node_modules/long/umd/index.js\");\nexports.Long = Long;\nfunction isAnyExtension(obj) {\n    return ('@type' in obj) && (typeof obj['@type'] === 'string');\n}\nexports.isAnyExtension = isAnyExtension;\nvar IdempotencyLevel;\n(function (IdempotencyLevel) {\n    IdempotencyLevel[\"IDEMPOTENCY_UNKNOWN\"] = \"IDEMPOTENCY_UNKNOWN\";\n    IdempotencyLevel[\"NO_SIDE_EFFECTS\"] = \"NO_SIDE_EFFECTS\";\n    IdempotencyLevel[\"IDEMPOTENT\"] = \"IDEMPOTENT\";\n})(IdempotencyLevel = exports.IdempotencyLevel || (exports.IdempotencyLevel = {}));\nconst descriptorOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    defaults: true,\n    oneofs: true,\n    json: true,\n};\nfunction joinName(baseName, name) {\n    if (baseName === '') {\n        return name;\n    }\n    else {\n        return baseName + '.' + name;\n    }\n}\nfunction isHandledReflectionObject(obj) {\n    return (obj instanceof Protobuf.Service ||\n        obj instanceof Protobuf.Type ||\n        obj instanceof Protobuf.Enum);\n}\nfunction isNamespaceBase(obj) {\n    return obj instanceof Protobuf.Namespace || obj instanceof Protobuf.Root;\n}\nfunction getAllHandledReflectionObjects(obj, parentName) {\n    const objName = joinName(parentName, obj.name);\n    if (isHandledReflectionObject(obj)) {\n        return [[objName, obj]];\n    }\n    else {\n        if (isNamespaceBase(obj) && typeof obj.nested !== 'undefined') {\n            return Object.keys(obj.nested)\n                .map(name => {\n                return getAllHandledReflectionObjects(obj.nested[name], objName);\n            })\n                .reduce((accumulator, currentValue) => accumulator.concat(currentValue), []);\n        }\n    }\n    return [];\n}\nfunction createDeserializer(cls, options) {\n    return function deserialize(argBuf) {\n        return cls.toObject(cls.decode(argBuf), options);\n    };\n}\nfunction createSerializer(cls) {\n    return function serialize(arg) {\n        if (Array.isArray(arg)) {\n            throw new Error(`Failed to serialize message: expected object with ${cls.name} structure, got array instead`);\n        }\n        const message = cls.fromObject(arg);\n        return cls.encode(message).finish();\n    };\n}\nfunction mapMethodOptions(options) {\n    return (options || []).reduce((obj, item) => {\n        for (const [key, value] of Object.entries(item)) {\n            switch (key) {\n                case 'uninterpreted_option':\n                    obj.uninterpreted_option.push(item.uninterpreted_option);\n                    break;\n                default:\n                    obj[key] = value;\n            }\n        }\n        return obj;\n    }, {\n        deprecated: false,\n        idempotency_level: IdempotencyLevel.IDEMPOTENCY_UNKNOWN,\n        uninterpreted_option: [],\n    });\n}\nfunction createMethodDefinition(method, serviceName, options, fileDescriptors) {\n    /* This is only ever called after the corresponding root.resolveAll(), so we\n     * can assume that the resolved request and response types are non-null */\n    const requestType = method.resolvedRequestType;\n    const responseType = method.resolvedResponseType;\n    return {\n        path: '/' + serviceName + '/' + method.name,\n        requestStream: !!method.requestStream,\n        responseStream: !!method.responseStream,\n        requestSerialize: createSerializer(requestType),\n        requestDeserialize: createDeserializer(requestType, options),\n        responseSerialize: createSerializer(responseType),\n        responseDeserialize: createDeserializer(responseType, options),\n        // TODO(murgatroid99): Find a better way to handle this\n        originalName: camelCase(method.name),\n        requestType: createMessageDefinition(requestType, fileDescriptors),\n        responseType: createMessageDefinition(responseType, fileDescriptors),\n        options: mapMethodOptions(method.parsedOptions),\n    };\n}\nfunction createServiceDefinition(service, name, options, fileDescriptors) {\n    const def = {};\n    for (const method of service.methodsArray) {\n        def[method.name] = createMethodDefinition(method, name, options, fileDescriptors);\n    }\n    return def;\n}\nfunction createMessageDefinition(message, fileDescriptors) {\n    const messageDescriptor = message.toDescriptor('proto3');\n    return {\n        format: 'Protocol Buffer 3 DescriptorProto',\n        type: messageDescriptor.$type.toObject(messageDescriptor, descriptorOptions),\n        fileDescriptorProtos: fileDescriptors,\n    };\n}\nfunction createEnumDefinition(enumType, fileDescriptors) {\n    const enumDescriptor = enumType.toDescriptor('proto3');\n    return {\n        format: 'Protocol Buffer 3 EnumDescriptorProto',\n        type: enumDescriptor.$type.toObject(enumDescriptor, descriptorOptions),\n        fileDescriptorProtos: fileDescriptors,\n    };\n}\n/**\n * function createDefinition(obj: Protobuf.Service, name: string, options:\n * Options): ServiceDefinition; function createDefinition(obj: Protobuf.Type,\n * name: string, options: Options): MessageTypeDefinition; function\n * createDefinition(obj: Protobuf.Enum, name: string, options: Options):\n * EnumTypeDefinition;\n */\nfunction createDefinition(obj, name, options, fileDescriptors) {\n    if (obj instanceof Protobuf.Service) {\n        return createServiceDefinition(obj, name, options, fileDescriptors);\n    }\n    else if (obj instanceof Protobuf.Type) {\n        return createMessageDefinition(obj, fileDescriptors);\n    }\n    else if (obj instanceof Protobuf.Enum) {\n        return createEnumDefinition(obj, fileDescriptors);\n    }\n    else {\n        throw new Error('Type mismatch in reflection object handling');\n    }\n}\nfunction createPackageDefinition(root, options) {\n    const def = {};\n    root.resolveAll();\n    const descriptorList = root.toDescriptor('proto3').file;\n    const bufferList = descriptorList.map(value => Buffer.from(descriptor.FileDescriptorProto.encode(value).finish()));\n    for (const [name, obj] of getAllHandledReflectionObjects(root, '')) {\n        def[name] = createDefinition(obj, name, options, bufferList);\n    }\n    return def;\n}\nfunction createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options) {\n    options = options || {};\n    const root = Protobuf.Root.fromDescriptor(decodedDescriptorSet);\n    root.resolveAll();\n    return createPackageDefinition(root, options);\n}\n/**\n * Load a .proto file with the specified options.\n * @param filename One or multiple file paths to load. Can be an absolute path\n *     or relative to an include path.\n * @param options.keepCase Preserve field names. The default is to change them\n *     to camel case.\n * @param options.longs The type that should be used to represent `long` values.\n *     Valid options are `Number` and `String`. Defaults to a `Long` object type\n *     from a library.\n * @param options.enums The type that should be used to represent `enum` values.\n *     The only valid option is `String`. Defaults to the numeric value.\n * @param options.bytes The type that should be used to represent `bytes`\n *     values. Valid options are `Array` and `String`. The default is to use\n *     `Buffer`.\n * @param options.defaults Set default values on output objects. Defaults to\n *     `false`.\n * @param options.arrays Set empty arrays for missing array values even if\n *     `defaults` is `false`. Defaults to `false`.\n * @param options.objects Set empty objects for missing object values even if\n *     `defaults` is `false`. Defaults to `false`.\n * @param options.oneofs Set virtual oneof properties to the present field's\n *     name\n * @param options.json Represent Infinity and NaN as strings in float fields,\n *     and automatically decode google.protobuf.Any values.\n * @param options.includeDirs Paths to search for imported `.proto` files.\n */\nfunction load(filename, options) {\n    return (0, util_1.loadProtosWithOptions)(filename, options).then(loadedRoot => {\n        return createPackageDefinition(loadedRoot, options);\n    });\n}\nexports.load = load;\nfunction loadSync(filename, options) {\n    const loadedRoot = (0, util_1.loadProtosWithOptionsSync)(filename, options);\n    return createPackageDefinition(loadedRoot, options);\n}\nexports.loadSync = loadSync;\nfunction fromJSON(json, options) {\n    options = options || {};\n    const loadedRoot = Protobuf.Root.fromJSON(json);\n    loadedRoot.resolveAll();\n    return createPackageDefinition(loadedRoot, options);\n}\nexports.fromJSON = fromJSON;\nfunction loadFileDescriptorSetFromBuffer(descriptorSet, options) {\n    const decodedDescriptorSet = descriptor.FileDescriptorSet.decode(descriptorSet);\n    return createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options);\n}\nexports.loadFileDescriptorSetFromBuffer = loadFileDescriptorSetFromBuffer;\nfunction loadFileDescriptorSetFromObject(descriptorSet, options) {\n    const decodedDescriptorSet = descriptor.FileDescriptorSet.fromObject(descriptorSet);\n    return createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options);\n}\nexports.loadFileDescriptorSetFromObject = loadFileDescriptorSetFromObject;\n(0, util_1.addCommonProtos)();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@grpc/proto-loader/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@grpc/proto-loader/build/src/util.js":
/*!***********************************************************!*\
  !*** ./node_modules/@grpc/proto-loader/build/src/util.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * @license\n * Copyright 2018 gRPC authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addCommonProtos = exports.loadProtosWithOptionsSync = exports.loadProtosWithOptions = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst Protobuf = __webpack_require__(/*! protobufjs */ \"(ssr)/./node_modules/protobufjs/index.js\");\nfunction addIncludePathResolver(root, includePaths) {\n    const originalResolvePath = root.resolvePath;\n    root.resolvePath = (origin, target) => {\n        if (path.isAbsolute(target)) {\n            return target;\n        }\n        for (const directory of includePaths) {\n            const fullPath = path.join(directory, target);\n            try {\n                fs.accessSync(fullPath, fs.constants.R_OK);\n                return fullPath;\n            }\n            catch (err) {\n                continue;\n            }\n        }\n        process.emitWarning(`${target} not found in any of the include paths ${includePaths}`);\n        return originalResolvePath(origin, target);\n    };\n}\nasync function loadProtosWithOptions(filename, options) {\n    const root = new Protobuf.Root();\n    options = options || {};\n    if (!!options.includeDirs) {\n        if (!Array.isArray(options.includeDirs)) {\n            return Promise.reject(new Error('The includeDirs option must be an array'));\n        }\n        addIncludePathResolver(root, options.includeDirs);\n    }\n    const loadedRoot = await root.load(filename, options);\n    loadedRoot.resolveAll();\n    return loadedRoot;\n}\nexports.loadProtosWithOptions = loadProtosWithOptions;\nfunction loadProtosWithOptionsSync(filename, options) {\n    const root = new Protobuf.Root();\n    options = options || {};\n    if (!!options.includeDirs) {\n        if (!Array.isArray(options.includeDirs)) {\n            throw new Error('The includeDirs option must be an array');\n        }\n        addIncludePathResolver(root, options.includeDirs);\n    }\n    const loadedRoot = root.loadSync(filename, options);\n    loadedRoot.resolveAll();\n    return loadedRoot;\n}\nexports.loadProtosWithOptionsSync = loadProtosWithOptionsSync;\n/**\n * Load Google's well-known proto files that aren't exposed by Protobuf.js.\n */\nfunction addCommonProtos() {\n    // Protobuf.js exposes: any, duration, empty, field_mask, struct, timestamp,\n    // and wrappers. compiler/plugin is excluded in Protobuf.js and here.\n    // Using constant strings for compatibility with tools like Webpack\n    const apiDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/api.json */ \"(ssr)/./node_modules/protobufjs/google/protobuf/api.json\");\n    const descriptorDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/descriptor.json */ \"(ssr)/./node_modules/protobufjs/google/protobuf/descriptor.json\");\n    const sourceContextDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/source_context.json */ \"(ssr)/./node_modules/protobufjs/google/protobuf/source_context.json\");\n    const typeDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/type.json */ \"(ssr)/./node_modules/protobufjs/google/protobuf/type.json\");\n    Protobuf.common('api', apiDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('descriptor', descriptorDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('source_context', sourceContextDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('type', typeDescriptor.nested.google.nested.protobuf.nested);\n}\nexports.addCommonProtos = addCommonProtos;\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@grpc/proto-loader/build/src/util.js\n");

/***/ })

};
;