'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface TimetableSlot {
  subject: string;
  startTime: string;
  endTime: string;
  lessonRef?: string;
}

interface DaySchedule {
  day: string;
  lessons: TimetableSlot[];
}

interface TimetableProps {
  studentId: string;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const TIME_SLOTS = Array.from({ length: 9 }, (_, i) => {
  const hour = Math.floor(i / 2) + 8;
  const minute = i % 2 === 0 ? '00' : '30';
  return `${hour.toString().padStart(2, '0')}:${minute}`;
});

const Timetable: React.FC<TimetableProps> = ({ studentId }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timetable, setTimetable] = useState<DaySchedule[]>([]);
  const [regenerating, setRegenerating] = useState(false);

  useEffect(() => {
    fetchTimetable();
  }, [studentId]);

  const fetchTimetable = async () => {
    try {
      const response = await fetch('/generate-timetable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: studentId,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setTimetable(data.data.timetable);
      } else {
        setError(data.message || 'Failed to fetch timetable');
      }
    } catch (err) {
      setError('Failed to communicate with server');
    } finally {
      setLoading(false);
    }
  };

  const handleRegenerateTimetable = async () => {
    setRegenerating(true);
    setError('');
    try {
      const response = await fetch('/generate-timetable', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: studentId,
          regenerate: true,
        }),
      });

      const data = await response.json();
      if (data.success) {
        setTimetable(data.data.timetable);
      } else {
        setError(data.message || 'Failed to regenerate timetable');
      }
    } catch (err) {
      setError('Failed to regenerate timetable');
    } finally {
      setRegenerating(false);
    }
  };

  const getSlotContent = (day: string, timeSlot: string): TimetableSlot | null => {
    const daySchedule = timetable.find(schedule => schedule.day === day);
    if (!daySchedule) return null;

    return daySchedule.lessons.find(lesson => 
      lesson.startTime <= timeSlot && lesson.endTime > timeSlot
    ) || null;
  };

  const handleStartLesson = async (slot: TimetableSlot) => {
    if (!slot.lessonRef) return;

    try {
      // Navigate to start-lesson page with proper parameters
      const params = {
        lessonRef: slot.lessonRef,
        studentId: studentId || '',
        subject: slot.subject || '',
        grade: '', // Add grade if available in slot
        level: '', // Add level if available in slot
        curriculum: '', // Add curriculum if available in slot
        country: '', // Add country if available in slot
      };

      const queryParams = new URLSearchParams(params).toString();
      router.push(`/start-lesson?${queryParams}`);
    } catch (error) {
      setError('Failed to start lesson');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Class Timetable</h2>
        <button
          onClick={handleRegenerateTimetable}
          disabled={regenerating}
          className={`py-2 px-4 bg-blue-600 text-white rounded hover:bg-blue-700 ${
            regenerating ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {regenerating ? 'Regenerating...' : 'Regenerate Timetable'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="border p-2 bg-gray-50">Time</th>
              {DAYS.map(day => (
                <th key={day} className="border p-2 bg-gray-50">{day}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {TIME_SLOTS.map((timeSlot, index) => (
              <tr key={timeSlot}>
                <td className="border p-2 text-center bg-gray-50 font-medium">
                  {timeSlot}
                </td>
                {DAYS.map(day => {
                  const slot = getSlotContent(day, timeSlot);
                  return (
                    <td
                      key={`${day}-${timeSlot}`}
                      className={`border p-2 ${
                        slot ? 'bg-blue-50' : ''
                      }`}
                    >
                      {slot && (
                        <div 
                          onClick={() => handleStartLesson(slot)}
                          className={`cursor-pointer hover:bg-blue-100 transition-colors ${
                            slot ? 'bg-blue-50' : ''
                          }`}
                        >
                          <div className="font-medium">{slot.subject}</div>
                          <div className="text-sm text-gray-600">
                            {slot.startTime} - {slot.endTime}
                          </div>
                        </div>
                      )}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Timetable;
