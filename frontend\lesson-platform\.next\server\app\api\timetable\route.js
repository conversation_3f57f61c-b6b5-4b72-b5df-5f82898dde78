/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/timetable/route";
exports.ids = ["app/api/timetable/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/timetable/route.ts */ \"(rsc)/./src/app/api/timetable/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/timetable/route\",\n        pathname: \"/api/timetable\",\n        filename: \"route\",\n        bundlePath: \"app/api/timetable/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\timetable\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/timetable/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/timetable/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n/* harmony import */ var firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase-admin/auth */ \"firebase-admin/auth\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__]);\nfirebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Define the standard time slots for timetable generation\nconst STANDARD_TIME_SLOTS = [\n    '08:00-08:45',\n    '08:50-09:35',\n    '09:40-10:25',\n    '10:40-11:25',\n    '11:30-12:15',\n    '13:15-14:00',\n    '14:15-15:00'\n];\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\n// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)\nconst SUBJECT_COGNITIVE_LOADS = {\n    'mathematics': 9,\n    'english_language': 8,\n    'basic_science_and_technology': 7,\n    'basic_science': 7,\n    'social_studies': 6,\n    'computer_studies': 8,\n    'computing': 8,\n    'artificial_intelligence': 9,\n    'french': 7,\n    'christian_religious_knowledge': 5,\n    'national_values_education': 4,\n    'entrepreneurship_education': 5,\n    'entrepreneurship': 5,\n    'financial_literacy': 6,\n    'creative_arts': 3,\n    'cultural_and_creative_arts': 3,\n    'art_and_design': 3,\n    'physical_health_education': 2,\n    'project_based_excellence': 6\n};\n// Calculate lesson reference for a specific week and subject\nfunction generateLessonReference(grade, subjectCode, academicWeek, lessonNumber, lessonsPerWeek) {\n    // Calculate the absolute lesson number across all weeks\n    const absoluteLessonNumber = (academicWeek - 1) * lessonsPerWeek + lessonNumber;\n    // Format lesson number with leading zeros (e.g., 001, 002, etc.)\n    const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');\n    // Convert grade to proper abbreviated format\n    const abbreviatedGrade = convertGradeToAbbreviation(grade);\n    return `${abbreviatedGrade}-${subjectCode}-${formattedLessonNumber}`;\n}\n// Convert grade level to proper abbreviated format\nfunction convertGradeToAbbreviation(grade) {\n    // Handle different grade formats\n    const gradeStr = grade.toLowerCase().trim();\n    // Primary grades\n    if (gradeStr.includes('primary') || gradeStr.startsWith('p')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `P${match[1]}`;\n        }\n    }\n    // Junior Secondary\n    if (gradeStr.includes('junior') || gradeStr.includes('jss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `JSS${match[1]}`;\n        }\n    }\n    // Senior Secondary\n    if (gradeStr.includes('senior') || gradeStr.includes('sss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `SSS${match[1]}`;\n        }\n    }\n    // If already in correct format, return as is\n    if (/^(P|JSS|SSS)\\d+$/i.test(grade)) {\n        return grade.toUpperCase();\n    }\n    // Default fallback\n    return grade;\n}\n// Fetch student enrollments from Firestore\nasync function fetchStudentEnrollments(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        let enrollments = [];\n        let studentRef = null;\n        for (const format of studentIdFormats){\n            console.log(`[Timetable API] Trying student ID format: ${format}`);\n            const testStudentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const testStudentSnap = await testStudentRef.get();\n            if (testStudentSnap.exists) {\n                console.log(`[Timetable API] Found student document with ID: ${format}`);\n                studentRef = testStudentRef;\n                break;\n            }\n        }\n        if (!studentRef) {\n            console.warn(`[Timetable API] No student document found for any ID format`);\n            return [];\n        }\n        // Fetch enrollments from the enrollments subcollection\n        const enrollmentsRef = studentRef.collection('enrollments');\n        const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();\n        console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);\n        enrollmentsSnap.docs.forEach((doc)=>{\n            const data = doc.data();\n            const subjectId = doc.id;\n            enrollments.push({\n                subjectId: subjectId,\n                subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),\n                lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,\n                status: data.status,\n                enrolledAt: data.enrolledAt || data.enrolled_at,\n                cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load\n            });\n        });\n        console.log(`[Timetable API] Processed enrollments:`, enrollments);\n        return enrollments;\n    } catch (error) {\n        console.error(`[Timetable API] Error fetching enrollments:`, error);\n        return [];\n    }\n}\n// Get subject code from subject ID\nfunction getSubjectCodeFromId(subjectId) {\n    // Normalize the input: convert to lowercase and replace hyphens/spaces with underscores\n    const normalizedId = subjectId.toLowerCase().replace(/[-\\s]+/g, '_').replace(/[^a-z0-9_]/g, ''); // Remove any other special characters\n    const codeMap = {\n        'mathematics': 'MAT',\n        'english_language': 'ENG',\n        'basic_science_and_technology': 'BST',\n        'basic_science': 'BST',\n        'social_studies': 'SST',\n        'computer_studies': 'COM',\n        'computing': 'COM',\n        'creative_arts': 'ART',\n        'cultural_and_creative_arts': 'CCA',\n        'art_and_design': 'ART',\n        'physical_health_education': 'PHE',\n        'national_values_education': 'NVE',\n        'entrepreneurship_education': 'ENT',\n        'entrepreneurship': 'ENT',\n        'financial_literacy': 'FIL',\n        'french': 'FRE',\n        'artificial_intelligence': 'AI',\n        'project_based_excellence': 'PBE',\n        'project_based_excellence': 'PBE',\n        'christian_religious_knowledge': 'CRK'\n    };\n    // Try the normalized ID first\n    if (codeMap[normalizedId]) {\n        return codeMap[normalizedId];\n    }\n    // Try the original ID as fallback\n    if (codeMap[subjectId.toLowerCase()]) {\n        return codeMap[subjectId.toLowerCase()];\n    }\n    // Special handling for project-based excellence variants\n    if (subjectId.toLowerCase().includes('project') && subjectId.toLowerCase().includes('excellence')) {\n        return 'PBE';\n    }\n    return 'GEN';\n}\n// Distribute lessons across the week based on lessons per week and cognitive load\nfunction distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade) {\n    const weeklyLessons = [];\n    console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);\n    // Create a weekly schedule grid: [day][timeSlot]\n    const weekSchedule = DAYS_OF_WEEK.map(()=>new Array(STANDARD_TIME_SLOTS.length).fill(null));\n    // Calculate total lessons needed\n    const totalLessonsNeeded = enrollments.reduce((sum, e)=>sum + e.lessonsPerWeek, 0);\n    const totalSlotsAvailable = DAYS_OF_WEEK.length * STANDARD_TIME_SLOTS.length; // 5 days × 7 slots = 35\n    console.log(`[Timetable API] Total lessons needed: ${totalLessonsNeeded}, Total slots available: ${totalSlotsAvailable}`);\n    // Sort enrollments by cognitive load (highest first) for optimal time slot assignment\n    const sortedEnrollments = [\n        ...enrollments\n    ].sort((a, b)=>(b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));\n    console.log(`[Timetable API] Sorted subjects by cognitive load:`, sortedEnrollments.map((e)=>`${e.subjectName} (${e.cognitiveLoad}) - ${e.lessonsPerWeek} lessons`));\n    // Track how many lessons each subject has been assigned\n    const subjectLessonCounts = {};\n    // Initialize all subjects\n    sortedEnrollments.forEach((enrollment)=>{\n        subjectLessonCounts[enrollment.subjectId] = 0;\n    });\n    // Schedule all lessons using a more aggressive approach\n    scheduleAllLessonsOptimally(sortedEnrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);\n    // Fill any remaining empty slots with \"Free Period\"\n    fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade);\n    // Convert the schedule grid back to lesson objects\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson) {\n                weeklyLessons.push(lesson);\n            }\n        }\n    }\n    console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (target: ${totalSlotsAvailable})`);\n    // Log final subject distribution\n    const finalCounts = {};\n    weeklyLessons.forEach((lesson)=>{\n        if (lesson.subjectId && lesson.subjectId !== 'free_period') {\n            finalCounts[lesson.subjectId] = (finalCounts[lesson.subjectId] || 0) + 1;\n        }\n    });\n    console.log(`[Timetable API] Final lesson distribution:`, finalCounts);\n    return weeklyLessons;\n}\n// New optimized scheduling function that ensures all lessons are scheduled\nfunction scheduleAllLessonsOptimally(enrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts) {\n    // Create a list of all lessons that need to be scheduled (without lesson numbers initially)\n    const lessonsToSchedule = [];\n    enrollments.forEach((enrollment)=>{\n        for(let i = 1; i <= enrollment.lessonsPerWeek; i++){\n            lessonsToSchedule.push({\n                enrollment\n            });\n        }\n    });\n    console.log(`[Timetable API] Total lessons to schedule: ${lessonsToSchedule.length}`);\n    // Sort lessons by cognitive load (highest first)\n    lessonsToSchedule.sort((a, b)=>{\n        return (b.enrollment.cognitiveLoad || 5) - (a.enrollment.cognitiveLoad || 5);\n    });\n    // Track lessons per day for each subject to enforce distribution rules\n    const subjectDailyCount = {};\n    enrollments.forEach((enrollment)=>{\n        subjectDailyCount[enrollment.subjectId] = new Array(DAYS_OF_WEEK.length).fill(0);\n    });\n    // Schedule each lesson (without assigning lesson numbers yet)\n    for (const lessonToSchedule of lessonsToSchedule){\n        const { enrollment } = lessonToSchedule;\n        let scheduled = false;\n        // Get preferred time slots based on cognitive load\n        const preferredTimeSlots = getPreferredTimeSlots(enrollment.cognitiveLoad || 5);\n        // Try to schedule on each day, prioritizing days with fewer lessons for this subject\n        const dayPriority = Array.from({\n            length: DAYS_OF_WEEK.length\n        }, (_, i)=>i).sort((a, b)=>subjectDailyCount[enrollment.subjectId][a] - subjectDailyCount[enrollment.subjectId][b]);\n        for (const dayIndex of dayPriority){\n            // Special rule for Mathematics: allow up to 2 lessons on Monday, 1 on other days\n            const maxLessonsPerDay = enrollment.subjectId === 'mathematics' && dayIndex === 0 ? 2 : enrollment.lessonsPerWeek >= 6 ? 2 : 1;\n            if (subjectDailyCount[enrollment.subjectId][dayIndex] >= maxLessonsPerDay) {\n                continue; // Skip this day if we've reached the limit\n            }\n            // Try preferred time slots first\n            for (const timeIndex of preferredTimeSlots){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    // Check if this would create back-to-back lessons (avoid if possible)\n                    const hasAdjacentLesson = timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId || timeIndex < STANDARD_TIME_SLOTS.length - 1 && weekSchedule[dayIndex][timeIndex + 1]?.subjectId === enrollment.subjectId;\n                    // For subjects with multiple lessons per week, allow adjacent lessons if necessary\n                    if (!hasAdjacentLesson || enrollment.lessonsPerWeek >= 4) {\n                        subjectLessonCounts[enrollment.subjectId]++;\n                        subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                        // Create lesson object with placeholder lesson number (will be fixed later)\n                        const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);\n                        weekSchedule[dayIndex][timeIndex] = lesson;\n                        scheduled = true;\n                        break;\n                    }\n                }\n            }\n            if (scheduled) break;\n            // If no preferred slot worked, try any available slot on this day\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                    // Create lesson object with placeholder lesson number (will be fixed later)\n                    const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    scheduled = true;\n                    break;\n                }\n            }\n            if (scheduled) break;\n        }\n        if (!scheduled) {\n            console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} lesson - no available slots`);\n        }\n    }\n    // Now fix lesson numbering to be chronological\n    fixLessonNumberingChronologically(weekSchedule, academicWeek, studentGrade);\n    // Log scheduling results\n    console.log(`[Timetable API] Scheduled lessons and fixed chronological numbering`);\n}\n// Fix lesson numbering to follow chronological order (day and time based)\nfunction fixLessonNumberingChronologically(weekSchedule, academicWeek, studentGrade) {\n    // Group lessons by subject\n    const subjectLessons = {};\n    // Collect all lessons for each subject\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson && lesson.subjectId && lesson.subjectId !== 'free_period') {\n                if (!subjectLessons[lesson.subjectId]) {\n                    subjectLessons[lesson.subjectId] = [];\n                }\n                subjectLessons[lesson.subjectId].push({\n                    lesson,\n                    dayIndex,\n                    timeIndex,\n                    position: dayIndex * STANDARD_TIME_SLOTS.length + timeIndex // For sorting\n                });\n            }\n        }\n    }\n    // For each subject, sort lessons chronologically and reassign lesson numbers\n    Object.entries(subjectLessons).forEach(([subjectId, lessons])=>{\n        // Sort by chronological position (day first, then time)\n        lessons.sort((a, b)=>a.position - b.position);\n        // Reassign lesson numbers chronologically\n        lessons.forEach((lessonData, index)=>{\n            const { lesson, dayIndex, timeIndex } = lessonData;\n            const newLessonNumber = index + 1;\n            // Update the lesson object with correct numbering\n            const enrollment = {\n                subjectId: lesson.subjectId,\n                subjectName: lesson.subject,\n                subjectCode: lesson.subjectCode,\n                lessonsPerWeek: lessons.length,\n                cognitiveLoad: lesson.cognitiveLoad,\n                status: 'active',\n                enrolledAt: lesson.enrolledAt || new Date()\n            };\n            const updatedLesson = createLessonObject(enrollment, newLessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n            // Replace the lesson in the schedule\n            weekSchedule[dayIndex][timeIndex] = updatedLesson;\n        });\n    });\n    console.log(`[Timetable API] Fixed lesson numbering for ${Object.keys(subjectLessons).length} subjects`);\n}\n// Get preferred time slots based on cognitive load\nfunction getPreferredTimeSlots(cognitiveLoad) {\n    if (cognitiveLoad >= 8) {\n        // High cognitive load: prefer early morning slots\n        return [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 6) {\n        // Medium cognitive load: prefer mid-morning slots\n        return [\n            1,\n            2,\n            3,\n            0,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 4) {\n        // Low-medium cognitive load: prefer afternoon slots\n        return [\n            3,\n            4,\n            5,\n            2,\n            6,\n            1,\n            0\n        ];\n    } else {\n        // Low cognitive load: prefer late slots\n        return [\n            5,\n            6,\n            4,\n            3,\n            2,\n            1,\n            0\n        ];\n    }\n}\n// Create a lesson object\nfunction createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade) {\n    const lessonReference = generateLessonReference(studentGrade, enrollment.subjectCode, academicWeek, lessonNumber, enrollment.lessonsPerWeek);\n    // Default all lessons to \"upcoming\" status\n    // Actual completion status will be applied later from Firestore data\n    const status = 'upcoming';\n    return {\n        id: `${lessonReference}_week${academicWeek}`,\n        lessonReference: lessonReference,\n        title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,\n        subject: enrollment.subjectName,\n        subjectId: enrollment.subjectId,\n        subjectCode: enrollment.subjectCode,\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        day: DAYS_OF_WEEK[dayIndex],\n        duration: 45,\n        status: status,\n        description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,\n        grade: studentGrade,\n        academicWeek: academicWeek,\n        lessonNumberInWeek: lessonNumber,\n        absoluteLessonNumber: (academicWeek - 1) * enrollment.lessonsPerWeek + lessonNumber,\n        totalWeeks: 30,\n        teacher: 'AI Instructor',\n        cognitiveLoad: enrollment.cognitiveLoad,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n}\n// Apply lesson completion states from Firestore to the generated lessons\nfunction applyLessonCompletionStates(lessons, completionStates) {\n    console.log(`[Timetable API] Applying completion states to ${lessons.length} lessons`);\n    const updatedLessons = lessons.map((lesson)=>{\n        if (!lesson.lessonReference) {\n            return lesson; // Skip lessons without lesson references (e.g., free periods)\n        }\n        // Check if this lesson has completion data\n        const completionData = completionStates[lesson.lessonReference];\n        if (completionData) {\n            console.log(`[Timetable API] Found completion data for ${lesson.lessonReference}:`, completionData);\n            // Update lesson with actual completion status\n            return {\n                ...lesson,\n                status: completionData.status || lesson.status,\n                completed: completionData.completed || false,\n                completedAt: completionData.completedAt,\n                progress: completionData.progress || 0,\n                // Preserve any additional completion metadata\n                completionData: completionData\n            };\n        }\n        // No completion data found, keep as \"upcoming\"\n        return lesson;\n    });\n    const completedCount = updatedLessons.filter((l)=>l.status === 'completed').length;\n    const inProgressCount = updatedLessons.filter((l)=>l.status === 'in_progress').length;\n    const upcomingCount = updatedLessons.filter((l)=>l.status === 'upcoming').length;\n    console.log(`[Timetable API] Lesson status summary: ${completedCount} completed, ${inProgressCount} in progress, ${upcomingCount} upcoming`);\n    return updatedLessons;\n}\n// Get student grade level\nasync function getStudentGrade(studentId) {\n    try {\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        for (const format of studentIdFormats){\n            const studentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const studentSnap = await studentRef.get();\n            if (studentSnap.exists) {\n                const studentData = studentSnap.data();\n                return studentData?.gradeLevel || studentData?.grade || 'P5';\n            }\n        }\n        return 'P5'; // Default grade\n    } catch (error) {\n        console.error('[Timetable API] Error fetching student grade:', error);\n        return 'P5';\n    }\n}\n// Fetch lesson completion states from Firestore\nasync function fetchLessonCompletionStates(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching lesson completion states for student: ${studentId}`);\n        // Try different student ID formats for lesson_states collection\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        const completionStates = {};\n        for (const format of studentIdFormats){\n            try {\n                const lessonStatesRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('lesson_states').doc(format);\n                const lessonStatesSnap = await lessonStatesRef.get();\n                if (lessonStatesSnap.exists) {\n                    const statesData = lessonStatesSnap.data();\n                    console.log(`[Timetable API] Found lesson states for student ID format: ${format}`);\n                    // Extract lesson completion data\n                    if (statesData) {\n                        Object.entries(statesData).forEach(([lessonRef, lessonData])=>{\n                            if (lessonData && typeof lessonData === 'object') {\n                                // Store the completion state with lesson reference as key\n                                completionStates[lessonRef] = {\n                                    status: lessonData.status || lessonData.completion_status || 'upcoming',\n                                    completed: lessonData.completed || false,\n                                    completedAt: lessonData.completedAt || lessonData.completed_at,\n                                    progress: lessonData.progress || 0,\n                                    ...lessonData\n                                };\n                            }\n                        });\n                    }\n                    break; // Found data, no need to try other formats\n                }\n            } catch (formatError) {\n                console.warn(`[Timetable API] Error checking lesson states for format ${format}:`, formatError);\n                continue;\n            }\n        }\n        console.log(`[Timetable API] Found ${Object.keys(completionStates).length} lesson completion records`);\n        return completionStates;\n    } catch (error) {\n        console.error('[Timetable API] Error fetching lesson completion states:', error);\n        return {};\n    }\n}\n// Verify Firebase Auth token\nasync function verifyAuthToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader?.startsWith('Bearer ')) {\n            return null;\n        }\n        const idToken = authHeader.split('Bearer ')[1];\n        const decodedToken = await (0,firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)().verifyIdToken(idToken);\n        return {\n            uid: decodedToken.uid\n        };\n    } catch (error) {\n        console.error('Auth verification failed:', error);\n        return null;\n    }\n}\n// Fill any remaining empty slots with \"Free Period\"\nfunction fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade) {\n    const abbreviatedGrade = convertGradeToAbbreviation(studentGrade);\n    let freePeriodCounter = 1;\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                const freePeriod = {\n                    id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,\n                    lessonReference: `${abbreviatedGrade}-${String(academicWeek).padStart(3, '0')}-${String(freePeriodCounter).padStart(3, '0')}`,\n                    title: 'Free Period',\n                    subject: 'Free Period',\n                    subjectId: 'free_period',\n                    subjectCode: null,\n                    time: STANDARD_TIME_SLOTS[timeIndex],\n                    day: DAYS_OF_WEEK[dayIndex],\n                    duration: 45,\n                    status: 'upcoming',\n                    description: 'Free study period',\n                    grade: studentGrade,\n                    academicWeek: academicWeek,\n                    lessonNumberInWeek: freePeriodCounter,\n                    absoluteLessonNumber: null,\n                    totalWeeks: 30,\n                    teacher: null,\n                    cognitiveLoad: 1,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isFreePeriod: true\n                };\n                weekSchedule[dayIndex][timeIndex] = freePeriod;\n                freePeriodCounter++;\n            }\n        }\n    }\n}\nasync function GET(request) {\n    try {\n        // Check if Firebase Admin is properly initialized\n        if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db) {\n            console.error('[Timetable API] Firebase Admin not properly initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Database not available'\n            }, {\n                status: 500\n            });\n        }\n        // For development/testing, allow unauthenticated requests\n        // In production, uncomment the authentication check below\n        /*\r\n    const authResult = await verifyAuthToken(request);\r\n    if (!authResult) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Unauthorized' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n    */ const { searchParams } = new URL(request.url);\n        const studentId = searchParams.get('studentId');\n        const date = searchParams.get('date');\n        const weekParam = searchParams.get('week');\n        // Parse week parameter (default to week 1)\n        const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;\n        if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid week number. Must be between 1 and 30.'\n            }, {\n                status: 400\n            });\n        }\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);\n        // Fetch student grade level\n        const studentGrade = await getStudentGrade(studentId);\n        console.log(`[Timetable API] Student grade: ${studentGrade}`);\n        // Fetch student enrollments (this is now the single source of truth)\n        const enrollments = await fetchStudentEnrollments(studentId);\n        if (enrollments.length === 0) {\n            console.log('[Timetable API] No active enrollments found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    schedule: [],\n                    timetable: [],\n                    totalLessons: 0,\n                    enrollments: [],\n                    student_id: studentId,\n                    academic_week: academicWeek,\n                    total_weeks: 30,\n                    student_grade: studentGrade,\n                    date: date\n                }\n            });\n        }\n        // Generate weekly timetable based on enrollments\n        const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade);\n        // Fetch lesson completion states from Firestore\n        const completionStates = await fetchLessonCompletionStates(studentId);\n        // Apply actual completion states to the generated lessons\n        const scheduleWithCompletionStates = applyLessonCompletionStates(weeklySchedule, completionStates);\n        // Sort weekly schedule by day and time\n        const dayOrder = [\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday'\n        ];\n        scheduleWithCompletionStates.sort((a, b)=>{\n            const dayA = dayOrder.indexOf(a.day);\n            const dayB = dayOrder.indexOf(b.day);\n            if (dayA !== dayB) {\n                return dayA - dayB;\n            }\n            // Sort by time within the same day\n            if (a.time && b.time) {\n                return a.time.localeCompare(b.time);\n            }\n            return 0;\n        });\n        console.log(`[Timetable API] Returning ${scheduleWithCompletionStates.length} lessons for week ${academicWeek}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                schedule: scheduleWithCompletionStates,\n                timetable: scheduleWithCompletionStates,\n                totalLessons: scheduleWithCompletionStates.length,\n                enrollments: enrollments,\n                student_id: studentId,\n                academic_week: academicWeek,\n                total_weeks: 30,\n                student_grade: studentGrade,\n                date: date\n            }\n        });\n    } catch (error) {\n        console.error('[Timetable API] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch timetable',\n            details: error instanceof Error ? error.stack : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS90aW1ldGFibGUvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RDtBQUNkO0FBRUk7QUFFOUMsMERBQTBEO0FBQzFELE1BQU1HLHNCQUFzQjtJQUMxQjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtDQUNEO0FBRUQsTUFBTUMsZUFBZTtJQUFDO0lBQVU7SUFBVztJQUFhO0lBQVk7Q0FBUztBQWE3RSw0RUFBNEU7QUFDNUUsTUFBTUMsMEJBQWtEO0lBQ3RELGVBQWU7SUFDZixvQkFBb0I7SUFDcEIsZ0NBQWdDO0lBQ2hDLGlCQUFpQjtJQUNqQixrQkFBa0I7SUFDbEIsb0JBQW9CO0lBQ3BCLGFBQWE7SUFDYiwyQkFBMkI7SUFDM0IsVUFBVTtJQUNWLGlDQUFpQztJQUNqQyw2QkFBNkI7SUFDN0IsOEJBQThCO0lBQzlCLG9CQUFvQjtJQUNwQixzQkFBc0I7SUFDdEIsaUJBQWlCO0lBQ2pCLDhCQUE4QjtJQUM5QixrQkFBa0I7SUFDbEIsNkJBQTZCO0lBQzdCLDRCQUE0QjtBQUM5QjtBQUVBLDZEQUE2RDtBQUM3RCxTQUFTQyx3QkFDUEMsS0FBYSxFQUNiQyxXQUFtQixFQUNuQkMsWUFBb0IsRUFDcEJDLFlBQW9CLEVBQ3BCQyxjQUFzQjtJQUV0Qix3REFBd0Q7SUFDeEQsTUFBTUMsdUJBQXVCLENBQUVILGVBQWUsS0FBS0UsaUJBQWtCRDtJQUVyRSxpRUFBaUU7SUFDakUsTUFBTUcsd0JBQXdCRCxxQkFBcUJFLFFBQVEsR0FBR0MsUUFBUSxDQUFDLEdBQUc7SUFFMUUsNkNBQTZDO0lBQzdDLE1BQU1DLG1CQUFtQkMsMkJBQTJCVjtJQUVwRCxPQUFPLEdBQUdTLGlCQUFpQixDQUFDLEVBQUVSLFlBQVksQ0FBQyxFQUFFSyx1QkFBdUI7QUFDdEU7QUFFQSxtREFBbUQ7QUFDbkQsU0FBU0ksMkJBQTJCVixLQUFhO0lBQy9DLGlDQUFpQztJQUNqQyxNQUFNVyxXQUFXWCxNQUFNWSxXQUFXLEdBQUdDLElBQUk7SUFFekMsaUJBQWlCO0lBQ2pCLElBQUlGLFNBQVNHLFFBQVEsQ0FBQyxjQUFjSCxTQUFTSSxVQUFVLENBQUMsTUFBTTtRQUM1RCxNQUFNQyxRQUFRTCxTQUFTSyxLQUFLLENBQUM7UUFDN0IsSUFBSUEsT0FBTztZQUNULE9BQU8sQ0FBQyxDQUFDLEVBQUVBLEtBQUssQ0FBQyxFQUFFLEVBQUU7UUFDdkI7SUFDRjtJQUVBLG1CQUFtQjtJQUNuQixJQUFJTCxTQUFTRyxRQUFRLENBQUMsYUFBYUgsU0FBU0csUUFBUSxDQUFDLFFBQVE7UUFDM0QsTUFBTUUsUUFBUUwsU0FBU0ssS0FBSyxDQUFDO1FBQzdCLElBQUlBLE9BQU87WUFDVCxPQUFPLENBQUMsR0FBRyxFQUFFQSxLQUFLLENBQUMsRUFBRSxFQUFFO1FBQ3pCO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsSUFBSUwsU0FBU0csUUFBUSxDQUFDLGFBQWFILFNBQVNHLFFBQVEsQ0FBQyxRQUFRO1FBQzNELE1BQU1FLFFBQVFMLFNBQVNLLEtBQUssQ0FBQztRQUM3QixJQUFJQSxPQUFPO1lBQ1QsT0FBTyxDQUFDLEdBQUcsRUFBRUEsS0FBSyxDQUFDLEVBQUUsRUFBRTtRQUN6QjtJQUNGO0lBRUEsNkNBQTZDO0lBQzdDLElBQUksb0JBQW9CQyxJQUFJLENBQUNqQixRQUFRO1FBQ25DLE9BQU9BLE1BQU1rQixXQUFXO0lBQzFCO0lBRUEsbUJBQW1CO0lBQ25CLE9BQU9sQjtBQUNUO0FBRUEsMkNBQTJDO0FBQzNDLGVBQWVtQix3QkFBd0JDLFNBQWlCO0lBQ3RELElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDLENBQUMsa0RBQWtELEVBQUVGLFdBQVc7UUFFNUUsbUNBQW1DO1FBQ25DLE1BQU1HLG1CQUFtQjtZQUN2Qkg7WUFDQSxDQUFDLGtCQUFrQixDQUFDO1lBQ3BCQSxVQUFVUixXQUFXO1lBQ3JCUSxVQUFVSSxPQUFPLENBQUMsUUFBUSxLQUFLWixXQUFXO1NBQzNDO1FBRUQsSUFBSWEsY0FBNEIsRUFBRTtRQUNsQyxJQUFJQyxhQUFrQjtRQUV0QixLQUFLLE1BQU1DLFVBQVVKLGlCQUFrQjtZQUNyQ0YsUUFBUUMsR0FBRyxDQUFDLENBQUMsMENBQTBDLEVBQUVLLFFBQVE7WUFFakUsTUFBTUMsaUJBQWlCbEMsbURBQUVBLENBQUVtQyxVQUFVLENBQUMsWUFBWUMsR0FBRyxDQUFDSDtZQUN0RCxNQUFNSSxrQkFBa0IsTUFBTUgsZUFBZUksR0FBRztZQUVoRCxJQUFJRCxnQkFBZ0JFLE1BQU0sRUFBRTtnQkFDMUJaLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGdEQUFnRCxFQUFFSyxRQUFRO2dCQUN2RUQsYUFBYUU7Z0JBQ2I7WUFDRjtRQUNGO1FBRUEsSUFBSSxDQUFDRixZQUFZO1lBQ2ZMLFFBQVFhLElBQUksQ0FBQyxDQUFDLDJEQUEyRCxDQUFDO1lBQzFFLE9BQU8sRUFBRTtRQUNYO1FBRUEsdURBQXVEO1FBQ3ZELE1BQU1DLGlCQUFpQlQsV0FBV0csVUFBVSxDQUFDO1FBQzdDLE1BQU1PLGtCQUFrQixNQUFNRCxlQUFlRSxLQUFLLENBQUMsVUFBVSxNQUFNLFVBQVVMLEdBQUc7UUFFaEZYLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFYyxnQkFBZ0JFLElBQUksQ0FBQyxtQkFBbUIsQ0FBQztRQUU5RUYsZ0JBQWdCRyxJQUFJLENBQUNDLE9BQU8sQ0FBQ1YsQ0FBQUE7WUFDM0IsTUFBTVcsT0FBT1gsSUFBSVcsSUFBSTtZQUNyQixNQUFNQyxZQUFZWixJQUFJYSxFQUFFO1lBQ3hCbEIsWUFBWW1CLElBQUksQ0FBQztnQkFDZkYsV0FBV0E7Z0JBQ1hHLGFBQWFKLEtBQUtJLFdBQVcsSUFBSUosS0FBS0ssWUFBWSxJQUFJaEIsSUFBSWEsRUFBRSxDQUFDbkIsT0FBTyxDQUFDLE1BQU0sS0FBS0EsT0FBTyxDQUFDLFNBQVN1QixDQUFBQSxJQUFLQSxFQUFFN0IsV0FBVztnQkFDbkhqQixhQUFhd0MsS0FBS3hDLFdBQVcsSUFBSXdDLEtBQUtPLFlBQVksSUFBSUMscUJBQXFCbkIsSUFBSWEsRUFBRTtnQkFDakZ2QyxnQkFBZ0JxQyxLQUFLckMsY0FBYyxJQUFJcUMsS0FBS1MsZ0JBQWdCLElBQUk7Z0JBQ2hFQyxRQUFRVixLQUFLVSxNQUFNO2dCQUNuQkMsWUFBWVgsS0FBS1csVUFBVSxJQUFJWCxLQUFLWSxXQUFXO2dCQUMvQ0MsZUFBZWIsS0FBS2EsYUFBYSxJQUFJeEQsdUJBQXVCLENBQUM0QyxVQUFVLElBQUksRUFBRSxtQ0FBbUM7WUFDbEg7UUFDRjtRQUVBckIsUUFBUUMsR0FBRyxDQUFDLENBQUMsc0NBQXNDLENBQUMsRUFBRUc7UUFDdEQsT0FBT0E7SUFFVCxFQUFFLE9BQU84QixPQUFPO1FBQ2RsQyxRQUFRa0MsS0FBSyxDQUFDLENBQUMsMkNBQTJDLENBQUMsRUFBRUE7UUFDN0QsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBLG1DQUFtQztBQUNuQyxTQUFTTixxQkFBcUJQLFNBQWlCO0lBQzdDLHdGQUF3RjtJQUN4RixNQUFNYyxlQUFlZCxVQUFVOUIsV0FBVyxHQUN2Q1ksT0FBTyxDQUFDLFdBQVcsS0FDbkJBLE9BQU8sQ0FBQyxlQUFlLEtBQUssc0NBQXNDO0lBRXJFLE1BQU1pQyxVQUFrQztRQUN0QyxlQUFlO1FBQ2Ysb0JBQW9CO1FBQ3BCLGdDQUFnQztRQUNoQyxpQkFBaUI7UUFDakIsa0JBQWtCO1FBQ2xCLG9CQUFvQjtRQUNwQixhQUFhO1FBQ2IsaUJBQWlCO1FBQ2pCLDhCQUE4QjtRQUM5QixrQkFBa0I7UUFDbEIsNkJBQTZCO1FBQzdCLDZCQUE2QjtRQUM3Qiw4QkFBOEI7UUFDOUIsb0JBQW9CO1FBQ3BCLHNCQUFzQjtRQUN0QixVQUFVO1FBQ1YsMkJBQTJCO1FBQzNCLDRCQUE0QjtRQUM1Qiw0QkFBNEI7UUFDNUIsaUNBQWlDO0lBQ25DO0lBRUEsOEJBQThCO0lBQzlCLElBQUlBLE9BQU8sQ0FBQ0QsYUFBYSxFQUFFO1FBQ3pCLE9BQU9DLE9BQU8sQ0FBQ0QsYUFBYTtJQUM5QjtJQUVBLGtDQUFrQztJQUNsQyxJQUFJQyxPQUFPLENBQUNmLFVBQVU5QixXQUFXLEdBQUcsRUFBRTtRQUNwQyxPQUFPNkMsT0FBTyxDQUFDZixVQUFVOUIsV0FBVyxHQUFHO0lBQ3pDO0lBRUEseURBQXlEO0lBQ3pELElBQUk4QixVQUFVOUIsV0FBVyxHQUFHRSxRQUFRLENBQUMsY0FBYzRCLFVBQVU5QixXQUFXLEdBQUdFLFFBQVEsQ0FBQyxlQUFlO1FBQ2pHLE9BQU87SUFDVDtJQUVBLE9BQU87QUFDVDtBQUVBLGtGQUFrRjtBQUNsRixTQUFTNEMsNEJBQTRCakMsV0FBeUIsRUFBRXZCLFlBQW9CLEVBQUV5RCxZQUFvQjtJQUN4RyxNQUFNQyxnQkFBdUIsRUFBRTtJQUUvQnZDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlDQUF5QyxFQUFFRyxZQUFZb0MsTUFBTSxDQUFDLHlCQUF5QixDQUFDO0lBRXJHLGlEQUFpRDtJQUNqRCxNQUFNQyxlQUFpQ2pFLGFBQWFrRSxHQUFHLENBQUMsSUFBTSxJQUFJQyxNQUFNcEUsb0JBQW9CaUUsTUFBTSxFQUFFSSxJQUFJLENBQUM7SUFFekcsaUNBQWlDO0lBQ2pDLE1BQU1DLHFCQUFxQnpDLFlBQVkwQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsSUFBTUQsTUFBTUMsRUFBRWpFLGNBQWMsRUFBRTtJQUNsRixNQUFNa0Usc0JBQXNCekUsYUFBYWdFLE1BQU0sR0FBR2pFLG9CQUFvQmlFLE1BQU0sRUFBRSx3QkFBd0I7SUFFdEd4QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQ0FBc0MsRUFBRTRDLG1CQUFtQix5QkFBeUIsRUFBRUkscUJBQXFCO0lBRXhILHNGQUFzRjtJQUN0RixNQUFNQyxvQkFBb0I7V0FBSTlDO0tBQVksQ0FBQytDLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNLENBQUNBLEVBQUVwQixhQUFhLElBQUksS0FBTW1CLENBQUFBLEVBQUVuQixhQUFhLElBQUk7SUFFdkdqQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxrREFBa0QsQ0FBQyxFQUM5RGlELGtCQUFrQlIsR0FBRyxDQUFDTSxDQUFBQSxJQUFLLEdBQUdBLEVBQUV4QixXQUFXLENBQUMsRUFBRSxFQUFFd0IsRUFBRWYsYUFBYSxDQUFDLElBQUksRUFBRWUsRUFBRWpFLGNBQWMsQ0FBQyxRQUFRLENBQUM7SUFFbEcsd0RBQXdEO0lBQ3hELE1BQU11RSxzQkFBOEMsQ0FBQztJQUVyRCwwQkFBMEI7SUFDMUJKLGtCQUFrQi9CLE9BQU8sQ0FBQ29DLENBQUFBO1FBQ3hCRCxtQkFBbUIsQ0FBQ0MsV0FBV2xDLFNBQVMsQ0FBQyxHQUFHO0lBQzlDO0lBRUEsd0RBQXdEO0lBQ3hEbUMsNEJBQTRCTixtQkFBbUJULGNBQWM1RCxjQUFjeUQsY0FBY2dCO0lBRXpGLG9EQUFvRDtJQUNwREcsOEJBQThCaEIsY0FBYzVELGNBQWN5RDtJQUUxRCxtREFBbUQ7SUFDbkQsSUFBSyxJQUFJb0IsV0FBVyxHQUFHQSxXQUFXbEYsYUFBYWdFLE1BQU0sRUFBRWtCLFdBQVk7UUFDakUsSUFBSyxJQUFJQyxZQUFZLEdBQUdBLFlBQVlwRixvQkFBb0JpRSxNQUFNLEVBQUVtQixZQUFhO1lBQzNFLE1BQU1DLFNBQVNuQixZQUFZLENBQUNpQixTQUFTLENBQUNDLFVBQVU7WUFDaEQsSUFBSUMsUUFBUTtnQkFDVnJCLGNBQWNoQixJQUFJLENBQUNxQztZQUNyQjtRQUNGO0lBQ0Y7SUFFQTVELFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFc0MsY0FBY0MsTUFBTSxDQUFDLCtCQUErQixFQUFFUyxvQkFBb0IsQ0FBQyxDQUFDO0lBRXJILGlDQUFpQztJQUNqQyxNQUFNWSxjQUFzQyxDQUFDO0lBQzdDdEIsY0FBY3BCLE9BQU8sQ0FBQ3lDLENBQUFBO1FBQ3BCLElBQUlBLE9BQU92QyxTQUFTLElBQUl1QyxPQUFPdkMsU0FBUyxLQUFLLGVBQWU7WUFDMUR3QyxXQUFXLENBQUNELE9BQU92QyxTQUFTLENBQUMsR0FBRyxDQUFDd0MsV0FBVyxDQUFDRCxPQUFPdkMsU0FBUyxDQUFDLElBQUksS0FBSztRQUN6RTtJQUNGO0lBRUFyQixRQUFRQyxHQUFHLENBQUMsQ0FBQywwQ0FBMEMsQ0FBQyxFQUFFNEQ7SUFFMUQsT0FBT3RCO0FBQ1Q7QUFFQSwyRUFBMkU7QUFDM0UsU0FBU2lCLDRCQUNQcEQsV0FBeUIsRUFDekJxQyxZQUE4QixFQUM5QjVELFlBQW9CLEVBQ3BCeUQsWUFBb0IsRUFDcEJnQixtQkFBMkM7SUFFM0MsNEZBQTRGO0lBQzVGLE1BQU1RLG9CQUFrRCxFQUFFO0lBRTFEMUQsWUFBWWUsT0FBTyxDQUFDb0MsQ0FBQUE7UUFDbEIsSUFBSyxJQUFJUSxJQUFJLEdBQUdBLEtBQUtSLFdBQVd4RSxjQUFjLEVBQUVnRixJQUFLO1lBQ25ERCxrQkFBa0J2QyxJQUFJLENBQUM7Z0JBQUVnQztZQUFXO1FBQ3RDO0lBQ0Y7SUFFQXZELFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJDQUEyQyxFQUFFNkQsa0JBQWtCdEIsTUFBTSxFQUFFO0lBRXBGLGlEQUFpRDtJQUNqRHNCLGtCQUFrQlgsSUFBSSxDQUFDLENBQUNDLEdBQUdDO1FBQ3pCLE9BQU8sQ0FBQ0EsRUFBRUUsVUFBVSxDQUFDdEIsYUFBYSxJQUFJLEtBQU1tQixDQUFBQSxFQUFFRyxVQUFVLENBQUN0QixhQUFhLElBQUk7SUFDNUU7SUFFQSx1RUFBdUU7SUFDdkUsTUFBTStCLG9CQUE4QyxDQUFDO0lBQ3JENUQsWUFBWWUsT0FBTyxDQUFDb0MsQ0FBQUE7UUFDbEJTLGlCQUFpQixDQUFDVCxXQUFXbEMsU0FBUyxDQUFDLEdBQUcsSUFBSXNCLE1BQU1uRSxhQUFhZ0UsTUFBTSxFQUFFSSxJQUFJLENBQUM7SUFDaEY7SUFFQSw4REFBOEQ7SUFDOUQsS0FBSyxNQUFNcUIsb0JBQW9CSCxrQkFBbUI7UUFDaEQsTUFBTSxFQUFFUCxVQUFVLEVBQUUsR0FBR1U7UUFDdkIsSUFBSUMsWUFBWTtRQUVoQixtREFBbUQ7UUFDbkQsTUFBTUMscUJBQXFCQyxzQkFBc0JiLFdBQVd0QixhQUFhLElBQUk7UUFFN0UscUZBQXFGO1FBQ3JGLE1BQU1vQyxjQUFjMUIsTUFBTTJCLElBQUksQ0FBQztZQUFFOUIsUUFBUWhFLGFBQWFnRSxNQUFNO1FBQUMsR0FBRyxDQUFDK0IsR0FBR1IsSUFBTUEsR0FDdkVaLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNVyxpQkFBaUIsQ0FBQ1QsV0FBV2xDLFNBQVMsQ0FBQyxDQUFDK0IsRUFBRSxHQUFHWSxpQkFBaUIsQ0FBQ1QsV0FBV2xDLFNBQVMsQ0FBQyxDQUFDZ0MsRUFBRTtRQUV6RyxLQUFLLE1BQU1LLFlBQVlXLFlBQWE7WUFDbEMsaUZBQWlGO1lBQ2pGLE1BQU1HLG1CQUFtQixXQUFZbkQsU0FBUyxLQUFLLGlCQUFpQnFDLGFBQWEsSUFBSyxJQUM3RCxXQUFZM0UsY0FBYyxJQUFJLElBQUssSUFBSTtZQUVoRSxJQUFJaUYsaUJBQWlCLENBQUNULFdBQVdsQyxTQUFTLENBQUMsQ0FBQ3FDLFNBQVMsSUFBSWMsa0JBQWtCO2dCQUN6RSxVQUFVLDJDQUEyQztZQUN2RDtZQUVBLGlDQUFpQztZQUNqQyxLQUFLLE1BQU1iLGFBQWFRLG1CQUFvQjtnQkFDMUMsSUFBSTFCLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsVUFBVSxLQUFLLE1BQU07b0JBQzlDLHNFQUFzRTtvQkFDdEUsTUFBTWMsb0JBQW9CLFlBQWEsS0FBS2hDLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsWUFBWSxFQUFFLEVBQUV0QyxjQUFja0MsV0FBV2xDLFNBQVMsSUFDM0ZzQyxZQUFZcEYsb0JBQW9CaUUsTUFBTSxHQUFHLEtBQUtDLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsWUFBWSxFQUFFLEVBQUV0QyxjQUFja0MsV0FBV2xDLFNBQVM7b0JBRWpKLG1GQUFtRjtvQkFDbkYsSUFBSSxDQUFDb0QscUJBQXFCbEIsV0FBV3hFLGNBQWMsSUFBSSxHQUFHO3dCQUN4RHVFLG1CQUFtQixDQUFDQyxXQUFXbEMsU0FBUyxDQUFDO3dCQUN6QzJDLGlCQUFpQixDQUFDVCxXQUFXbEMsU0FBUyxDQUFDLENBQUNxQyxTQUFTO3dCQUVqRCw0RUFBNEU7d0JBQzVFLE1BQU1FLFNBQVNjLG1CQUFtQm5CLFlBQVksR0FBR0csVUFBVUMsV0FBVzlFLGNBQWN5RDt3QkFDcEZHLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsVUFBVSxHQUFHQzt3QkFDcENNLFlBQVk7d0JBQ1o7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlBLFdBQVc7WUFFZixrRUFBa0U7WUFDbEUsSUFBSyxJQUFJUCxZQUFZLEdBQUdBLFlBQVlwRixvQkFBb0JpRSxNQUFNLEVBQUVtQixZQUFhO2dCQUMzRSxJQUFJbEIsWUFBWSxDQUFDaUIsU0FBUyxDQUFDQyxVQUFVLEtBQUssTUFBTTtvQkFDOUNMLG1CQUFtQixDQUFDQyxXQUFXbEMsU0FBUyxDQUFDO29CQUN6QzJDLGlCQUFpQixDQUFDVCxXQUFXbEMsU0FBUyxDQUFDLENBQUNxQyxTQUFTO29CQUVqRCw0RUFBNEU7b0JBQzVFLE1BQU1FLFNBQVNjLG1CQUFtQm5CLFlBQVksR0FBR0csVUFBVUMsV0FBVzlFLGNBQWN5RDtvQkFDcEZHLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsVUFBVSxHQUFHQztvQkFDcENNLFlBQVk7b0JBQ1o7Z0JBQ0Y7WUFDRjtZQUVBLElBQUlBLFdBQVc7UUFDakI7UUFFQSxJQUFJLENBQUNBLFdBQVc7WUFDZGxFLFFBQVFhLElBQUksQ0FBQyxDQUFDLG1DQUFtQyxFQUFFMEMsV0FBVy9CLFdBQVcsQ0FBQyw0QkFBNEIsQ0FBQztRQUN6RztJQUNGO0lBRUEsK0NBQStDO0lBQy9DbUQsa0NBQWtDbEMsY0FBYzVELGNBQWN5RDtJQUU5RCx5QkFBeUI7SUFDekJ0QyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxtRUFBbUUsQ0FBQztBQUNuRjtBQUVBLDBFQUEwRTtBQUMxRSxTQUFTMEUsa0NBQ1BsQyxZQUE4QixFQUM5QjVELFlBQW9CLEVBQ3BCeUQsWUFBb0I7SUFFcEIsMkJBQTJCO0lBQzNCLE1BQU1zQyxpQkFBd0MsQ0FBQztJQUUvQyx1Q0FBdUM7SUFDdkMsSUFBSyxJQUFJbEIsV0FBVyxHQUFHQSxXQUFXbEYsYUFBYWdFLE1BQU0sRUFBRWtCLFdBQVk7UUFDakUsSUFBSyxJQUFJQyxZQUFZLEdBQUdBLFlBQVlwRixvQkFBb0JpRSxNQUFNLEVBQUVtQixZQUFhO1lBQzNFLE1BQU1DLFNBQVNuQixZQUFZLENBQUNpQixTQUFTLENBQUNDLFVBQVU7WUFDaEQsSUFBSUMsVUFBVUEsT0FBT3ZDLFNBQVMsSUFBSXVDLE9BQU92QyxTQUFTLEtBQUssZUFBZTtnQkFDcEUsSUFBSSxDQUFDdUQsY0FBYyxDQUFDaEIsT0FBT3ZDLFNBQVMsQ0FBQyxFQUFFO29CQUNyQ3VELGNBQWMsQ0FBQ2hCLE9BQU92QyxTQUFTLENBQUMsR0FBRyxFQUFFO2dCQUN2QztnQkFDQXVELGNBQWMsQ0FBQ2hCLE9BQU92QyxTQUFTLENBQUMsQ0FBQ0UsSUFBSSxDQUFDO29CQUNwQ3FDO29CQUNBRjtvQkFDQUM7b0JBQ0FrQixVQUFVbkIsV0FBV25GLG9CQUFvQmlFLE1BQU0sR0FBR21CLFVBQVUsY0FBYztnQkFDNUU7WUFDRjtRQUNGO0lBQ0Y7SUFFQSw2RUFBNkU7SUFDN0VtQixPQUFPQyxPQUFPLENBQUNILGdCQUFnQnpELE9BQU8sQ0FBQyxDQUFDLENBQUNFLFdBQVcyRCxRQUFRO1FBQzFELHdEQUF3RDtRQUN4REEsUUFBUTdCLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFeUIsUUFBUSxHQUFHeEIsRUFBRXdCLFFBQVE7UUFFOUMsMENBQTBDO1FBQzFDRyxRQUFRN0QsT0FBTyxDQUFDLENBQUM4RCxZQUFZQztZQUMzQixNQUFNLEVBQUV0QixNQUFNLEVBQUVGLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdzQjtZQUN4QyxNQUFNRSxrQkFBa0JELFFBQVE7WUFFaEMsa0RBQWtEO1lBQ2xELE1BQU0zQixhQUF5QjtnQkFDN0JsQyxXQUFXdUMsT0FBT3ZDLFNBQVM7Z0JBQzNCRyxhQUFhb0MsT0FBT3dCLE9BQU87Z0JBQzNCeEcsYUFBYWdGLE9BQU9oRixXQUFXO2dCQUMvQkcsZ0JBQWdCaUcsUUFBUXhDLE1BQU07Z0JBQzlCUCxlQUFlMkIsT0FBTzNCLGFBQWE7Z0JBQ25DSCxRQUFRO2dCQUNSQyxZQUFZNkIsT0FBTzdCLFVBQVUsSUFBSSxJQUFJc0Q7WUFDdkM7WUFFQSxNQUFNQyxnQkFBZ0JaLG1CQUNwQm5CLFlBQ0E0QixpQkFDQXpCLFVBQ0FDLFdBQ0E5RSxjQUNBeUQ7WUFHRixxQ0FBcUM7WUFDckNHLFlBQVksQ0FBQ2lCLFNBQVMsQ0FBQ0MsVUFBVSxHQUFHMkI7UUFDdEM7SUFDRjtJQUVBdEYsUUFBUUMsR0FBRyxDQUFDLENBQUMsMkNBQTJDLEVBQUU2RSxPQUFPUyxJQUFJLENBQUNYLGdCQUFnQnBDLE1BQU0sQ0FBQyxTQUFTLENBQUM7QUFDekc7QUFFQSxtREFBbUQ7QUFDbkQsU0FBUzRCLHNCQUFzQm5DLGFBQXFCO0lBQ2xELElBQUlBLGlCQUFpQixHQUFHO1FBQ3RCLGtEQUFrRDtRQUNsRCxPQUFPO1lBQUM7WUFBRztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7U0FBRTtJQUM5QixPQUFPLElBQUlBLGlCQUFpQixHQUFHO1FBQzdCLGtEQUFrRDtRQUNsRCxPQUFPO1lBQUM7WUFBRztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7U0FBRTtJQUM5QixPQUFPLElBQUlBLGlCQUFpQixHQUFHO1FBQzdCLG9EQUFvRDtRQUNwRCxPQUFPO1lBQUM7WUFBRztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7U0FBRTtJQUM5QixPQUFPO1FBQ0wsd0NBQXdDO1FBQ3hDLE9BQU87WUFBQztZQUFHO1lBQUc7WUFBRztZQUFHO1lBQUc7WUFBRztTQUFFO0lBQzlCO0FBQ0Y7QUFFQSx5QkFBeUI7QUFDekIsU0FBU3lDLG1CQUNQbkIsVUFBc0IsRUFDdEJ6RSxZQUFvQixFQUNwQjRFLFFBQWdCLEVBQ2hCQyxTQUFpQixFQUNqQjlFLFlBQW9CLEVBQ3BCeUQsWUFBb0I7SUFFcEIsTUFBTWtELGtCQUFrQjlHLHdCQUN0QjRELGNBQ0FpQixXQUFXM0UsV0FBVyxFQUN0QkMsY0FDQUMsY0FDQXlFLFdBQVd4RSxjQUFjO0lBRzNCLDJDQUEyQztJQUMzQyxxRUFBcUU7SUFDckUsTUFBTStDLFNBQVM7SUFFZixPQUFPO1FBQ0xSLElBQUksR0FBR2tFLGdCQUFnQixLQUFLLEVBQUUzRyxjQUFjO1FBQzVDMkcsaUJBQWlCQTtRQUNqQkMsT0FBTyxHQUFHbEMsV0FBVy9CLFdBQVcsQ0FBQyxRQUFRLEVBQUUzQyxhQUFhLFNBQVMsRUFBRUMsY0FBYztRQUNqRnNHLFNBQVM3QixXQUFXL0IsV0FBVztRQUMvQkgsV0FBV2tDLFdBQVdsQyxTQUFTO1FBQy9CekMsYUFBYTJFLFdBQVczRSxXQUFXO1FBQ25DOEcsTUFBTW5ILG1CQUFtQixDQUFDb0YsVUFBVTtRQUNwQ2dDLEtBQUtuSCxZQUFZLENBQUNrRixTQUFTO1FBQzNCa0MsVUFBVTtRQUNWOUQsUUFBUUE7UUFDUitELGFBQWEsQ0FBQyxLQUFLLEVBQUVoSCxhQUFhLFFBQVEsRUFBRUMsYUFBYSxLQUFLLEVBQUV5RSxXQUFXL0IsV0FBVyxFQUFFO1FBQ3hGN0MsT0FBTzJEO1FBQ1B6RCxjQUFjQTtRQUNkaUgsb0JBQW9CaEg7UUFDcEJFLHNCQUFzQixDQUFFSCxlQUFlLEtBQUswRSxXQUFXeEUsY0FBYyxHQUFJRDtRQUN6RWlILFlBQVk7UUFDWkMsU0FBUztRQUNUL0QsZUFBZXNCLFdBQVd0QixhQUFhO1FBQ3ZDZ0UsV0FBVyxJQUFJWixPQUFPYSxXQUFXO1FBQ2pDQyxXQUFXLElBQUlkLE9BQU9hLFdBQVc7SUFDbkM7QUFDRjtBQUVBLHlFQUF5RTtBQUN6RSxTQUFTRSw0QkFDUHBCLE9BQWMsRUFDZHFCLGdCQUFxQztJQUVyQ3JHLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDhDQUE4QyxFQUFFK0UsUUFBUXhDLE1BQU0sQ0FBQyxRQUFRLENBQUM7SUFFckYsTUFBTThELGlCQUFpQnRCLFFBQVF0QyxHQUFHLENBQUNrQixDQUFBQTtRQUNqQyxJQUFJLENBQUNBLE9BQU80QixlQUFlLEVBQUU7WUFDM0IsT0FBTzVCLFFBQVEsOERBQThEO1FBQy9FO1FBRUEsMkNBQTJDO1FBQzNDLE1BQU0yQyxpQkFBaUJGLGdCQUFnQixDQUFDekMsT0FBTzRCLGVBQWUsQ0FBQztRQUUvRCxJQUFJZSxnQkFBZ0I7WUFDbEJ2RyxRQUFRQyxHQUFHLENBQUMsQ0FBQywwQ0FBMEMsRUFBRTJELE9BQU80QixlQUFlLENBQUMsQ0FBQyxDQUFDLEVBQUVlO1lBRXBGLDhDQUE4QztZQUM5QyxPQUFPO2dCQUNMLEdBQUczQyxNQUFNO2dCQUNUOUIsUUFBUXlFLGVBQWV6RSxNQUFNLElBQUk4QixPQUFPOUIsTUFBTTtnQkFDOUMwRSxXQUFXRCxlQUFlQyxTQUFTLElBQUk7Z0JBQ3ZDQyxhQUFhRixlQUFlRSxXQUFXO2dCQUN2Q0MsVUFBVUgsZUFBZUcsUUFBUSxJQUFJO2dCQUNyQyw4Q0FBOEM7Z0JBQzlDSCxnQkFBZ0JBO1lBQ2xCO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsT0FBTzNDO0lBQ1Q7SUFFQSxNQUFNK0MsaUJBQWlCTCxlQUFlTSxNQUFNLENBQUNsRixDQUFBQSxJQUFLQSxFQUFFSSxNQUFNLEtBQUssYUFBYVUsTUFBTTtJQUNsRixNQUFNcUUsa0JBQWtCUCxlQUFlTSxNQUFNLENBQUNsRixDQUFBQSxJQUFLQSxFQUFFSSxNQUFNLEtBQUssZUFBZVUsTUFBTTtJQUNyRixNQUFNc0UsZ0JBQWdCUixlQUFlTSxNQUFNLENBQUNsRixDQUFBQSxJQUFLQSxFQUFFSSxNQUFNLEtBQUssWUFBWVUsTUFBTTtJQUVoRnhDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVDQUF1QyxFQUFFMEcsZUFBZSxZQUFZLEVBQUVFLGdCQUFnQixjQUFjLEVBQUVDLGNBQWMsU0FBUyxDQUFDO0lBRTNJLE9BQU9SO0FBQ1Q7QUFFQSwwQkFBMEI7QUFDMUIsZUFBZVMsZ0JBQWdCaEgsU0FBaUI7SUFDOUMsSUFBSTtRQUNGLG1DQUFtQztRQUNuQyxNQUFNRyxtQkFBbUI7WUFDdkJIO1lBQ0EsQ0FBQyxrQkFBa0IsQ0FBQztZQUNwQkEsVUFBVVIsV0FBVztZQUNyQlEsVUFBVUksT0FBTyxDQUFDLFFBQVEsS0FBS1osV0FBVztTQUMzQztRQUVELEtBQUssTUFBTWUsVUFBVUosaUJBQWtCO1lBQ3JDLE1BQU1HLGFBQWtCaEMsbURBQUVBLENBQUVtQyxVQUFVLENBQUMsWUFBWUMsR0FBRyxDQUFDSDtZQUN2RCxNQUFNMEcsY0FBYyxNQUFNM0csV0FBV00sR0FBRztZQUV4QyxJQUFJcUcsWUFBWXBHLE1BQU0sRUFBRTtnQkFDdEIsTUFBTXFHLGNBQWNELFlBQVk1RixJQUFJO2dCQUNwQyxPQUFPNkYsYUFBYUMsY0FBY0QsYUFBYXRJLFNBQVM7WUFDMUQ7UUFDRjtRQUVBLE9BQU8sTUFBTSxnQkFBZ0I7SUFDL0IsRUFBRSxPQUFPdUQsT0FBTztRQUNkbEMsUUFBUWtDLEtBQUssQ0FBQyxpREFBaURBO1FBQy9ELE9BQU87SUFDVDtBQUNGO0FBRUEsZ0RBQWdEO0FBQ2hELGVBQWVpRiw0QkFBNEJwSCxTQUFpQjtJQUMxRCxJQUFJO1FBQ0ZDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLCtEQUErRCxFQUFFRixXQUFXO1FBRXpGLGdFQUFnRTtRQUNoRSxNQUFNRyxtQkFBbUI7WUFDdkJIO1lBQ0EsQ0FBQyxrQkFBa0IsQ0FBQztZQUNwQkEsVUFBVVIsV0FBVztZQUNyQlEsVUFBVUksT0FBTyxDQUFDLFFBQVEsS0FBS1osV0FBVztTQUMzQztRQUVELE1BQU04RyxtQkFBd0MsQ0FBQztRQUUvQyxLQUFLLE1BQU0vRixVQUFVSixpQkFBa0I7WUFDckMsSUFBSTtnQkFDRixNQUFNa0gsa0JBQWtCL0ksbURBQUVBLENBQUVtQyxVQUFVLENBQUMsaUJBQWlCQyxHQUFHLENBQUNIO2dCQUM1RCxNQUFNK0csbUJBQW1CLE1BQU1ELGdCQUFnQnpHLEdBQUc7Z0JBRWxELElBQUkwRyxpQkFBaUJ6RyxNQUFNLEVBQUU7b0JBQzNCLE1BQU0wRyxhQUFhRCxpQkFBaUJqRyxJQUFJO29CQUN4Q3BCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJEQUEyRCxFQUFFSyxRQUFRO29CQUVsRixpQ0FBaUM7b0JBQ2pDLElBQUlnSCxZQUFZO3dCQUNkeEMsT0FBT0MsT0FBTyxDQUFDdUMsWUFBWW5HLE9BQU8sQ0FBQyxDQUFDLENBQUNvRyxXQUFXdEMsV0FBMEI7NEJBQ3hFLElBQUlBLGNBQWMsT0FBT0EsZUFBZSxVQUFVO2dDQUNoRCwwREFBMEQ7Z0NBQzFEb0IsZ0JBQWdCLENBQUNrQixVQUFVLEdBQUc7b0NBQzVCekYsUUFBUW1ELFdBQVduRCxNQUFNLElBQUltRCxXQUFXdUMsaUJBQWlCLElBQUk7b0NBQzdEaEIsV0FBV3ZCLFdBQVd1QixTQUFTLElBQUk7b0NBQ25DQyxhQUFheEIsV0FBV3dCLFdBQVcsSUFBSXhCLFdBQVd3QyxZQUFZO29DQUM5RGYsVUFBVXpCLFdBQVd5QixRQUFRLElBQUk7b0NBQ2pDLEdBQUd6QixVQUFVO2dDQUNmOzRCQUNGO3dCQUNGO29CQUNGO29CQUVBLE9BQU8sMkNBQTJDO2dCQUNwRDtZQUNGLEVBQUUsT0FBT3lDLGFBQWE7Z0JBQ3BCMUgsUUFBUWEsSUFBSSxDQUFDLENBQUMsd0RBQXdELEVBQUVQLE9BQU8sQ0FBQyxDQUFDLEVBQUVvSDtnQkFDbkY7WUFDRjtRQUNGO1FBRUExSCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxzQkFBc0IsRUFBRTZFLE9BQU9TLElBQUksQ0FBQ2Msa0JBQWtCN0QsTUFBTSxDQUFDLDBCQUEwQixDQUFDO1FBQ3JHLE9BQU82RDtJQUVULEVBQUUsT0FBT25FLE9BQU87UUFDZGxDLFFBQVFrQyxLQUFLLENBQUMsNERBQTREQTtRQUMxRSxPQUFPLENBQUM7SUFDVjtBQUNGO0FBRUEsNkJBQTZCO0FBQzdCLGVBQWV5RixnQkFBZ0JDLE9BQW9CO0lBQ2pELElBQUk7UUFDRixNQUFNQyxhQUFhRCxRQUFRRSxPQUFPLENBQUNuSCxHQUFHLENBQUM7UUFDdkMsSUFBSSxDQUFDa0gsWUFBWW5JLFdBQVcsWUFBWTtZQUN0QyxPQUFPO1FBQ1Q7UUFFQSxNQUFNcUksVUFBVUYsV0FBV0csS0FBSyxDQUFDLFVBQVUsQ0FBQyxFQUFFO1FBQzlDLE1BQU1DLGVBQWUsTUFBTTNKLDREQUFPQSxHQUFHNEosYUFBYSxDQUFDSDtRQUNuRCxPQUFPO1lBQUVJLEtBQUtGLGFBQWFFLEdBQUc7UUFBQztJQUNqQyxFQUFFLE9BQU9qRyxPQUFPO1FBQ2RsQyxRQUFRa0MsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBTztJQUNUO0FBQ0Y7QUFFQSxvREFBb0Q7QUFDcEQsU0FBU3VCLDhCQUNQaEIsWUFBOEIsRUFDOUI1RCxZQUFvQixFQUNwQnlELFlBQW9CO0lBRXBCLE1BQU1sRCxtQkFBbUJDLDJCQUEyQmlEO0lBQ3BELElBQUk4RixvQkFBb0I7SUFFeEIsSUFBSyxJQUFJMUUsV0FBVyxHQUFHQSxXQUFXbEYsYUFBYWdFLE1BQU0sRUFBRWtCLFdBQVk7UUFDakUsSUFBSyxJQUFJQyxZQUFZLEdBQUdBLFlBQVlwRixvQkFBb0JpRSxNQUFNLEVBQUVtQixZQUFhO1lBQzNFLElBQUlsQixZQUFZLENBQUNpQixTQUFTLENBQUNDLFVBQVUsS0FBSyxNQUFNO2dCQUM5QyxNQUFNMEUsYUFBYTtvQkFDakIvRyxJQUFJLENBQUMsZ0JBQWdCLEVBQUV6QyxhQUFhLENBQUMsRUFBRUwsWUFBWSxDQUFDa0YsU0FBUyxDQUFDLENBQUMsRUFBRUMsV0FBVztvQkFDNUU2QixpQkFBaUIsR0FBR3BHLGlCQUFpQixDQUFDLEVBQUVrSixPQUFPekosY0FBY00sUUFBUSxDQUFDLEdBQUcsS0FBSyxDQUFDLEVBQUVtSixPQUFPRixtQkFBbUJqSixRQUFRLENBQUMsR0FBRyxNQUFNO29CQUM3SHNHLE9BQU87b0JBQ1BMLFNBQVM7b0JBQ1QvRCxXQUFXO29CQUNYekMsYUFBYTtvQkFDYjhHLE1BQU1uSCxtQkFBbUIsQ0FBQ29GLFVBQVU7b0JBQ3BDZ0MsS0FBS25ILFlBQVksQ0FBQ2tGLFNBQVM7b0JBQzNCa0MsVUFBVTtvQkFDVjlELFFBQVE7b0JBQ1IrRCxhQUFhO29CQUNibEgsT0FBTzJEO29CQUNQekQsY0FBY0E7b0JBQ2RpSCxvQkFBb0JzQztvQkFDcEJwSixzQkFBc0I7b0JBQ3RCK0csWUFBWTtvQkFDWkMsU0FBUztvQkFDVC9ELGVBQWU7b0JBQ2ZnRSxXQUFXLElBQUlaLE9BQU9hLFdBQVc7b0JBQ2pDQyxXQUFXLElBQUlkLE9BQU9hLFdBQVc7b0JBQ2pDcUMsY0FBYztnQkFDaEI7Z0JBRUE5RixZQUFZLENBQUNpQixTQUFTLENBQUNDLFVBQVUsR0FBRzBFO2dCQUNwQ0Q7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUVPLGVBQWVJLElBQUlaLE9BQW9CO0lBQzVDLElBQUk7UUFDRixrREFBa0Q7UUFDbEQsSUFBSSxDQUFDdkosbURBQUVBLEVBQUU7WUFDUDJCLFFBQVFrQyxLQUFLLENBQUM7WUFDZCxPQUFPOUQscURBQVlBLENBQUNxSyxJQUFJLENBQ3RCO2dCQUNFQyxTQUFTO2dCQUNUeEcsT0FBTztZQUNULEdBQ0E7Z0JBQUVKLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDBEQUEwRDtRQUMxRCwwREFBMEQ7UUFDMUQ7Ozs7Ozs7O0lBUUEsR0FFQSxNQUFNLEVBQUU2RyxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJaEIsUUFBUWlCLEdBQUc7UUFDNUMsTUFBTTlJLFlBQVk0SSxhQUFhaEksR0FBRyxDQUFDO1FBQ25DLE1BQU1tSSxPQUFPSCxhQUFhaEksR0FBRyxDQUFDO1FBQzlCLE1BQU1vSSxZQUFZSixhQUFhaEksR0FBRyxDQUFDO1FBRW5DLDJDQUEyQztRQUMzQyxNQUFNOUIsZUFBZWtLLFlBQVlDLFNBQVNELFdBQVcsTUFBTTtRQUUzRCxJQUFJRSxNQUFNcEssaUJBQWlCQSxlQUFlLEtBQUtBLGVBQWUsSUFBSTtZQUNoRSxPQUFPVCxxREFBWUEsQ0FBQ3FLLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU94RyxPQUFPO1lBQWlELEdBQzFFO2dCQUFFSixRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJLENBQUMvQixXQUFXO1lBQ2QsT0FBTzNCLHFEQUFZQSxDQUFDcUssSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT3hHLE9BQU87WUFBeUIsR0FDbEQ7Z0JBQUVKLFFBQVE7WUFBSTtRQUVsQjtRQUVBOUIsUUFBUUMsR0FBRyxDQUFDLENBQUMsZ0RBQWdELEVBQUVGLFVBQVUsUUFBUSxFQUFFbEIsYUFBYSxRQUFRLEVBQUVpSyxNQUFNO1FBRWhILDRCQUE0QjtRQUM1QixNQUFNeEcsZUFBZSxNQUFNeUUsZ0JBQWdCaEg7UUFDM0NDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLCtCQUErQixFQUFFcUMsY0FBYztRQUU1RCxxRUFBcUU7UUFDckUsTUFBTWxDLGNBQWMsTUFBTU4sd0JBQXdCQztRQUVsRCxJQUFJSyxZQUFZb0MsTUFBTSxLQUFLLEdBQUc7WUFDNUJ4QyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPN0IscURBQVlBLENBQUNxSyxJQUFJLENBQUM7Z0JBQ3ZCQyxTQUFTO2dCQUNUdEgsTUFBTTtvQkFDSjhILFVBQVUsRUFBRTtvQkFDWkMsV0FBVyxFQUFFO29CQUNiQyxjQUFjO29CQUNkaEosYUFBYSxFQUFFO29CQUNmaUosWUFBWXRKO29CQUNadUosZUFBZXpLO29CQUNmMEssYUFBYTtvQkFDYkMsZUFBZWxIO29CQUNmd0csTUFBTUE7Z0JBQ1I7WUFDRjtRQUNGO1FBRUEsaURBQWlEO1FBQ2pELE1BQU1XLGlCQUFpQnBILDRCQUE0QmpDLGFBQWF2QixjQUFjeUQ7UUFFOUUsZ0RBQWdEO1FBQ2hELE1BQU0rRCxtQkFBbUIsTUFBTWMsNEJBQTRCcEg7UUFFM0QsMERBQTBEO1FBQzFELE1BQU0ySiwrQkFBK0J0RCw0QkFBNEJxRCxnQkFBZ0JwRDtRQUVqRix1Q0FBdUM7UUFDdkMsTUFBTXNELFdBQVc7WUFBQztZQUFVO1lBQVc7WUFBYTtZQUFZO1NBQVM7UUFDekVELDZCQUE2QnZHLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUNwQyxNQUFNdUcsT0FBT0QsU0FBU0UsT0FBTyxDQUFDekcsRUFBRXVDLEdBQUc7WUFDbkMsTUFBTW1FLE9BQU9ILFNBQVNFLE9BQU8sQ0FBQ3hHLEVBQUVzQyxHQUFHO1lBRW5DLElBQUlpRSxTQUFTRSxNQUFNO2dCQUNqQixPQUFPRixPQUFPRTtZQUNoQjtZQUVBLG1DQUFtQztZQUNuQyxJQUFJMUcsRUFBRXNDLElBQUksSUFBSXJDLEVBQUVxQyxJQUFJLEVBQUU7Z0JBQ3BCLE9BQU90QyxFQUFFc0MsSUFBSSxDQUFDcUUsYUFBYSxDQUFDMUcsRUFBRXFDLElBQUk7WUFDcEM7WUFFQSxPQUFPO1FBQ1Q7UUFFQTFGLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDBCQUEwQixFQUFFeUosNkJBQTZCbEgsTUFBTSxDQUFDLGtCQUFrQixFQUFFM0QsY0FBYztRQUUvRyxPQUFPVCxxREFBWUEsQ0FBQ3FLLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUdEgsTUFBTTtnQkFDSjhILFVBQVVRO2dCQUNWUCxXQUFXTztnQkFDWE4sY0FBY00sNkJBQTZCbEgsTUFBTTtnQkFDakRwQyxhQUFhQTtnQkFDYmlKLFlBQVl0SjtnQkFDWnVKLGVBQWV6SztnQkFDZjBLLGFBQWE7Z0JBQ2JDLGVBQWVsSDtnQkFDZndHLE1BQU1BO1lBQ1I7UUFDRjtJQUVGLEVBQUUsT0FBTzVHLE9BQU87UUFDZGxDLFFBQVFrQyxLQUFLLENBQUMsMEJBQTBCQTtRQUN4QyxPQUFPOUQscURBQVlBLENBQUNxSyxJQUFJLENBQ3RCO1lBQ0VDLFNBQVM7WUFDVHhHLE9BQU9BLGlCQUFpQjhILFFBQVE5SCxNQUFNK0gsT0FBTyxHQUFHO1lBQ2hEQyxTQUFTaEksaUJBQWlCOEgsUUFBUTlILE1BQU1pSSxLQUFLLEdBQUc7UUFDbEQsR0FDQTtZQUFFckksUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxhcGlcXHRpbWV0YWJsZVxccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcclxuaW1wb3J0IHsgZGIgfSBmcm9tICdAL2xpYi9maXJlYmFzZS1hZG1pbic7XHJcbmltcG9ydCB7IGNhbGN1bGF0ZUxlc3NvblN0YXR1cyB9IGZyb20gJ0AvbGliL3RpbWUtdXRpbHMnO1xyXG5pbXBvcnQgeyBnZXRBdXRoIH0gZnJvbSAnZmlyZWJhc2UtYWRtaW4vYXV0aCc7XHJcblxyXG4vLyBEZWZpbmUgdGhlIHN0YW5kYXJkIHRpbWUgc2xvdHMgZm9yIHRpbWV0YWJsZSBnZW5lcmF0aW9uXHJcbmNvbnN0IFNUQU5EQVJEX1RJTUVfU0xPVFMgPSBbXHJcbiAgJzA4OjAwLTA4OjQ1JyxcclxuICAnMDg6NTAtMDk6MzUnLCBcclxuICAnMDk6NDAtMTA6MjUnLFxyXG4gICcxMDo0MC0xMToyNScsXHJcbiAgJzExOjMwLTEyOjE1JyxcclxuICAnMTM6MTUtMTQ6MDAnLFxyXG4gICcxNDoxNS0xNTowMCdcclxuXTtcclxuXHJcbmNvbnN0IERBWVNfT0ZfV0VFSyA9IFsnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheSddO1xyXG5cclxuLy8gSW50ZXJmYWNlIGZvciBlbnJvbGxtZW50IGRhdGFcclxuaW50ZXJmYWNlIEVucm9sbG1lbnQge1xyXG4gIHN1YmplY3RJZDogc3RyaW5nO1xyXG4gIHN1YmplY3ROYW1lOiBzdHJpbmc7XHJcbiAgc3ViamVjdENvZGU6IHN0cmluZztcclxuICBsZXNzb25zUGVyV2VlazogbnVtYmVyO1xyXG4gIHN0YXR1czogc3RyaW5nO1xyXG4gIGVucm9sbGVkQXQ6IGFueTtcclxuICBjb2duaXRpdmVMb2FkPzogbnVtYmVyOyAvLyBBZGQgY29nbml0aXZlIGxvYWQgcmF0aW5nICgxLTEwLCAxMCBiZWluZyBoaWdoZXN0KVxyXG59XHJcblxyXG4vLyBTdWJqZWN0IGNvZ25pdGl2ZSBsb2FkIG1hcHBpbmcgKDEtMTAsIHdoZXJlIDEwIGlzIGhpZ2hlc3QgY29nbml0aXZlIGxvYWQpXHJcbmNvbnN0IFNVQkpFQ1RfQ09HTklUSVZFX0xPQURTOiBSZWNvcmQ8c3RyaW5nLCBudW1iZXI+ID0ge1xyXG4gICdtYXRoZW1hdGljcyc6IDksXHJcbiAgJ2VuZ2xpc2hfbGFuZ3VhZ2UnOiA4LFxyXG4gICdiYXNpY19zY2llbmNlX2FuZF90ZWNobm9sb2d5JzogNyxcclxuICAnYmFzaWNfc2NpZW5jZSc6IDcsXHJcbiAgJ3NvY2lhbF9zdHVkaWVzJzogNixcclxuICAnY29tcHV0ZXJfc3R1ZGllcyc6IDgsXHJcbiAgJ2NvbXB1dGluZyc6IDgsXHJcbiAgJ2FydGlmaWNpYWxfaW50ZWxsaWdlbmNlJzogOSxcclxuICAnZnJlbmNoJzogNyxcclxuICAnY2hyaXN0aWFuX3JlbGlnaW91c19rbm93bGVkZ2UnOiA1LFxyXG4gICduYXRpb25hbF92YWx1ZXNfZWR1Y2F0aW9uJzogNCxcclxuICAnZW50cmVwcmVuZXVyc2hpcF9lZHVjYXRpb24nOiA1LFxyXG4gICdlbnRyZXByZW5ldXJzaGlwJzogNSxcclxuICAnZmluYW5jaWFsX2xpdGVyYWN5JzogNixcclxuICAnY3JlYXRpdmVfYXJ0cyc6IDMsXHJcbiAgJ2N1bHR1cmFsX2FuZF9jcmVhdGl2ZV9hcnRzJzogMyxcclxuICAnYXJ0X2FuZF9kZXNpZ24nOiAzLFxyXG4gICdwaHlzaWNhbF9oZWFsdGhfZWR1Y2F0aW9uJzogMixcclxuICAncHJvamVjdF9iYXNlZF9leGNlbGxlbmNlJzogNlxyXG59O1xyXG5cclxuLy8gQ2FsY3VsYXRlIGxlc3NvbiByZWZlcmVuY2UgZm9yIGEgc3BlY2lmaWMgd2VlayBhbmQgc3ViamVjdFxyXG5mdW5jdGlvbiBnZW5lcmF0ZUxlc3NvblJlZmVyZW5jZShcclxuICBncmFkZTogc3RyaW5nLFxyXG4gIHN1YmplY3RDb2RlOiBzdHJpbmcsXHJcbiAgYWNhZGVtaWNXZWVrOiBudW1iZXIsXHJcbiAgbGVzc29uTnVtYmVyOiBudW1iZXIsXHJcbiAgbGVzc29uc1BlcldlZWs6IG51bWJlclxyXG4pOiBzdHJpbmcge1xyXG4gIC8vIENhbGN1bGF0ZSB0aGUgYWJzb2x1dGUgbGVzc29uIG51bWJlciBhY3Jvc3MgYWxsIHdlZWtzXHJcbiAgY29uc3QgYWJzb2x1dGVMZXNzb25OdW1iZXIgPSAoKGFjYWRlbWljV2VlayAtIDEpICogbGVzc29uc1BlcldlZWspICsgbGVzc29uTnVtYmVyO1xyXG5cclxuICAvLyBGb3JtYXQgbGVzc29uIG51bWJlciB3aXRoIGxlYWRpbmcgemVyb3MgKGUuZy4sIDAwMSwgMDAyLCBldGMuKVxyXG4gIGNvbnN0IGZvcm1hdHRlZExlc3Nvbk51bWJlciA9IGFic29sdXRlTGVzc29uTnVtYmVyLnRvU3RyaW5nKCkucGFkU3RhcnQoMywgJzAnKTtcclxuXHJcbiAgLy8gQ29udmVydCBncmFkZSB0byBwcm9wZXIgYWJicmV2aWF0ZWQgZm9ybWF0XHJcbiAgY29uc3QgYWJicmV2aWF0ZWRHcmFkZSA9IGNvbnZlcnRHcmFkZVRvQWJicmV2aWF0aW9uKGdyYWRlKTtcclxuXHJcbiAgcmV0dXJuIGAke2FiYnJldmlhdGVkR3JhZGV9LSR7c3ViamVjdENvZGV9LSR7Zm9ybWF0dGVkTGVzc29uTnVtYmVyfWA7XHJcbn1cclxuXHJcbi8vIENvbnZlcnQgZ3JhZGUgbGV2ZWwgdG8gcHJvcGVyIGFiYnJldmlhdGVkIGZvcm1hdFxyXG5mdW5jdGlvbiBjb252ZXJ0R3JhZGVUb0FiYnJldmlhdGlvbihncmFkZTogc3RyaW5nKTogc3RyaW5nIHtcclxuICAvLyBIYW5kbGUgZGlmZmVyZW50IGdyYWRlIGZvcm1hdHNcclxuICBjb25zdCBncmFkZVN0ciA9IGdyYWRlLnRvTG93ZXJDYXNlKCkudHJpbSgpO1xyXG5cclxuICAvLyBQcmltYXJ5IGdyYWRlc1xyXG4gIGlmIChncmFkZVN0ci5pbmNsdWRlcygncHJpbWFyeScpIHx8IGdyYWRlU3RyLnN0YXJ0c1dpdGgoJ3AnKSkge1xyXG4gICAgY29uc3QgbWF0Y2ggPSBncmFkZVN0ci5tYXRjaCgvKFxcZCspLyk7XHJcbiAgICBpZiAobWF0Y2gpIHtcclxuICAgICAgcmV0dXJuIGBQJHttYXRjaFsxXX1gO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gSnVuaW9yIFNlY29uZGFyeVxyXG4gIGlmIChncmFkZVN0ci5pbmNsdWRlcygnanVuaW9yJykgfHwgZ3JhZGVTdHIuaW5jbHVkZXMoJ2pzcycpKSB7XHJcbiAgICBjb25zdCBtYXRjaCA9IGdyYWRlU3RyLm1hdGNoKC8oXFxkKykvKTtcclxuICAgIGlmIChtYXRjaCkge1xyXG4gICAgICByZXR1cm4gYEpTUyR7bWF0Y2hbMV19YDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC8vIFNlbmlvciBTZWNvbmRhcnlcclxuICBpZiAoZ3JhZGVTdHIuaW5jbHVkZXMoJ3NlbmlvcicpIHx8IGdyYWRlU3RyLmluY2x1ZGVzKCdzc3MnKSkge1xyXG4gICAgY29uc3QgbWF0Y2ggPSBncmFkZVN0ci5tYXRjaCgvKFxcZCspLyk7XHJcbiAgICBpZiAobWF0Y2gpIHtcclxuICAgICAgcmV0dXJuIGBTU1Mke21hdGNoWzFdfWA7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBJZiBhbHJlYWR5IGluIGNvcnJlY3QgZm9ybWF0LCByZXR1cm4gYXMgaXNcclxuICBpZiAoL14oUHxKU1N8U1NTKVxcZCskL2kudGVzdChncmFkZSkpIHtcclxuICAgIHJldHVybiBncmFkZS50b1VwcGVyQ2FzZSgpO1xyXG4gIH1cclxuXHJcbiAgLy8gRGVmYXVsdCBmYWxsYmFja1xyXG4gIHJldHVybiBncmFkZTtcclxufVxyXG5cclxuLy8gRmV0Y2ggc3R1ZGVudCBlbnJvbGxtZW50cyBmcm9tIEZpcmVzdG9yZVxyXG5hc3luYyBmdW5jdGlvbiBmZXRjaFN0dWRlbnRFbnJvbGxtZW50cyhzdHVkZW50SWQ6IHN0cmluZyk6IFByb21pc2U8RW5yb2xsbWVudFtdPiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gRmV0Y2hpbmcgZW5yb2xsbWVudHMgZm9yIHN0dWRlbnQ6ICR7c3R1ZGVudElkfWApO1xyXG4gICAgXHJcbiAgICAvLyBUcnkgZGlmZmVyZW50IHN0dWRlbnQgSUQgZm9ybWF0c1xyXG4gICAgY29uc3Qgc3R1ZGVudElkRm9ybWF0cyA9IFtcclxuICAgICAgc3R1ZGVudElkLFxyXG4gICAgICBgYW5kcmVhX3Vnb25vXzMzMzA1YCwgLy8gRGlyZWN0IElEXHJcbiAgICAgIHN0dWRlbnRJZC50b0xvd2VyQ2FzZSgpLFxyXG4gICAgICBzdHVkZW50SWQucmVwbGFjZSgvXFxzKy9nLCAnXycpLnRvTG93ZXJDYXNlKClcclxuICAgIF07XHJcbiAgICBcclxuICAgIGxldCBlbnJvbGxtZW50czogRW5yb2xsbWVudFtdID0gW107XHJcbiAgICBsZXQgc3R1ZGVudFJlZjogYW55ID0gbnVsbDtcclxuICAgIFxyXG4gICAgZm9yIChjb25zdCBmb3JtYXQgb2Ygc3R1ZGVudElkRm9ybWF0cykge1xyXG4gICAgICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIFRyeWluZyBzdHVkZW50IElEIGZvcm1hdDogJHtmb3JtYXR9YCk7XHJcbiAgICAgIFxyXG4gICAgICBjb25zdCB0ZXN0U3R1ZGVudFJlZiA9IGRiIS5jb2xsZWN0aW9uKCdzdHVkZW50cycpLmRvYyhmb3JtYXQpO1xyXG4gICAgICBjb25zdCB0ZXN0U3R1ZGVudFNuYXAgPSBhd2FpdCB0ZXN0U3R1ZGVudFJlZi5nZXQoKTtcclxuICAgICAgXHJcbiAgICAgIGlmICh0ZXN0U3R1ZGVudFNuYXAuZXhpc3RzKSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBGb3VuZCBzdHVkZW50IGRvY3VtZW50IHdpdGggSUQ6ICR7Zm9ybWF0fWApO1xyXG4gICAgICAgIHN0dWRlbnRSZWYgPSB0ZXN0U3R1ZGVudFJlZjtcclxuICAgICAgICBicmVhaztcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgXHJcbiAgICBpZiAoIXN0dWRlbnRSZWYpIHtcclxuICAgICAgY29uc29sZS53YXJuKGBbVGltZXRhYmxlIEFQSV0gTm8gc3R1ZGVudCBkb2N1bWVudCBmb3VuZCBmb3IgYW55IElEIGZvcm1hdGApO1xyXG4gICAgICByZXR1cm4gW107XHJcbiAgICB9XHJcbiAgICBcclxuICAgIC8vIEZldGNoIGVucm9sbG1lbnRzIGZyb20gdGhlIGVucm9sbG1lbnRzIHN1YmNvbGxlY3Rpb25cclxuICAgIGNvbnN0IGVucm9sbG1lbnRzUmVmID0gc3R1ZGVudFJlZi5jb2xsZWN0aW9uKCdlbnJvbGxtZW50cycpO1xyXG4gICAgY29uc3QgZW5yb2xsbWVudHNTbmFwID0gYXdhaXQgZW5yb2xsbWVudHNSZWYud2hlcmUoJ3N0YXR1cycsICc9PScsICdhY3RpdmUnKS5nZXQoKTtcclxuICAgIFxyXG4gICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBGb3VuZCAke2Vucm9sbG1lbnRzU25hcC5zaXplfSBhY3RpdmUgZW5yb2xsbWVudHNgKTtcclxuICAgIFxyXG4gICAgZW5yb2xsbWVudHNTbmFwLmRvY3MuZm9yRWFjaChkb2MgPT4ge1xyXG4gICAgICBjb25zdCBkYXRhID0gZG9jLmRhdGEoKTtcclxuICAgICAgY29uc3Qgc3ViamVjdElkID0gZG9jLmlkO1xyXG4gICAgICBlbnJvbGxtZW50cy5wdXNoKHtcclxuICAgICAgICBzdWJqZWN0SWQ6IHN1YmplY3RJZCxcclxuICAgICAgICBzdWJqZWN0TmFtZTogZGF0YS5zdWJqZWN0TmFtZSB8fCBkYXRhLnN1YmplY3RfbmFtZSB8fCBkb2MuaWQucmVwbGFjZSgvXy9nLCAnICcpLnJlcGxhY2UoL1xcYlxcdy9nLCBsID0+IGwudG9VcHBlckNhc2UoKSksXHJcbiAgICAgICAgc3ViamVjdENvZGU6IGRhdGEuc3ViamVjdENvZGUgfHwgZGF0YS5zdWJqZWN0X2NvZGUgfHwgZ2V0U3ViamVjdENvZGVGcm9tSWQoZG9jLmlkKSxcclxuICAgICAgICBsZXNzb25zUGVyV2VlazogZGF0YS5sZXNzb25zUGVyV2VlayB8fCBkYXRhLmxlc3NvbnNfcGVyX3dlZWsgfHwgMSxcclxuICAgICAgICBzdGF0dXM6IGRhdGEuc3RhdHVzLFxyXG4gICAgICAgIGVucm9sbGVkQXQ6IGRhdGEuZW5yb2xsZWRBdCB8fCBkYXRhLmVucm9sbGVkX2F0LFxyXG4gICAgICAgIGNvZ25pdGl2ZUxvYWQ6IGRhdGEuY29nbml0aXZlTG9hZCB8fCBTVUJKRUNUX0NPR05JVElWRV9MT0FEU1tzdWJqZWN0SWRdIHx8IDUgLy8gRGVmYXVsdCB0byBtZWRpdW0gY29nbml0aXZlIGxvYWRcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuICAgIFxyXG4gICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBQcm9jZXNzZWQgZW5yb2xsbWVudHM6YCwgZW5yb2xsbWVudHMpO1xyXG4gICAgcmV0dXJuIGVucm9sbG1lbnRzO1xyXG4gICAgXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYFtUaW1ldGFibGUgQVBJXSBFcnJvciBmZXRjaGluZyBlbnJvbGxtZW50czpgLCBlcnJvcik7XHJcbiAgICByZXR1cm4gW107XHJcbiAgfVxyXG59XHJcblxyXG4vLyBHZXQgc3ViamVjdCBjb2RlIGZyb20gc3ViamVjdCBJRFxyXG5mdW5jdGlvbiBnZXRTdWJqZWN0Q29kZUZyb21JZChzdWJqZWN0SWQ6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgLy8gTm9ybWFsaXplIHRoZSBpbnB1dDogY29udmVydCB0byBsb3dlcmNhc2UgYW5kIHJlcGxhY2UgaHlwaGVucy9zcGFjZXMgd2l0aCB1bmRlcnNjb3Jlc1xyXG4gIGNvbnN0IG5vcm1hbGl6ZWRJZCA9IHN1YmplY3RJZC50b0xvd2VyQ2FzZSgpXHJcbiAgICAucmVwbGFjZSgvWy1cXHNdKy9nLCAnXycpXHJcbiAgICAucmVwbGFjZSgvW15hLXowLTlfXS9nLCAnJyk7IC8vIFJlbW92ZSBhbnkgb3RoZXIgc3BlY2lhbCBjaGFyYWN0ZXJzXHJcblxyXG4gIGNvbnN0IGNvZGVNYXA6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XHJcbiAgICAnbWF0aGVtYXRpY3MnOiAnTUFUJyxcclxuICAgICdlbmdsaXNoX2xhbmd1YWdlJzogJ0VORycsXHJcbiAgICAnYmFzaWNfc2NpZW5jZV9hbmRfdGVjaG5vbG9neSc6ICdCU1QnLFxyXG4gICAgJ2Jhc2ljX3NjaWVuY2UnOiAnQlNUJyxcclxuICAgICdzb2NpYWxfc3R1ZGllcyc6ICdTU1QnLFxyXG4gICAgJ2NvbXB1dGVyX3N0dWRpZXMnOiAnQ09NJyxcclxuICAgICdjb21wdXRpbmcnOiAnQ09NJyxcclxuICAgICdjcmVhdGl2ZV9hcnRzJzogJ0FSVCcsXHJcbiAgICAnY3VsdHVyYWxfYW5kX2NyZWF0aXZlX2FydHMnOiAnQ0NBJyxcclxuICAgICdhcnRfYW5kX2Rlc2lnbic6ICdBUlQnLFxyXG4gICAgJ3BoeXNpY2FsX2hlYWx0aF9lZHVjYXRpb24nOiAnUEhFJyxcclxuICAgICduYXRpb25hbF92YWx1ZXNfZWR1Y2F0aW9uJzogJ05WRScsXHJcbiAgICAnZW50cmVwcmVuZXVyc2hpcF9lZHVjYXRpb24nOiAnRU5UJyxcclxuICAgICdlbnRyZXByZW5ldXJzaGlwJzogJ0VOVCcsXHJcbiAgICAnZmluYW5jaWFsX2xpdGVyYWN5JzogJ0ZJTCcsXHJcbiAgICAnZnJlbmNoJzogJ0ZSRScsXHJcbiAgICAnYXJ0aWZpY2lhbF9pbnRlbGxpZ2VuY2UnOiAnQUknLFxyXG4gICAgJ3Byb2plY3RfYmFzZWRfZXhjZWxsZW5jZSc6ICdQQkUnLFxyXG4gICAgJ3Byb2plY3RfYmFzZWRfZXhjZWxsZW5jZSc6ICdQQkUnLCAvLyBIYW5kbGUgdGhlIGh5cGhlbi11bmRlcnNjb3JlIHZhcmlhbnQgZnJvbSBsb2dzXHJcbiAgICAnY2hyaXN0aWFuX3JlbGlnaW91c19rbm93bGVkZ2UnOiAnQ1JLJ1xyXG4gIH07XHJcblxyXG4gIC8vIFRyeSB0aGUgbm9ybWFsaXplZCBJRCBmaXJzdFxyXG4gIGlmIChjb2RlTWFwW25vcm1hbGl6ZWRJZF0pIHtcclxuICAgIHJldHVybiBjb2RlTWFwW25vcm1hbGl6ZWRJZF07XHJcbiAgfVxyXG5cclxuICAvLyBUcnkgdGhlIG9yaWdpbmFsIElEIGFzIGZhbGxiYWNrXHJcbiAgaWYgKGNvZGVNYXBbc3ViamVjdElkLnRvTG93ZXJDYXNlKCldKSB7XHJcbiAgICByZXR1cm4gY29kZU1hcFtzdWJqZWN0SWQudG9Mb3dlckNhc2UoKV07XHJcbiAgfVxyXG5cclxuICAvLyBTcGVjaWFsIGhhbmRsaW5nIGZvciBwcm9qZWN0LWJhc2VkIGV4Y2VsbGVuY2UgdmFyaWFudHNcclxuICBpZiAoc3ViamVjdElkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3Byb2plY3QnKSAmJiBzdWJqZWN0SWQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnZXhjZWxsZW5jZScpKSB7XHJcbiAgICByZXR1cm4gJ1BCRSc7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gJ0dFTic7XHJcbn1cclxuXHJcbi8vIERpc3RyaWJ1dGUgbGVzc29ucyBhY3Jvc3MgdGhlIHdlZWsgYmFzZWQgb24gbGVzc29ucyBwZXIgd2VlayBhbmQgY29nbml0aXZlIGxvYWRcclxuZnVuY3Rpb24gZGlzdHJpYnV0ZUxlc3NvbnNBY3Jvc3NXZWVrKGVucm9sbG1lbnRzOiBFbnJvbGxtZW50W10sIGFjYWRlbWljV2VlazogbnVtYmVyLCBzdHVkZW50R3JhZGU6IHN0cmluZyk6IGFueVtdIHtcclxuICBjb25zdCB3ZWVrbHlMZXNzb25zOiBhbnlbXSA9IFtdO1xyXG5cclxuICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIERpc3RyaWJ1dGluZyBsZXNzb25zIGZvciAke2Vucm9sbG1lbnRzLmxlbmd0aH0gc3ViamVjdHMgYWNyb3NzIHRoZSB3ZWVrYCk7XHJcblxyXG4gIC8vIENyZWF0ZSBhIHdlZWtseSBzY2hlZHVsZSBncmlkOiBbZGF5XVt0aW1lU2xvdF1cclxuICBjb25zdCB3ZWVrU2NoZWR1bGU6IChhbnkgfCBudWxsKVtdW10gPSBEQVlTX09GX1dFRUsubWFwKCgpID0+IG5ldyBBcnJheShTVEFOREFSRF9USU1FX1NMT1RTLmxlbmd0aCkuZmlsbChudWxsKSk7XHJcblxyXG4gIC8vIENhbGN1bGF0ZSB0b3RhbCBsZXNzb25zIG5lZWRlZFxyXG4gIGNvbnN0IHRvdGFsTGVzc29uc05lZWRlZCA9IGVucm9sbG1lbnRzLnJlZHVjZSgoc3VtLCBlKSA9PiBzdW0gKyBlLmxlc3NvbnNQZXJXZWVrLCAwKTtcclxuICBjb25zdCB0b3RhbFNsb3RzQXZhaWxhYmxlID0gREFZU19PRl9XRUVLLmxlbmd0aCAqIFNUQU5EQVJEX1RJTUVfU0xPVFMubGVuZ3RoOyAvLyA1IGRheXMgw5cgNyBzbG90cyA9IDM1XHJcblxyXG4gIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gVG90YWwgbGVzc29ucyBuZWVkZWQ6ICR7dG90YWxMZXNzb25zTmVlZGVkfSwgVG90YWwgc2xvdHMgYXZhaWxhYmxlOiAke3RvdGFsU2xvdHNBdmFpbGFibGV9YCk7XHJcblxyXG4gIC8vIFNvcnQgZW5yb2xsbWVudHMgYnkgY29nbml0aXZlIGxvYWQgKGhpZ2hlc3QgZmlyc3QpIGZvciBvcHRpbWFsIHRpbWUgc2xvdCBhc3NpZ25tZW50XHJcbiAgY29uc3Qgc29ydGVkRW5yb2xsbWVudHMgPSBbLi4uZW5yb2xsbWVudHNdLnNvcnQoKGEsIGIpID0+IChiLmNvZ25pdGl2ZUxvYWQgfHwgNSkgLSAoYS5jb2duaXRpdmVMb2FkIHx8IDUpKTtcclxuXHJcbiAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBTb3J0ZWQgc3ViamVjdHMgYnkgY29nbml0aXZlIGxvYWQ6YCxcclxuICAgIHNvcnRlZEVucm9sbG1lbnRzLm1hcChlID0+IGAke2Uuc3ViamVjdE5hbWV9ICgke2UuY29nbml0aXZlTG9hZH0pIC0gJHtlLmxlc3NvbnNQZXJXZWVrfSBsZXNzb25zYCkpO1xyXG5cclxuICAvLyBUcmFjayBob3cgbWFueSBsZXNzb25zIGVhY2ggc3ViamVjdCBoYXMgYmVlbiBhc3NpZ25lZFxyXG4gIGNvbnN0IHN1YmplY3RMZXNzb25Db3VudHM6IFJlY29yZDxzdHJpbmcsIG51bWJlcj4gPSB7fTtcclxuXHJcbiAgLy8gSW5pdGlhbGl6ZSBhbGwgc3ViamVjdHNcclxuICBzb3J0ZWRFbnJvbGxtZW50cy5mb3JFYWNoKGVucm9sbG1lbnQgPT4ge1xyXG4gICAgc3ViamVjdExlc3NvbkNvdW50c1tlbnJvbGxtZW50LnN1YmplY3RJZF0gPSAwO1xyXG4gIH0pO1xyXG5cclxuICAvLyBTY2hlZHVsZSBhbGwgbGVzc29ucyB1c2luZyBhIG1vcmUgYWdncmVzc2l2ZSBhcHByb2FjaFxyXG4gIHNjaGVkdWxlQWxsTGVzc29uc09wdGltYWxseShzb3J0ZWRFbnJvbGxtZW50cywgd2Vla1NjaGVkdWxlLCBhY2FkZW1pY1dlZWssIHN0dWRlbnRHcmFkZSwgc3ViamVjdExlc3NvbkNvdW50cyk7XHJcblxyXG4gIC8vIEZpbGwgYW55IHJlbWFpbmluZyBlbXB0eSBzbG90cyB3aXRoIFwiRnJlZSBQZXJpb2RcIlxyXG4gIGZpbGxFbXB0eVNsb3RzV2l0aEZyZWVQZXJpb2RzKHdlZWtTY2hlZHVsZSwgYWNhZGVtaWNXZWVrLCBzdHVkZW50R3JhZGUpO1xyXG5cclxuICAvLyBDb252ZXJ0IHRoZSBzY2hlZHVsZSBncmlkIGJhY2sgdG8gbGVzc29uIG9iamVjdHNcclxuICBmb3IgKGxldCBkYXlJbmRleCA9IDA7IGRheUluZGV4IDwgREFZU19PRl9XRUVLLmxlbmd0aDsgZGF5SW5kZXgrKykge1xyXG4gICAgZm9yIChsZXQgdGltZUluZGV4ID0gMDsgdGltZUluZGV4IDwgU1RBTkRBUkRfVElNRV9TTE9UUy5sZW5ndGg7IHRpbWVJbmRleCsrKSB7XHJcbiAgICAgIGNvbnN0IGxlc3NvbiA9IHdlZWtTY2hlZHVsZVtkYXlJbmRleF1bdGltZUluZGV4XTtcclxuICAgICAgaWYgKGxlc3Nvbikge1xyXG4gICAgICAgIHdlZWtseUxlc3NvbnMucHVzaChsZXNzb24pO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIEdlbmVyYXRlZCAke3dlZWtseUxlc3NvbnMubGVuZ3RofSBsZXNzb25zIGZvciB0aGUgd2VlayAodGFyZ2V0OiAke3RvdGFsU2xvdHNBdmFpbGFibGV9KWApO1xyXG5cclxuICAvLyBMb2cgZmluYWwgc3ViamVjdCBkaXN0cmlidXRpb25cclxuICBjb25zdCBmaW5hbENvdW50czogUmVjb3JkPHN0cmluZywgbnVtYmVyPiA9IHt9O1xyXG4gIHdlZWtseUxlc3NvbnMuZm9yRWFjaChsZXNzb24gPT4ge1xyXG4gICAgaWYgKGxlc3Nvbi5zdWJqZWN0SWQgJiYgbGVzc29uLnN1YmplY3RJZCAhPT0gJ2ZyZWVfcGVyaW9kJykge1xyXG4gICAgICBmaW5hbENvdW50c1tsZXNzb24uc3ViamVjdElkXSA9IChmaW5hbENvdW50c1tsZXNzb24uc3ViamVjdElkXSB8fCAwKSArIDE7XHJcbiAgICB9XHJcbiAgfSk7XHJcblxyXG4gIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gRmluYWwgbGVzc29uIGRpc3RyaWJ1dGlvbjpgLCBmaW5hbENvdW50cyk7XHJcblxyXG4gIHJldHVybiB3ZWVrbHlMZXNzb25zO1xyXG59XHJcblxyXG4vLyBOZXcgb3B0aW1pemVkIHNjaGVkdWxpbmcgZnVuY3Rpb24gdGhhdCBlbnN1cmVzIGFsbCBsZXNzb25zIGFyZSBzY2hlZHVsZWRcclxuZnVuY3Rpb24gc2NoZWR1bGVBbGxMZXNzb25zT3B0aW1hbGx5KFxyXG4gIGVucm9sbG1lbnRzOiBFbnJvbGxtZW50W10sXHJcbiAgd2Vla1NjaGVkdWxlOiAoYW55IHwgbnVsbClbXVtdLFxyXG4gIGFjYWRlbWljV2VlazogbnVtYmVyLFxyXG4gIHN0dWRlbnRHcmFkZTogc3RyaW5nLFxyXG4gIHN1YmplY3RMZXNzb25Db3VudHM6IFJlY29yZDxzdHJpbmcsIG51bWJlcj5cclxuKTogdm9pZCB7XHJcbiAgLy8gQ3JlYXRlIGEgbGlzdCBvZiBhbGwgbGVzc29ucyB0aGF0IG5lZWQgdG8gYmUgc2NoZWR1bGVkICh3aXRob3V0IGxlc3NvbiBudW1iZXJzIGluaXRpYWxseSlcclxuICBjb25zdCBsZXNzb25zVG9TY2hlZHVsZTogeyBlbnJvbGxtZW50OiBFbnJvbGxtZW50IH1bXSA9IFtdO1xyXG5cclxuICBlbnJvbGxtZW50cy5mb3JFYWNoKGVucm9sbG1lbnQgPT4ge1xyXG4gICAgZm9yIChsZXQgaSA9IDE7IGkgPD0gZW5yb2xsbWVudC5sZXNzb25zUGVyV2VlazsgaSsrKSB7XHJcbiAgICAgIGxlc3NvbnNUb1NjaGVkdWxlLnB1c2goeyBlbnJvbGxtZW50IH0pO1xyXG4gICAgfVxyXG4gIH0pO1xyXG5cclxuICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIFRvdGFsIGxlc3NvbnMgdG8gc2NoZWR1bGU6ICR7bGVzc29uc1RvU2NoZWR1bGUubGVuZ3RofWApO1xyXG5cclxuICAvLyBTb3J0IGxlc3NvbnMgYnkgY29nbml0aXZlIGxvYWQgKGhpZ2hlc3QgZmlyc3QpXHJcbiAgbGVzc29uc1RvU2NoZWR1bGUuc29ydCgoYSwgYikgPT4ge1xyXG4gICAgcmV0dXJuIChiLmVucm9sbG1lbnQuY29nbml0aXZlTG9hZCB8fCA1KSAtIChhLmVucm9sbG1lbnQuY29nbml0aXZlTG9hZCB8fCA1KTtcclxuICB9KTtcclxuXHJcbiAgLy8gVHJhY2sgbGVzc29ucyBwZXIgZGF5IGZvciBlYWNoIHN1YmplY3QgdG8gZW5mb3JjZSBkaXN0cmlidXRpb24gcnVsZXNcclxuICBjb25zdCBzdWJqZWN0RGFpbHlDb3VudDogUmVjb3JkPHN0cmluZywgbnVtYmVyW10+ID0ge307XHJcbiAgZW5yb2xsbWVudHMuZm9yRWFjaChlbnJvbGxtZW50ID0+IHtcclxuICAgIHN1YmplY3REYWlseUNvdW50W2Vucm9sbG1lbnQuc3ViamVjdElkXSA9IG5ldyBBcnJheShEQVlTX09GX1dFRUsubGVuZ3RoKS5maWxsKDApO1xyXG4gIH0pO1xyXG5cclxuICAvLyBTY2hlZHVsZSBlYWNoIGxlc3NvbiAod2l0aG91dCBhc3NpZ25pbmcgbGVzc29uIG51bWJlcnMgeWV0KVxyXG4gIGZvciAoY29uc3QgbGVzc29uVG9TY2hlZHVsZSBvZiBsZXNzb25zVG9TY2hlZHVsZSkge1xyXG4gICAgY29uc3QgeyBlbnJvbGxtZW50IH0gPSBsZXNzb25Ub1NjaGVkdWxlO1xyXG4gICAgbGV0IHNjaGVkdWxlZCA9IGZhbHNlO1xyXG5cclxuICAgIC8vIEdldCBwcmVmZXJyZWQgdGltZSBzbG90cyBiYXNlZCBvbiBjb2duaXRpdmUgbG9hZFxyXG4gICAgY29uc3QgcHJlZmVycmVkVGltZVNsb3RzID0gZ2V0UHJlZmVycmVkVGltZVNsb3RzKGVucm9sbG1lbnQuY29nbml0aXZlTG9hZCB8fCA1KTtcclxuXHJcbiAgICAvLyBUcnkgdG8gc2NoZWR1bGUgb24gZWFjaCBkYXksIHByaW9yaXRpemluZyBkYXlzIHdpdGggZmV3ZXIgbGVzc29ucyBmb3IgdGhpcyBzdWJqZWN0XHJcbiAgICBjb25zdCBkYXlQcmlvcml0eSA9IEFycmF5LmZyb20oeyBsZW5ndGg6IERBWVNfT0ZfV0VFSy5sZW5ndGggfSwgKF8sIGkpID0+IGkpXHJcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBzdWJqZWN0RGFpbHlDb3VudFtlbnJvbGxtZW50LnN1YmplY3RJZF1bYV0gLSBzdWJqZWN0RGFpbHlDb3VudFtlbnJvbGxtZW50LnN1YmplY3RJZF1bYl0pO1xyXG5cclxuICAgIGZvciAoY29uc3QgZGF5SW5kZXggb2YgZGF5UHJpb3JpdHkpIHtcclxuICAgICAgLy8gU3BlY2lhbCBydWxlIGZvciBNYXRoZW1hdGljczogYWxsb3cgdXAgdG8gMiBsZXNzb25zIG9uIE1vbmRheSwgMSBvbiBvdGhlciBkYXlzXHJcbiAgICAgIGNvbnN0IG1heExlc3NvbnNQZXJEYXkgPSAoZW5yb2xsbWVudC5zdWJqZWN0SWQgPT09ICdtYXRoZW1hdGljcycgJiYgZGF5SW5kZXggPT09IDApID8gMiA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZW5yb2xsbWVudC5sZXNzb25zUGVyV2VlayA+PSA2KSA/IDIgOiAxO1xyXG5cclxuICAgICAgaWYgKHN1YmplY3REYWlseUNvdW50W2Vucm9sbG1lbnQuc3ViamVjdElkXVtkYXlJbmRleF0gPj0gbWF4TGVzc29uc1BlckRheSkge1xyXG4gICAgICAgIGNvbnRpbnVlOyAvLyBTa2lwIHRoaXMgZGF5IGlmIHdlJ3ZlIHJlYWNoZWQgdGhlIGxpbWl0XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFRyeSBwcmVmZXJyZWQgdGltZSBzbG90cyBmaXJzdFxyXG4gICAgICBmb3IgKGNvbnN0IHRpbWVJbmRleCBvZiBwcmVmZXJyZWRUaW1lU2xvdHMpIHtcclxuICAgICAgICBpZiAod2Vla1NjaGVkdWxlW2RheUluZGV4XVt0aW1lSW5kZXhdID09PSBudWxsKSB7XHJcbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGlzIHdvdWxkIGNyZWF0ZSBiYWNrLXRvLWJhY2sgbGVzc29ucyAoYXZvaWQgaWYgcG9zc2libGUpXHJcbiAgICAgICAgICBjb25zdCBoYXNBZGphY2VudExlc3NvbiA9ICh0aW1lSW5kZXggPiAwICYmIHdlZWtTY2hlZHVsZVtkYXlJbmRleF1bdGltZUluZGV4IC0gMV0/LnN1YmplY3RJZCA9PT0gZW5yb2xsbWVudC5zdWJqZWN0SWQpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHRpbWVJbmRleCA8IFNUQU5EQVJEX1RJTUVfU0xPVFMubGVuZ3RoIC0gMSAmJiB3ZWVrU2NoZWR1bGVbZGF5SW5kZXhdW3RpbWVJbmRleCArIDFdPy5zdWJqZWN0SWQgPT09IGVucm9sbG1lbnQuc3ViamVjdElkKTtcclxuXHJcbiAgICAgICAgICAvLyBGb3Igc3ViamVjdHMgd2l0aCBtdWx0aXBsZSBsZXNzb25zIHBlciB3ZWVrLCBhbGxvdyBhZGphY2VudCBsZXNzb25zIGlmIG5lY2Vzc2FyeVxyXG4gICAgICAgICAgaWYgKCFoYXNBZGphY2VudExlc3NvbiB8fCBlbnJvbGxtZW50Lmxlc3NvbnNQZXJXZWVrID49IDQpIHtcclxuICAgICAgICAgICAgc3ViamVjdExlc3NvbkNvdW50c1tlbnJvbGxtZW50LnN1YmplY3RJZF0rKztcclxuICAgICAgICAgICAgc3ViamVjdERhaWx5Q291bnRbZW5yb2xsbWVudC5zdWJqZWN0SWRdW2RheUluZGV4XSsrO1xyXG5cclxuICAgICAgICAgICAgLy8gQ3JlYXRlIGxlc3NvbiBvYmplY3Qgd2l0aCBwbGFjZWhvbGRlciBsZXNzb24gbnVtYmVyICh3aWxsIGJlIGZpeGVkIGxhdGVyKVxyXG4gICAgICAgICAgICBjb25zdCBsZXNzb24gPSBjcmVhdGVMZXNzb25PYmplY3QoZW5yb2xsbWVudCwgMCwgZGF5SW5kZXgsIHRpbWVJbmRleCwgYWNhZGVtaWNXZWVrLCBzdHVkZW50R3JhZGUpO1xyXG4gICAgICAgICAgICB3ZWVrU2NoZWR1bGVbZGF5SW5kZXhdW3RpbWVJbmRleF0gPSBsZXNzb247XHJcbiAgICAgICAgICAgIHNjaGVkdWxlZCA9IHRydWU7XHJcbiAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHNjaGVkdWxlZCkgYnJlYWs7XHJcblxyXG4gICAgICAvLyBJZiBubyBwcmVmZXJyZWQgc2xvdCB3b3JrZWQsIHRyeSBhbnkgYXZhaWxhYmxlIHNsb3Qgb24gdGhpcyBkYXlcclxuICAgICAgZm9yIChsZXQgdGltZUluZGV4ID0gMDsgdGltZUluZGV4IDwgU1RBTkRBUkRfVElNRV9TTE9UUy5sZW5ndGg7IHRpbWVJbmRleCsrKSB7XHJcbiAgICAgICAgaWYgKHdlZWtTY2hlZHVsZVtkYXlJbmRleF1bdGltZUluZGV4XSA9PT0gbnVsbCkge1xyXG4gICAgICAgICAgc3ViamVjdExlc3NvbkNvdW50c1tlbnJvbGxtZW50LnN1YmplY3RJZF0rKztcclxuICAgICAgICAgIHN1YmplY3REYWlseUNvdW50W2Vucm9sbG1lbnQuc3ViamVjdElkXVtkYXlJbmRleF0rKztcclxuXHJcbiAgICAgICAgICAvLyBDcmVhdGUgbGVzc29uIG9iamVjdCB3aXRoIHBsYWNlaG9sZGVyIGxlc3NvbiBudW1iZXIgKHdpbGwgYmUgZml4ZWQgbGF0ZXIpXHJcbiAgICAgICAgICBjb25zdCBsZXNzb24gPSBjcmVhdGVMZXNzb25PYmplY3QoZW5yb2xsbWVudCwgMCwgZGF5SW5kZXgsIHRpbWVJbmRleCwgYWNhZGVtaWNXZWVrLCBzdHVkZW50R3JhZGUpO1xyXG4gICAgICAgICAgd2Vla1NjaGVkdWxlW2RheUluZGV4XVt0aW1lSW5kZXhdID0gbGVzc29uO1xyXG4gICAgICAgICAgc2NoZWR1bGVkID0gdHJ1ZTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgaWYgKHNjaGVkdWxlZCkgYnJlYWs7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKCFzY2hlZHVsZWQpIHtcclxuICAgICAgY29uc29sZS53YXJuKGBbVGltZXRhYmxlIEFQSV0gQ291bGQgbm90IHNjaGVkdWxlICR7ZW5yb2xsbWVudC5zdWJqZWN0TmFtZX0gbGVzc29uIC0gbm8gYXZhaWxhYmxlIHNsb3RzYCk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAvLyBOb3cgZml4IGxlc3NvbiBudW1iZXJpbmcgdG8gYmUgY2hyb25vbG9naWNhbFxyXG4gIGZpeExlc3Nvbk51bWJlcmluZ0Nocm9ub2xvZ2ljYWxseSh3ZWVrU2NoZWR1bGUsIGFjYWRlbWljV2Vlaywgc3R1ZGVudEdyYWRlKTtcclxuXHJcbiAgLy8gTG9nIHNjaGVkdWxpbmcgcmVzdWx0c1xyXG4gIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gU2NoZWR1bGVkIGxlc3NvbnMgYW5kIGZpeGVkIGNocm9ub2xvZ2ljYWwgbnVtYmVyaW5nYCk7XHJcbn1cclxuXHJcbi8vIEZpeCBsZXNzb24gbnVtYmVyaW5nIHRvIGZvbGxvdyBjaHJvbm9sb2dpY2FsIG9yZGVyIChkYXkgYW5kIHRpbWUgYmFzZWQpXHJcbmZ1bmN0aW9uIGZpeExlc3Nvbk51bWJlcmluZ0Nocm9ub2xvZ2ljYWxseShcclxuICB3ZWVrU2NoZWR1bGU6IChhbnkgfCBudWxsKVtdW10sXHJcbiAgYWNhZGVtaWNXZWVrOiBudW1iZXIsXHJcbiAgc3R1ZGVudEdyYWRlOiBzdHJpbmdcclxuKTogdm9pZCB7XHJcbiAgLy8gR3JvdXAgbGVzc29ucyBieSBzdWJqZWN0XHJcbiAgY29uc3Qgc3ViamVjdExlc3NvbnM6IFJlY29yZDxzdHJpbmcsIGFueVtdPiA9IHt9O1xyXG5cclxuICAvLyBDb2xsZWN0IGFsbCBsZXNzb25zIGZvciBlYWNoIHN1YmplY3RcclxuICBmb3IgKGxldCBkYXlJbmRleCA9IDA7IGRheUluZGV4IDwgREFZU19PRl9XRUVLLmxlbmd0aDsgZGF5SW5kZXgrKykge1xyXG4gICAgZm9yIChsZXQgdGltZUluZGV4ID0gMDsgdGltZUluZGV4IDwgU1RBTkRBUkRfVElNRV9TTE9UUy5sZW5ndGg7IHRpbWVJbmRleCsrKSB7XHJcbiAgICAgIGNvbnN0IGxlc3NvbiA9IHdlZWtTY2hlZHVsZVtkYXlJbmRleF1bdGltZUluZGV4XTtcclxuICAgICAgaWYgKGxlc3NvbiAmJiBsZXNzb24uc3ViamVjdElkICYmIGxlc3Nvbi5zdWJqZWN0SWQgIT09ICdmcmVlX3BlcmlvZCcpIHtcclxuICAgICAgICBpZiAoIXN1YmplY3RMZXNzb25zW2xlc3Nvbi5zdWJqZWN0SWRdKSB7XHJcbiAgICAgICAgICBzdWJqZWN0TGVzc29uc1tsZXNzb24uc3ViamVjdElkXSA9IFtdO1xyXG4gICAgICAgIH1cclxuICAgICAgICBzdWJqZWN0TGVzc29uc1tsZXNzb24uc3ViamVjdElkXS5wdXNoKHtcclxuICAgICAgICAgIGxlc3NvbixcclxuICAgICAgICAgIGRheUluZGV4LFxyXG4gICAgICAgICAgdGltZUluZGV4LFxyXG4gICAgICAgICAgcG9zaXRpb246IGRheUluZGV4ICogU1RBTkRBUkRfVElNRV9TTE9UUy5sZW5ndGggKyB0aW1lSW5kZXggLy8gRm9yIHNvcnRpbmdcclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gRm9yIGVhY2ggc3ViamVjdCwgc29ydCBsZXNzb25zIGNocm9ub2xvZ2ljYWxseSBhbmQgcmVhc3NpZ24gbGVzc29uIG51bWJlcnNcclxuICBPYmplY3QuZW50cmllcyhzdWJqZWN0TGVzc29ucykuZm9yRWFjaCgoW3N1YmplY3RJZCwgbGVzc29uc10pID0+IHtcclxuICAgIC8vIFNvcnQgYnkgY2hyb25vbG9naWNhbCBwb3NpdGlvbiAoZGF5IGZpcnN0LCB0aGVuIHRpbWUpXHJcbiAgICBsZXNzb25zLnNvcnQoKGEsIGIpID0+IGEucG9zaXRpb24gLSBiLnBvc2l0aW9uKTtcclxuXHJcbiAgICAvLyBSZWFzc2lnbiBsZXNzb24gbnVtYmVycyBjaHJvbm9sb2dpY2FsbHlcclxuICAgIGxlc3NvbnMuZm9yRWFjaCgobGVzc29uRGF0YSwgaW5kZXgpID0+IHtcclxuICAgICAgY29uc3QgeyBsZXNzb24sIGRheUluZGV4LCB0aW1lSW5kZXggfSA9IGxlc3NvbkRhdGE7XHJcbiAgICAgIGNvbnN0IG5ld0xlc3Nvbk51bWJlciA9IGluZGV4ICsgMTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB0aGUgbGVzc29uIG9iamVjdCB3aXRoIGNvcnJlY3QgbnVtYmVyaW5nXHJcbiAgICAgIGNvbnN0IGVucm9sbG1lbnQ6IEVucm9sbG1lbnQgPSB7XHJcbiAgICAgICAgc3ViamVjdElkOiBsZXNzb24uc3ViamVjdElkLFxyXG4gICAgICAgIHN1YmplY3ROYW1lOiBsZXNzb24uc3ViamVjdCxcclxuICAgICAgICBzdWJqZWN0Q29kZTogbGVzc29uLnN1YmplY3RDb2RlLFxyXG4gICAgICAgIGxlc3NvbnNQZXJXZWVrOiBsZXNzb25zLmxlbmd0aCxcclxuICAgICAgICBjb2duaXRpdmVMb2FkOiBsZXNzb24uY29nbml0aXZlTG9hZCxcclxuICAgICAgICBzdGF0dXM6ICdhY3RpdmUnLFxyXG4gICAgICAgIGVucm9sbGVkQXQ6IGxlc3Nvbi5lbnJvbGxlZEF0IHx8IG5ldyBEYXRlKClcclxuICAgICAgfTtcclxuXHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRMZXNzb24gPSBjcmVhdGVMZXNzb25PYmplY3QoXHJcbiAgICAgICAgZW5yb2xsbWVudCxcclxuICAgICAgICBuZXdMZXNzb25OdW1iZXIsXHJcbiAgICAgICAgZGF5SW5kZXgsXHJcbiAgICAgICAgdGltZUluZGV4LFxyXG4gICAgICAgIGFjYWRlbWljV2VlayxcclxuICAgICAgICBzdHVkZW50R3JhZGVcclxuICAgICAgKTtcclxuXHJcbiAgICAgIC8vIFJlcGxhY2UgdGhlIGxlc3NvbiBpbiB0aGUgc2NoZWR1bGVcclxuICAgICAgd2Vla1NjaGVkdWxlW2RheUluZGV4XVt0aW1lSW5kZXhdID0gdXBkYXRlZExlc3NvbjtcclxuICAgIH0pO1xyXG4gIH0pO1xyXG5cclxuICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIEZpeGVkIGxlc3NvbiBudW1iZXJpbmcgZm9yICR7T2JqZWN0LmtleXMoc3ViamVjdExlc3NvbnMpLmxlbmd0aH0gc3ViamVjdHNgKTtcclxufVxyXG5cclxuLy8gR2V0IHByZWZlcnJlZCB0aW1lIHNsb3RzIGJhc2VkIG9uIGNvZ25pdGl2ZSBsb2FkXHJcbmZ1bmN0aW9uIGdldFByZWZlcnJlZFRpbWVTbG90cyhjb2duaXRpdmVMb2FkOiBudW1iZXIpOiBudW1iZXJbXSB7XHJcbiAgaWYgKGNvZ25pdGl2ZUxvYWQgPj0gOCkge1xyXG4gICAgLy8gSGlnaCBjb2duaXRpdmUgbG9hZDogcHJlZmVyIGVhcmx5IG1vcm5pbmcgc2xvdHNcclxuICAgIHJldHVybiBbMCwgMSwgMiwgMywgNCwgNSwgNl07XHJcbiAgfSBlbHNlIGlmIChjb2duaXRpdmVMb2FkID49IDYpIHtcclxuICAgIC8vIE1lZGl1bSBjb2duaXRpdmUgbG9hZDogcHJlZmVyIG1pZC1tb3JuaW5nIHNsb3RzXHJcbiAgICByZXR1cm4gWzEsIDIsIDMsIDAsIDQsIDUsIDZdO1xyXG4gIH0gZWxzZSBpZiAoY29nbml0aXZlTG9hZCA+PSA0KSB7XHJcbiAgICAvLyBMb3ctbWVkaXVtIGNvZ25pdGl2ZSBsb2FkOiBwcmVmZXIgYWZ0ZXJub29uIHNsb3RzXHJcbiAgICByZXR1cm4gWzMsIDQsIDUsIDIsIDYsIDEsIDBdO1xyXG4gIH0gZWxzZSB7XHJcbiAgICAvLyBMb3cgY29nbml0aXZlIGxvYWQ6IHByZWZlciBsYXRlIHNsb3RzXHJcbiAgICByZXR1cm4gWzUsIDYsIDQsIDMsIDIsIDEsIDBdO1xyXG4gIH1cclxufVxyXG5cclxuLy8gQ3JlYXRlIGEgbGVzc29uIG9iamVjdFxyXG5mdW5jdGlvbiBjcmVhdGVMZXNzb25PYmplY3QoXHJcbiAgZW5yb2xsbWVudDogRW5yb2xsbWVudCxcclxuICBsZXNzb25OdW1iZXI6IG51bWJlcixcclxuICBkYXlJbmRleDogbnVtYmVyLFxyXG4gIHRpbWVJbmRleDogbnVtYmVyLFxyXG4gIGFjYWRlbWljV2VlazogbnVtYmVyLFxyXG4gIHN0dWRlbnRHcmFkZTogc3RyaW5nXHJcbik6IGFueSB7XHJcbiAgY29uc3QgbGVzc29uUmVmZXJlbmNlID0gZ2VuZXJhdGVMZXNzb25SZWZlcmVuY2UoXHJcbiAgICBzdHVkZW50R3JhZGUsXHJcbiAgICBlbnJvbGxtZW50LnN1YmplY3RDb2RlLFxyXG4gICAgYWNhZGVtaWNXZWVrLFxyXG4gICAgbGVzc29uTnVtYmVyLFxyXG4gICAgZW5yb2xsbWVudC5sZXNzb25zUGVyV2Vla1xyXG4gICk7XHJcblxyXG4gIC8vIERlZmF1bHQgYWxsIGxlc3NvbnMgdG8gXCJ1cGNvbWluZ1wiIHN0YXR1c1xyXG4gIC8vIEFjdHVhbCBjb21wbGV0aW9uIHN0YXR1cyB3aWxsIGJlIGFwcGxpZWQgbGF0ZXIgZnJvbSBGaXJlc3RvcmUgZGF0YVxyXG4gIGNvbnN0IHN0YXR1cyA9ICd1cGNvbWluZyc7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBpZDogYCR7bGVzc29uUmVmZXJlbmNlfV93ZWVrJHthY2FkZW1pY1dlZWt9YCxcclxuICAgIGxlc3NvblJlZmVyZW5jZTogbGVzc29uUmVmZXJlbmNlLFxyXG4gICAgdGl0bGU6IGAke2Vucm9sbG1lbnQuc3ViamVjdE5hbWV9IC0gV2VlayAke2FjYWRlbWljV2Vla30sIExlc3NvbiAke2xlc3Nvbk51bWJlcn1gLFxyXG4gICAgc3ViamVjdDogZW5yb2xsbWVudC5zdWJqZWN0TmFtZSxcclxuICAgIHN1YmplY3RJZDogZW5yb2xsbWVudC5zdWJqZWN0SWQsXHJcbiAgICBzdWJqZWN0Q29kZTogZW5yb2xsbWVudC5zdWJqZWN0Q29kZSxcclxuICAgIHRpbWU6IFNUQU5EQVJEX1RJTUVfU0xPVFNbdGltZUluZGV4XSxcclxuICAgIGRheTogREFZU19PRl9XRUVLW2RheUluZGV4XSxcclxuICAgIGR1cmF0aW9uOiA0NSxcclxuICAgIHN0YXR1czogc3RhdHVzLFxyXG4gICAgZGVzY3JpcHRpb246IGBXZWVrICR7YWNhZGVtaWNXZWVrfSBsZXNzb24gJHtsZXNzb25OdW1iZXJ9IGZvciAke2Vucm9sbG1lbnQuc3ViamVjdE5hbWV9YCxcclxuICAgIGdyYWRlOiBzdHVkZW50R3JhZGUsXHJcbiAgICBhY2FkZW1pY1dlZWs6IGFjYWRlbWljV2VlayxcclxuICAgIGxlc3Nvbk51bWJlckluV2VlazogbGVzc29uTnVtYmVyLFxyXG4gICAgYWJzb2x1dGVMZXNzb25OdW1iZXI6ICgoYWNhZGVtaWNXZWVrIC0gMSkgKiBlbnJvbGxtZW50Lmxlc3NvbnNQZXJXZWVrKSArIGxlc3Nvbk51bWJlcixcclxuICAgIHRvdGFsV2Vla3M6IDMwLFxyXG4gICAgdGVhY2hlcjogJ0FJIEluc3RydWN0b3InLFxyXG4gICAgY29nbml0aXZlTG9hZDogZW5yb2xsbWVudC5jb2duaXRpdmVMb2FkLFxyXG4gICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxyXG4gIH07XHJcbn1cclxuXHJcbi8vIEFwcGx5IGxlc3NvbiBjb21wbGV0aW9uIHN0YXRlcyBmcm9tIEZpcmVzdG9yZSB0byB0aGUgZ2VuZXJhdGVkIGxlc3NvbnNcclxuZnVuY3Rpb24gYXBwbHlMZXNzb25Db21wbGV0aW9uU3RhdGVzKFxyXG4gIGxlc3NvbnM6IGFueVtdLFxyXG4gIGNvbXBsZXRpb25TdGF0ZXM6IFJlY29yZDxzdHJpbmcsIGFueT5cclxuKTogYW55W10ge1xyXG4gIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gQXBwbHlpbmcgY29tcGxldGlvbiBzdGF0ZXMgdG8gJHtsZXNzb25zLmxlbmd0aH0gbGVzc29uc2ApO1xyXG5cclxuICBjb25zdCB1cGRhdGVkTGVzc29ucyA9IGxlc3NvbnMubWFwKGxlc3NvbiA9PiB7XHJcbiAgICBpZiAoIWxlc3Nvbi5sZXNzb25SZWZlcmVuY2UpIHtcclxuICAgICAgcmV0dXJuIGxlc3NvbjsgLy8gU2tpcCBsZXNzb25zIHdpdGhvdXQgbGVzc29uIHJlZmVyZW5jZXMgKGUuZy4sIGZyZWUgcGVyaW9kcylcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBpZiB0aGlzIGxlc3NvbiBoYXMgY29tcGxldGlvbiBkYXRhXHJcbiAgICBjb25zdCBjb21wbGV0aW9uRGF0YSA9IGNvbXBsZXRpb25TdGF0ZXNbbGVzc29uLmxlc3NvblJlZmVyZW5jZV07XHJcblxyXG4gICAgaWYgKGNvbXBsZXRpb25EYXRhKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKGBbVGltZXRhYmxlIEFQSV0gRm91bmQgY29tcGxldGlvbiBkYXRhIGZvciAke2xlc3Nvbi5sZXNzb25SZWZlcmVuY2V9OmAsIGNvbXBsZXRpb25EYXRhKTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSBsZXNzb24gd2l0aCBhY3R1YWwgY29tcGxldGlvbiBzdGF0dXNcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICAuLi5sZXNzb24sXHJcbiAgICAgICAgc3RhdHVzOiBjb21wbGV0aW9uRGF0YS5zdGF0dXMgfHwgbGVzc29uLnN0YXR1cyxcclxuICAgICAgICBjb21wbGV0ZWQ6IGNvbXBsZXRpb25EYXRhLmNvbXBsZXRlZCB8fCBmYWxzZSxcclxuICAgICAgICBjb21wbGV0ZWRBdDogY29tcGxldGlvbkRhdGEuY29tcGxldGVkQXQsXHJcbiAgICAgICAgcHJvZ3Jlc3M6IGNvbXBsZXRpb25EYXRhLnByb2dyZXNzIHx8IDAsXHJcbiAgICAgICAgLy8gUHJlc2VydmUgYW55IGFkZGl0aW9uYWwgY29tcGxldGlvbiBtZXRhZGF0YVxyXG4gICAgICAgIGNvbXBsZXRpb25EYXRhOiBjb21wbGV0aW9uRGF0YVxyXG4gICAgICB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vIGNvbXBsZXRpb24gZGF0YSBmb3VuZCwga2VlcCBhcyBcInVwY29taW5nXCJcclxuICAgIHJldHVybiBsZXNzb247XHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IGNvbXBsZXRlZENvdW50ID0gdXBkYXRlZExlc3NvbnMuZmlsdGVyKGwgPT4gbC5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKS5sZW5ndGg7XHJcbiAgY29uc3QgaW5Qcm9ncmVzc0NvdW50ID0gdXBkYXRlZExlc3NvbnMuZmlsdGVyKGwgPT4gbC5zdGF0dXMgPT09ICdpbl9wcm9ncmVzcycpLmxlbmd0aDtcclxuICBjb25zdCB1cGNvbWluZ0NvdW50ID0gdXBkYXRlZExlc3NvbnMuZmlsdGVyKGwgPT4gbC5zdGF0dXMgPT09ICd1cGNvbWluZycpLmxlbmd0aDtcclxuXHJcbiAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBMZXNzb24gc3RhdHVzIHN1bW1hcnk6ICR7Y29tcGxldGVkQ291bnR9IGNvbXBsZXRlZCwgJHtpblByb2dyZXNzQ291bnR9IGluIHByb2dyZXNzLCAke3VwY29taW5nQ291bnR9IHVwY29taW5nYCk7XHJcblxyXG4gIHJldHVybiB1cGRhdGVkTGVzc29ucztcclxufVxyXG5cclxuLy8gR2V0IHN0dWRlbnQgZ3JhZGUgbGV2ZWxcclxuYXN5bmMgZnVuY3Rpb24gZ2V0U3R1ZGVudEdyYWRlKHN0dWRlbnRJZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcclxuICB0cnkge1xyXG4gICAgLy8gVHJ5IGRpZmZlcmVudCBzdHVkZW50IElEIGZvcm1hdHNcclxuICAgIGNvbnN0IHN0dWRlbnRJZEZvcm1hdHMgPSBbXHJcbiAgICAgIHN0dWRlbnRJZCxcclxuICAgICAgYGFuZHJlYV91Z29ub18zMzMwNWAsXHJcbiAgICAgIHN0dWRlbnRJZC50b0xvd2VyQ2FzZSgpLFxyXG4gICAgICBzdHVkZW50SWQucmVwbGFjZSgvXFxzKy9nLCAnXycpLnRvTG93ZXJDYXNlKClcclxuICAgIF07XHJcblxyXG4gICAgZm9yIChjb25zdCBmb3JtYXQgb2Ygc3R1ZGVudElkRm9ybWF0cykge1xyXG4gICAgICBjb25zdCBzdHVkZW50UmVmOiBhbnkgPSBkYiEuY29sbGVjdGlvbignc3R1ZGVudHMnKS5kb2MoZm9ybWF0KTtcclxuICAgICAgY29uc3Qgc3R1ZGVudFNuYXAgPSBhd2FpdCBzdHVkZW50UmVmLmdldCgpO1xyXG5cclxuICAgICAgaWYgKHN0dWRlbnRTbmFwLmV4aXN0cykge1xyXG4gICAgICAgIGNvbnN0IHN0dWRlbnREYXRhID0gc3R1ZGVudFNuYXAuZGF0YSgpO1xyXG4gICAgICAgIHJldHVybiBzdHVkZW50RGF0YT8uZ3JhZGVMZXZlbCB8fCBzdHVkZW50RGF0YT8uZ3JhZGUgfHwgJ1A1JztcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiAnUDUnOyAvLyBEZWZhdWx0IGdyYWRlXHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ1tUaW1ldGFibGUgQVBJXSBFcnJvciBmZXRjaGluZyBzdHVkZW50IGdyYWRlOicsIGVycm9yKTtcclxuICAgIHJldHVybiAnUDUnO1xyXG4gIH1cclxufVxyXG5cclxuLy8gRmV0Y2ggbGVzc29uIGNvbXBsZXRpb24gc3RhdGVzIGZyb20gRmlyZXN0b3JlXHJcbmFzeW5jIGZ1bmN0aW9uIGZldGNoTGVzc29uQ29tcGxldGlvblN0YXRlcyhzdHVkZW50SWQ6IHN0cmluZyk6IFByb21pc2U8UmVjb3JkPHN0cmluZywgYW55Pj4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIEZldGNoaW5nIGxlc3NvbiBjb21wbGV0aW9uIHN0YXRlcyBmb3Igc3R1ZGVudDogJHtzdHVkZW50SWR9YCk7XHJcblxyXG4gICAgLy8gVHJ5IGRpZmZlcmVudCBzdHVkZW50IElEIGZvcm1hdHMgZm9yIGxlc3Nvbl9zdGF0ZXMgY29sbGVjdGlvblxyXG4gICAgY29uc3Qgc3R1ZGVudElkRm9ybWF0cyA9IFtcclxuICAgICAgc3R1ZGVudElkLFxyXG4gICAgICBgYW5kcmVhX3Vnb25vXzMzMzA1YCxcclxuICAgICAgc3R1ZGVudElkLnRvTG93ZXJDYXNlKCksXHJcbiAgICAgIHN0dWRlbnRJZC5yZXBsYWNlKC9cXHMrL2csICdfJykudG9Mb3dlckNhc2UoKVxyXG4gICAgXTtcclxuXHJcbiAgICBjb25zdCBjb21wbGV0aW9uU3RhdGVzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0ge307XHJcblxyXG4gICAgZm9yIChjb25zdCBmb3JtYXQgb2Ygc3R1ZGVudElkRm9ybWF0cykge1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGxlc3NvblN0YXRlc1JlZiA9IGRiIS5jb2xsZWN0aW9uKCdsZXNzb25fc3RhdGVzJykuZG9jKGZvcm1hdCk7XHJcbiAgICAgICAgY29uc3QgbGVzc29uU3RhdGVzU25hcCA9IGF3YWl0IGxlc3NvblN0YXRlc1JlZi5nZXQoKTtcclxuXHJcbiAgICAgICAgaWYgKGxlc3NvblN0YXRlc1NuYXAuZXhpc3RzKSB7XHJcbiAgICAgICAgICBjb25zdCBzdGF0ZXNEYXRhID0gbGVzc29uU3RhdGVzU25hcC5kYXRhKCk7XHJcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIEZvdW5kIGxlc3NvbiBzdGF0ZXMgZm9yIHN0dWRlbnQgSUQgZm9ybWF0OiAke2Zvcm1hdH1gKTtcclxuXHJcbiAgICAgICAgICAvLyBFeHRyYWN0IGxlc3NvbiBjb21wbGV0aW9uIGRhdGFcclxuICAgICAgICAgIGlmIChzdGF0ZXNEYXRhKSB7XHJcbiAgICAgICAgICAgIE9iamVjdC5lbnRyaWVzKHN0YXRlc0RhdGEpLmZvckVhY2goKFtsZXNzb25SZWYsIGxlc3NvbkRhdGFdOiBbc3RyaW5nLCBhbnldKSA9PiB7XHJcbiAgICAgICAgICAgICAgaWYgKGxlc3NvbkRhdGEgJiYgdHlwZW9mIGxlc3NvbkRhdGEgPT09ICdvYmplY3QnKSB7XHJcbiAgICAgICAgICAgICAgICAvLyBTdG9yZSB0aGUgY29tcGxldGlvbiBzdGF0ZSB3aXRoIGxlc3NvbiByZWZlcmVuY2UgYXMga2V5XHJcbiAgICAgICAgICAgICAgICBjb21wbGV0aW9uU3RhdGVzW2xlc3NvblJlZl0gPSB7XHJcbiAgICAgICAgICAgICAgICAgIHN0YXR1czogbGVzc29uRGF0YS5zdGF0dXMgfHwgbGVzc29uRGF0YS5jb21wbGV0aW9uX3N0YXR1cyB8fCAndXBjb21pbmcnLFxyXG4gICAgICAgICAgICAgICAgICBjb21wbGV0ZWQ6IGxlc3NvbkRhdGEuY29tcGxldGVkIHx8IGZhbHNlLFxyXG4gICAgICAgICAgICAgICAgICBjb21wbGV0ZWRBdDogbGVzc29uRGF0YS5jb21wbGV0ZWRBdCB8fCBsZXNzb25EYXRhLmNvbXBsZXRlZF9hdCxcclxuICAgICAgICAgICAgICAgICAgcHJvZ3Jlc3M6IGxlc3NvbkRhdGEucHJvZ3Jlc3MgfHwgMCxcclxuICAgICAgICAgICAgICAgICAgLi4ubGVzc29uRGF0YVxyXG4gICAgICAgICAgICAgICAgfTtcclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGJyZWFrOyAvLyBGb3VuZCBkYXRhLCBubyBuZWVkIHRvIHRyeSBvdGhlciBmb3JtYXRzXHJcbiAgICAgICAgfVxyXG4gICAgICB9IGNhdGNoIChmb3JtYXRFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUud2FybihgW1RpbWV0YWJsZSBBUEldIEVycm9yIGNoZWNraW5nIGxlc3NvbiBzdGF0ZXMgZm9yIGZvcm1hdCAke2Zvcm1hdH06YCwgZm9ybWF0RXJyb3IpO1xyXG4gICAgICAgIGNvbnRpbnVlO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBGb3VuZCAke09iamVjdC5rZXlzKGNvbXBsZXRpb25TdGF0ZXMpLmxlbmd0aH0gbGVzc29uIGNvbXBsZXRpb24gcmVjb3Jkc2ApO1xyXG4gICAgcmV0dXJuIGNvbXBsZXRpb25TdGF0ZXM7XHJcblxyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdbVGltZXRhYmxlIEFQSV0gRXJyb3IgZmV0Y2hpbmcgbGVzc29uIGNvbXBsZXRpb24gc3RhdGVzOicsIGVycm9yKTtcclxuICAgIHJldHVybiB7fTtcclxuICB9XHJcbn1cclxuXHJcbi8vIFZlcmlmeSBGaXJlYmFzZSBBdXRoIHRva2VuXHJcbmFzeW5jIGZ1bmN0aW9uIHZlcmlmeUF1dGhUb2tlbihyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IFByb21pc2U8eyB1aWQ6IHN0cmluZyB9IHwgbnVsbD4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBhdXRoSGVhZGVyID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpO1xyXG4gICAgaWYgKCFhdXRoSGVhZGVyPy5zdGFydHNXaXRoKCdCZWFyZXIgJykpIHtcclxuICAgICAgcmV0dXJuIG51bGw7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgaWRUb2tlbiA9IGF1dGhIZWFkZXIuc3BsaXQoJ0JlYXJlciAnKVsxXTtcclxuICAgIGNvbnN0IGRlY29kZWRUb2tlbiA9IGF3YWl0IGdldEF1dGgoKS52ZXJpZnlJZFRva2VuKGlkVG9rZW4pO1xyXG4gICAgcmV0dXJuIHsgdWlkOiBkZWNvZGVkVG9rZW4udWlkIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggdmVyaWZpY2F0aW9uIGZhaWxlZDonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbi8vIEZpbGwgYW55IHJlbWFpbmluZyBlbXB0eSBzbG90cyB3aXRoIFwiRnJlZSBQZXJpb2RcIlxyXG5mdW5jdGlvbiBmaWxsRW1wdHlTbG90c1dpdGhGcmVlUGVyaW9kcyhcclxuICB3ZWVrU2NoZWR1bGU6IChhbnkgfCBudWxsKVtdW10sXHJcbiAgYWNhZGVtaWNXZWVrOiBudW1iZXIsXHJcbiAgc3R1ZGVudEdyYWRlOiBzdHJpbmdcclxuKTogdm9pZCB7XHJcbiAgY29uc3QgYWJicmV2aWF0ZWRHcmFkZSA9IGNvbnZlcnRHcmFkZVRvQWJicmV2aWF0aW9uKHN0dWRlbnRHcmFkZSk7XHJcbiAgbGV0IGZyZWVQZXJpb2RDb3VudGVyID0gMTtcclxuXHJcbiAgZm9yIChsZXQgZGF5SW5kZXggPSAwOyBkYXlJbmRleCA8IERBWVNfT0ZfV0VFSy5sZW5ndGg7IGRheUluZGV4KyspIHtcclxuICAgIGZvciAobGV0IHRpbWVJbmRleCA9IDA7IHRpbWVJbmRleCA8IFNUQU5EQVJEX1RJTUVfU0xPVFMubGVuZ3RoOyB0aW1lSW5kZXgrKykge1xyXG4gICAgICBpZiAod2Vla1NjaGVkdWxlW2RheUluZGV4XVt0aW1lSW5kZXhdID09PSBudWxsKSB7XHJcbiAgICAgICAgY29uc3QgZnJlZVBlcmlvZCA9IHtcclxuICAgICAgICAgIGlkOiBgZnJlZV9wZXJpb2Rfd2VlayR7YWNhZGVtaWNXZWVrfV8ke0RBWVNfT0ZfV0VFS1tkYXlJbmRleF19XyR7dGltZUluZGV4fWAsXHJcbiAgICAgICAgICBsZXNzb25SZWZlcmVuY2U6IGAke2FiYnJldmlhdGVkR3JhZGV9LSR7U3RyaW5nKGFjYWRlbWljV2VlaykucGFkU3RhcnQoMywgJzAnKX0tJHtTdHJpbmcoZnJlZVBlcmlvZENvdW50ZXIpLnBhZFN0YXJ0KDMsICcwJyl9YCxcclxuICAgICAgICAgIHRpdGxlOiAnRnJlZSBQZXJpb2QnLFxyXG4gICAgICAgICAgc3ViamVjdDogJ0ZyZWUgUGVyaW9kJyxcclxuICAgICAgICAgIHN1YmplY3RJZDogJ2ZyZWVfcGVyaW9kJyxcclxuICAgICAgICAgIHN1YmplY3RDb2RlOiBudWxsLCAvLyBSZW1vdmUgc3ViamVjdCBjb2RlIGZvciBmcmVlIHBlcmlvZHNcclxuICAgICAgICAgIHRpbWU6IFNUQU5EQVJEX1RJTUVfU0xPVFNbdGltZUluZGV4XSxcclxuICAgICAgICAgIGRheTogREFZU19PRl9XRUVLW2RheUluZGV4XSxcclxuICAgICAgICAgIGR1cmF0aW9uOiA0NSxcclxuICAgICAgICAgIHN0YXR1czogJ3VwY29taW5nJyxcclxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnRnJlZSBzdHVkeSBwZXJpb2QnLFxyXG4gICAgICAgICAgZ3JhZGU6IHN0dWRlbnRHcmFkZSxcclxuICAgICAgICAgIGFjYWRlbWljV2VlazogYWNhZGVtaWNXZWVrLFxyXG4gICAgICAgICAgbGVzc29uTnVtYmVySW5XZWVrOiBmcmVlUGVyaW9kQ291bnRlcixcclxuICAgICAgICAgIGFic29sdXRlTGVzc29uTnVtYmVyOiBudWxsLFxyXG4gICAgICAgICAgdG90YWxXZWVrczogMzAsXHJcbiAgICAgICAgICB0ZWFjaGVyOiBudWxsLFxyXG4gICAgICAgICAgY29nbml0aXZlTG9hZDogMSwgLy8gTG93ZXN0IGNvZ25pdGl2ZSBsb2FkXHJcbiAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgIHVwZGF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgICAgaXNGcmVlUGVyaW9kOiB0cnVlXHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgd2Vla1NjaGVkdWxlW2RheUluZGV4XVt0aW1lSW5kZXhdID0gZnJlZVBlcmlvZDtcclxuICAgICAgICBmcmVlUGVyaW9kQ291bnRlcisrO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIENoZWNrIGlmIEZpcmViYXNlIEFkbWluIGlzIHByb3Blcmx5IGluaXRpYWxpemVkXHJcbiAgICBpZiAoIWRiKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tUaW1ldGFibGUgQVBJXSBGaXJlYmFzZSBBZG1pbiBub3QgcHJvcGVybHkgaW5pdGlhbGl6ZWQnKTtcclxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxyXG4gICAgICAgIHsgXHJcbiAgICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXHJcbiAgICAgICAgICBlcnJvcjogJ0RhdGFiYXNlIG5vdCBhdmFpbGFibGUnXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7IHN0YXR1czogNTAwIH1cclxuICAgICAgKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBGb3IgZGV2ZWxvcG1lbnQvdGVzdGluZywgYWxsb3cgdW5hdXRoZW50aWNhdGVkIHJlcXVlc3RzXHJcbiAgICAvLyBJbiBwcm9kdWN0aW9uLCB1bmNvbW1lbnQgdGhlIGF1dGhlbnRpY2F0aW9uIGNoZWNrIGJlbG93XHJcbiAgICAvKlxyXG4gICAgY29uc3QgYXV0aFJlc3VsdCA9IGF3YWl0IHZlcmlmeUF1dGhUb2tlbihyZXF1ZXN0KTtcclxuICAgIGlmICghYXV0aFJlc3VsdCkge1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcbiAgICAqL1xyXG5cclxuICAgIGNvbnN0IHsgc2VhcmNoUGFyYW1zIH0gPSBuZXcgVVJMKHJlcXVlc3QudXJsKTtcclxuICAgIGNvbnN0IHN0dWRlbnRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3N0dWRlbnRJZCcpO1xyXG4gICAgY29uc3QgZGF0ZSA9IHNlYXJjaFBhcmFtcy5nZXQoJ2RhdGUnKTtcclxuICAgIGNvbnN0IHdlZWtQYXJhbSA9IHNlYXJjaFBhcmFtcy5nZXQoJ3dlZWsnKTtcclxuICAgIFxyXG4gICAgLy8gUGFyc2Ugd2VlayBwYXJhbWV0ZXIgKGRlZmF1bHQgdG8gd2VlayAxKVxyXG4gICAgY29uc3QgYWNhZGVtaWNXZWVrID0gd2Vla1BhcmFtID8gcGFyc2VJbnQod2Vla1BhcmFtLCAxMCkgOiAxO1xyXG4gICAgXHJcbiAgICBpZiAoaXNOYU4oYWNhZGVtaWNXZWVrKSB8fCBhY2FkZW1pY1dlZWsgPCAxIHx8IGFjYWRlbWljV2VlayA+IDMwKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ludmFsaWQgd2VlayBudW1iZXIuIE11c3QgYmUgYmV0d2VlbiAxIGFuZCAzMC4nIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIGlmICghc3R1ZGVudElkKSB7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1N0dWRlbnQgSUQgaXMgcmVxdWlyZWQnIH0sXHJcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XHJcbiAgICAgICk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBGZXRjaGluZyB0aW1ldGFibGUgZm9yIHN0dWRlbnQ6ICR7c3R1ZGVudElkfSwgd2VlazogJHthY2FkZW1pY1dlZWt9LCBkYXRlOiAke2RhdGV9YCk7XHJcblxyXG4gICAgLy8gRmV0Y2ggc3R1ZGVudCBncmFkZSBsZXZlbFxyXG4gICAgY29uc3Qgc3R1ZGVudEdyYWRlID0gYXdhaXQgZ2V0U3R1ZGVudEdyYWRlKHN0dWRlbnRJZCk7XHJcbiAgICBjb25zb2xlLmxvZyhgW1RpbWV0YWJsZSBBUEldIFN0dWRlbnQgZ3JhZGU6ICR7c3R1ZGVudEdyYWRlfWApO1xyXG5cclxuICAgIC8vIEZldGNoIHN0dWRlbnQgZW5yb2xsbWVudHMgKHRoaXMgaXMgbm93IHRoZSBzaW5nbGUgc291cmNlIG9mIHRydXRoKVxyXG4gICAgY29uc3QgZW5yb2xsbWVudHMgPSBhd2FpdCBmZXRjaFN0dWRlbnRFbnJvbGxtZW50cyhzdHVkZW50SWQpO1xyXG4gICAgXHJcbiAgICBpZiAoZW5yb2xsbWVudHMubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdbVGltZXRhYmxlIEFQSV0gTm8gYWN0aXZlIGVucm9sbG1lbnRzIGZvdW5kJyk7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IFxyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgICAgZGF0YToge1xyXG4gICAgICAgICAgc2NoZWR1bGU6IFtdLFxyXG4gICAgICAgICAgdGltZXRhYmxlOiBbXSxcclxuICAgICAgICAgIHRvdGFsTGVzc29uczogMCxcclxuICAgICAgICAgIGVucm9sbG1lbnRzOiBbXSxcclxuICAgICAgICAgIHN0dWRlbnRfaWQ6IHN0dWRlbnRJZCxcclxuICAgICAgICAgIGFjYWRlbWljX3dlZWs6IGFjYWRlbWljV2VlayxcclxuICAgICAgICAgIHRvdGFsX3dlZWtzOiAzMCxcclxuICAgICAgICAgIHN0dWRlbnRfZ3JhZGU6IHN0dWRlbnRHcmFkZSxcclxuICAgICAgICAgIGRhdGU6IGRhdGVcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEdlbmVyYXRlIHdlZWtseSB0aW1ldGFibGUgYmFzZWQgb24gZW5yb2xsbWVudHNcclxuICAgIGNvbnN0IHdlZWtseVNjaGVkdWxlID0gZGlzdHJpYnV0ZUxlc3NvbnNBY3Jvc3NXZWVrKGVucm9sbG1lbnRzLCBhY2FkZW1pY1dlZWssIHN0dWRlbnRHcmFkZSk7XHJcblxyXG4gICAgLy8gRmV0Y2ggbGVzc29uIGNvbXBsZXRpb24gc3RhdGVzIGZyb20gRmlyZXN0b3JlXHJcbiAgICBjb25zdCBjb21wbGV0aW9uU3RhdGVzID0gYXdhaXQgZmV0Y2hMZXNzb25Db21wbGV0aW9uU3RhdGVzKHN0dWRlbnRJZCk7XHJcblxyXG4gICAgLy8gQXBwbHkgYWN0dWFsIGNvbXBsZXRpb24gc3RhdGVzIHRvIHRoZSBnZW5lcmF0ZWQgbGVzc29uc1xyXG4gICAgY29uc3Qgc2NoZWR1bGVXaXRoQ29tcGxldGlvblN0YXRlcyA9IGFwcGx5TGVzc29uQ29tcGxldGlvblN0YXRlcyh3ZWVrbHlTY2hlZHVsZSwgY29tcGxldGlvblN0YXRlcyk7XHJcblxyXG4gICAgLy8gU29ydCB3ZWVrbHkgc2NoZWR1bGUgYnkgZGF5IGFuZCB0aW1lXHJcbiAgICBjb25zdCBkYXlPcmRlciA9IFsnTW9uZGF5JywgJ1R1ZXNkYXknLCAnV2VkbmVzZGF5JywgJ1RodXJzZGF5JywgJ0ZyaWRheSddO1xyXG4gICAgc2NoZWR1bGVXaXRoQ29tcGxldGlvblN0YXRlcy5zb3J0KChhLCBiKSA9PiB7XHJcbiAgICAgIGNvbnN0IGRheUEgPSBkYXlPcmRlci5pbmRleE9mKGEuZGF5KTtcclxuICAgICAgY29uc3QgZGF5QiA9IGRheU9yZGVyLmluZGV4T2YoYi5kYXkpO1xyXG5cclxuICAgICAgaWYgKGRheUEgIT09IGRheUIpIHtcclxuICAgICAgICByZXR1cm4gZGF5QSAtIGRheUI7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIFNvcnQgYnkgdGltZSB3aXRoaW4gdGhlIHNhbWUgZGF5XHJcbiAgICAgIGlmIChhLnRpbWUgJiYgYi50aW1lKSB7XHJcbiAgICAgICAgcmV0dXJuIGEudGltZS5sb2NhbGVDb21wYXJlKGIudGltZSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiAwO1xyXG4gICAgfSk7XHJcblxyXG4gICAgY29uc29sZS5sb2coYFtUaW1ldGFibGUgQVBJXSBSZXR1cm5pbmcgJHtzY2hlZHVsZVdpdGhDb21wbGV0aW9uU3RhdGVzLmxlbmd0aH0gbGVzc29ucyBmb3Igd2VlayAke2FjYWRlbWljV2Vla31gKTtcclxuICAgIFxyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgZGF0YToge1xyXG4gICAgICAgIHNjaGVkdWxlOiBzY2hlZHVsZVdpdGhDb21wbGV0aW9uU3RhdGVzLFxyXG4gICAgICAgIHRpbWV0YWJsZTogc2NoZWR1bGVXaXRoQ29tcGxldGlvblN0YXRlcywgLy8gS2VlcCBib3RoIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XHJcbiAgICAgICAgdG90YWxMZXNzb25zOiBzY2hlZHVsZVdpdGhDb21wbGV0aW9uU3RhdGVzLmxlbmd0aCxcclxuICAgICAgICBlbnJvbGxtZW50czogZW5yb2xsbWVudHMsXHJcbiAgICAgICAgc3R1ZGVudF9pZDogc3R1ZGVudElkLFxyXG4gICAgICAgIGFjYWRlbWljX3dlZWs6IGFjYWRlbWljV2VlayxcclxuICAgICAgICB0b3RhbF93ZWVrczogMzAsXHJcbiAgICAgICAgc3R1ZGVudF9ncmFkZTogc3R1ZGVudEdyYWRlLFxyXG4gICAgICAgIGRhdGU6IGRhdGVcclxuICAgICAgfVxyXG4gICAgfSk7XHJcblxyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdbVGltZXRhYmxlIEFQSV0gRXJyb3I6JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxyXG4gICAgICB7IFxyXG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLCBcclxuICAgICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZldGNoIHRpbWV0YWJsZScsXHJcbiAgICAgICAgZGV0YWlsczogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLnN0YWNrIDogJ1Vua25vd24gZXJyb3InXHJcbiAgICAgIH0sXHJcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxyXG4gICAgKTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImRiIiwiZ2V0QXV0aCIsIlNUQU5EQVJEX1RJTUVfU0xPVFMiLCJEQVlTX09GX1dFRUsiLCJTVUJKRUNUX0NPR05JVElWRV9MT0FEUyIsImdlbmVyYXRlTGVzc29uUmVmZXJlbmNlIiwiZ3JhZGUiLCJzdWJqZWN0Q29kZSIsImFjYWRlbWljV2VlayIsImxlc3Nvbk51bWJlciIsImxlc3NvbnNQZXJXZWVrIiwiYWJzb2x1dGVMZXNzb25OdW1iZXIiLCJmb3JtYXR0ZWRMZXNzb25OdW1iZXIiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiYWJicmV2aWF0ZWRHcmFkZSIsImNvbnZlcnRHcmFkZVRvQWJicmV2aWF0aW9uIiwiZ3JhZGVTdHIiLCJ0b0xvd2VyQ2FzZSIsInRyaW0iLCJpbmNsdWRlcyIsInN0YXJ0c1dpdGgiLCJtYXRjaCIsInRlc3QiLCJ0b1VwcGVyQ2FzZSIsImZldGNoU3R1ZGVudEVucm9sbG1lbnRzIiwic3R1ZGVudElkIiwiY29uc29sZSIsImxvZyIsInN0dWRlbnRJZEZvcm1hdHMiLCJyZXBsYWNlIiwiZW5yb2xsbWVudHMiLCJzdHVkZW50UmVmIiwiZm9ybWF0IiwidGVzdFN0dWRlbnRSZWYiLCJjb2xsZWN0aW9uIiwiZG9jIiwidGVzdFN0dWRlbnRTbmFwIiwiZ2V0IiwiZXhpc3RzIiwid2FybiIsImVucm9sbG1lbnRzUmVmIiwiZW5yb2xsbWVudHNTbmFwIiwid2hlcmUiLCJzaXplIiwiZG9jcyIsImZvckVhY2giLCJkYXRhIiwic3ViamVjdElkIiwiaWQiLCJwdXNoIiwic3ViamVjdE5hbWUiLCJzdWJqZWN0X25hbWUiLCJsIiwic3ViamVjdF9jb2RlIiwiZ2V0U3ViamVjdENvZGVGcm9tSWQiLCJsZXNzb25zX3Blcl93ZWVrIiwic3RhdHVzIiwiZW5yb2xsZWRBdCIsImVucm9sbGVkX2F0IiwiY29nbml0aXZlTG9hZCIsImVycm9yIiwibm9ybWFsaXplZElkIiwiY29kZU1hcCIsImRpc3RyaWJ1dGVMZXNzb25zQWNyb3NzV2VlayIsInN0dWRlbnRHcmFkZSIsIndlZWtseUxlc3NvbnMiLCJsZW5ndGgiLCJ3ZWVrU2NoZWR1bGUiLCJtYXAiLCJBcnJheSIsImZpbGwiLCJ0b3RhbExlc3NvbnNOZWVkZWQiLCJyZWR1Y2UiLCJzdW0iLCJlIiwidG90YWxTbG90c0F2YWlsYWJsZSIsInNvcnRlZEVucm9sbG1lbnRzIiwic29ydCIsImEiLCJiIiwic3ViamVjdExlc3NvbkNvdW50cyIsImVucm9sbG1lbnQiLCJzY2hlZHVsZUFsbExlc3NvbnNPcHRpbWFsbHkiLCJmaWxsRW1wdHlTbG90c1dpdGhGcmVlUGVyaW9kcyIsImRheUluZGV4IiwidGltZUluZGV4IiwibGVzc29uIiwiZmluYWxDb3VudHMiLCJsZXNzb25zVG9TY2hlZHVsZSIsImkiLCJzdWJqZWN0RGFpbHlDb3VudCIsImxlc3NvblRvU2NoZWR1bGUiLCJzY2hlZHVsZWQiLCJwcmVmZXJyZWRUaW1lU2xvdHMiLCJnZXRQcmVmZXJyZWRUaW1lU2xvdHMiLCJkYXlQcmlvcml0eSIsImZyb20iLCJfIiwibWF4TGVzc29uc1BlckRheSIsImhhc0FkamFjZW50TGVzc29uIiwiY3JlYXRlTGVzc29uT2JqZWN0IiwiZml4TGVzc29uTnVtYmVyaW5nQ2hyb25vbG9naWNhbGx5Iiwic3ViamVjdExlc3NvbnMiLCJwb3NpdGlvbiIsIk9iamVjdCIsImVudHJpZXMiLCJsZXNzb25zIiwibGVzc29uRGF0YSIsImluZGV4IiwibmV3TGVzc29uTnVtYmVyIiwic3ViamVjdCIsIkRhdGUiLCJ1cGRhdGVkTGVzc29uIiwia2V5cyIsImxlc3NvblJlZmVyZW5jZSIsInRpdGxlIiwidGltZSIsImRheSIsImR1cmF0aW9uIiwiZGVzY3JpcHRpb24iLCJsZXNzb25OdW1iZXJJbldlZWsiLCJ0b3RhbFdlZWtzIiwidGVhY2hlciIsImNyZWF0ZWRBdCIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0IiwiYXBwbHlMZXNzb25Db21wbGV0aW9uU3RhdGVzIiwiY29tcGxldGlvblN0YXRlcyIsInVwZGF0ZWRMZXNzb25zIiwiY29tcGxldGlvbkRhdGEiLCJjb21wbGV0ZWQiLCJjb21wbGV0ZWRBdCIsInByb2dyZXNzIiwiY29tcGxldGVkQ291bnQiLCJmaWx0ZXIiLCJpblByb2dyZXNzQ291bnQiLCJ1cGNvbWluZ0NvdW50IiwiZ2V0U3R1ZGVudEdyYWRlIiwic3R1ZGVudFNuYXAiLCJzdHVkZW50RGF0YSIsImdyYWRlTGV2ZWwiLCJmZXRjaExlc3NvbkNvbXBsZXRpb25TdGF0ZXMiLCJsZXNzb25TdGF0ZXNSZWYiLCJsZXNzb25TdGF0ZXNTbmFwIiwic3RhdGVzRGF0YSIsImxlc3NvblJlZiIsImNvbXBsZXRpb25fc3RhdHVzIiwiY29tcGxldGVkX2F0IiwiZm9ybWF0RXJyb3IiLCJ2ZXJpZnlBdXRoVG9rZW4iLCJyZXF1ZXN0IiwiYXV0aEhlYWRlciIsImhlYWRlcnMiLCJpZFRva2VuIiwic3BsaXQiLCJkZWNvZGVkVG9rZW4iLCJ2ZXJpZnlJZFRva2VuIiwidWlkIiwiZnJlZVBlcmlvZENvdW50ZXIiLCJmcmVlUGVyaW9kIiwiU3RyaW5nIiwiaXNGcmVlUGVyaW9kIiwiR0VUIiwianNvbiIsInN1Y2Nlc3MiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJkYXRlIiwid2Vla1BhcmFtIiwicGFyc2VJbnQiLCJpc05hTiIsInNjaGVkdWxlIiwidGltZXRhYmxlIiwidG90YWxMZXNzb25zIiwic3R1ZGVudF9pZCIsImFjYWRlbWljX3dlZWsiLCJ0b3RhbF93ZWVrcyIsInN0dWRlbnRfZ3JhZGUiLCJ3ZWVrbHlTY2hlZHVsZSIsInNjaGVkdWxlV2l0aENvbXBsZXRpb25TdGF0ZXMiLCJkYXlPcmRlciIsImRheUEiLCJpbmRleE9mIiwiZGF5QiIsImxvY2FsZUNvbXBhcmUiLCJFcnJvciIsIm1lc3NhZ2UiLCJkZXRhaWxzIiwic3RhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/timetable/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "firebase-admin/auth":
/*!**************************************!*\
  !*** external "firebase-admin/auth" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/auth");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();