/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/timetable/route";
exports.ids = ["app/api/timetable/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/timetable/route.ts */ \"(rsc)/./src/app/api/timetable/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/timetable/route\",\n        pathname: \"/api/timetable\",\n        filename: \"route\",\n        bundlePath: \"app/api/timetable/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\timetable\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/timetable/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/timetable/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n/* harmony import */ var _lib_time_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/time-utils */ \"(rsc)/./src/lib/time-utils.ts\");\n/* harmony import */ var firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase-admin/auth */ \"firebase-admin/auth\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__]);\nfirebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Define the standard time slots for timetable generation\nconst STANDARD_TIME_SLOTS = [\n    '08:00-08:45',\n    '08:50-09:35',\n    '09:40-10:25',\n    '10:40-11:25',\n    '11:30-12:15',\n    '13:15-14:00',\n    '14:15-15:00'\n];\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\n// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)\nconst SUBJECT_COGNITIVE_LOADS = {\n    'mathematics': 9,\n    'english_language': 8,\n    'basic_science_and_technology': 7,\n    'basic_science': 7,\n    'social_studies': 6,\n    'computer_studies': 8,\n    'computing': 8,\n    'artificial_intelligence': 9,\n    'french': 7,\n    'christian_religious_knowledge': 5,\n    'national_values_education': 4,\n    'entrepreneurship_education': 5,\n    'entrepreneurship': 5,\n    'financial_literacy': 6,\n    'creative_arts': 3,\n    'cultural_and_creative_arts': 3,\n    'art_and_design': 3,\n    'physical_health_education': 2,\n    'project_based_excellence': 6\n};\n// Calculate lesson reference for a specific week and subject\nfunction generateLessonReference(grade, subjectCode, academicWeek, lessonNumber, lessonsPerWeek) {\n    // Calculate the absolute lesson number across all weeks\n    const absoluteLessonNumber = (academicWeek - 1) * lessonsPerWeek + lessonNumber;\n    // Format lesson number with leading zeros (e.g., 001, 002, etc.)\n    const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');\n    return `${grade}-${subjectCode}-${formattedLessonNumber}`;\n}\n// Fetch student enrollments from Firestore\nasync function fetchStudentEnrollments(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        let enrollments = [];\n        let studentRef = null;\n        for (const format of studentIdFormats){\n            console.log(`[Timetable API] Trying student ID format: ${format}`);\n            const testStudentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const testStudentSnap = await testStudentRef.get();\n            if (testStudentSnap.exists) {\n                console.log(`[Timetable API] Found student document with ID: ${format}`);\n                studentRef = testStudentRef;\n                break;\n            }\n        }\n        if (!studentRef) {\n            console.warn(`[Timetable API] No student document found for any ID format`);\n            return [];\n        }\n        // Fetch enrollments from the enrollments subcollection\n        const enrollmentsRef = studentRef.collection('enrollments');\n        const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();\n        console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);\n        enrollmentsSnap.docs.forEach((doc)=>{\n            const data = doc.data();\n            const subjectId = doc.id;\n            enrollments.push({\n                subjectId: subjectId,\n                subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),\n                lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,\n                status: data.status,\n                enrolledAt: data.enrolledAt || data.enrolled_at,\n                cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load\n            });\n        });\n        console.log(`[Timetable API] Processed enrollments:`, enrollments);\n        return enrollments;\n    } catch (error) {\n        console.error(`[Timetable API] Error fetching enrollments:`, error);\n        return [];\n    }\n}\n// Get subject code from subject ID\nfunction getSubjectCodeFromId(subjectId) {\n    const codeMap = {\n        'mathematics': 'MAT',\n        'english_language': 'ENG',\n        'basic_science_and_technology': 'BST',\n        'basic_science': 'BST',\n        'social_studies': 'SST',\n        'computer_studies': 'COM',\n        'computing': 'COM',\n        'creative_arts': 'ART',\n        'cultural_and_creative_arts': 'CCA',\n        'art_and_design': 'ART',\n        'physical_health_education': 'PHE',\n        'national_values_education': 'NVE',\n        'entrepreneurship_education': 'ENT',\n        'entrepreneurship': 'ENT',\n        'financial_literacy': 'FIL',\n        'french': 'FRE',\n        'artificial_intelligence': 'AI',\n        'project_based_excellence': 'PBE',\n        'christian_religious_knowledge': 'CRK'\n    };\n    return codeMap[subjectId] || 'GEN';\n}\n// Distribute lessons across the week based on lessons per week and cognitive load\nfunction distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade) {\n    const weeklyLessons = [];\n    console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);\n    // Create a weekly schedule grid: [day][timeSlot]\n    const weekSchedule = DAYS_OF_WEEK.map(()=>new Array(STANDARD_TIME_SLOTS.length).fill(null));\n    // Sort enrollments by cognitive load (highest first) for optimal time slot assignment\n    const sortedEnrollments = [\n        ...enrollments\n    ].sort((a, b)=>(b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));\n    console.log(`[Timetable API] Sorted subjects by cognitive load:`, sortedEnrollments.map((e)=>`${e.subjectName} (${e.cognitiveLoad})`));\n    // Track how many lessons each subject has been assigned\n    const subjectLessonCounts = {};\n    // Track which subjects have been scheduled on which days to enforce one-per-day rule\n    const subjectDayTracker = {};\n    sortedEnrollments.forEach((enrollment)=>{\n        console.log(`[Timetable API] Processing ${enrollment.subjectName} with ${enrollment.lessonsPerWeek} lessons per week, cognitive load: ${enrollment.cognitiveLoad}`);\n        subjectLessonCounts[enrollment.subjectId] = 0;\n        subjectDayTracker[enrollment.subjectId] = new Set();\n        const lessonsToSchedule = enrollment.lessonsPerWeek;\n        if (enrollment.subjectId === 'mathematics' && lessonsToSchedule === 6) {\n            // Mathematics with 6 lessons: 2 on Monday, 1 on Tue-Fri\n            scheduleMathematicsLessons(enrollment, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);\n        } else if (lessonsToSchedule >= 6) {\n            // Other subjects with 6+ lessons per week, allow multiple lessons per day\n            scheduleMultipleLessonsPerDay(enrollment, lessonsToSchedule, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);\n        } else {\n            // For subjects with <6 lessons per week, max 1 lesson per day\n            scheduleOneLessonPerDay(enrollment, lessonsToSchedule, weekSchedule, academicWeek, studentGrade, subjectLessonCounts, subjectDayTracker[enrollment.subjectId]);\n        }\n    });\n    // Fill any remaining empty slots with \"Free Period\"\n    fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade);\n    // Convert the schedule grid back to lesson objects\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson) {\n                weeklyLessons.push(lesson);\n            }\n        }\n    }\n    console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (including free periods)`);\n    return weeklyLessons;\n}\n// Schedule Mathematics lessons specifically (2 on Monday, 1 on Tue-Fri)\nfunction scheduleMathematicsLessons(enrollment, weekSchedule, academicWeek, studentGrade, subjectLessonCounts) {\n    const lessonsToSchedule = 6;\n    // Schedule 2 lessons on Monday (high cognitive load - early morning slots)\n    let mondayLessons = 0;\n    for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length && mondayLessons < 2; timeIndex++){\n        if (weekSchedule[0][timeIndex] === null) {\n            subjectLessonCounts[enrollment.subjectId]++;\n            const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n            const lesson = createLessonObject(enrollment, lessonNumber, 0, timeIndex, academicWeek, studentGrade);\n            weekSchedule[0][timeIndex] = lesson;\n            mondayLessons++;\n        }\n    }\n    // Schedule 1 lesson on Tuesday through Friday\n    for(let dayIndex = 1; dayIndex < DAYS_OF_WEEK.length && subjectLessonCounts[enrollment.subjectId] < lessonsToSchedule; dayIndex++){\n        // Find the best available time slot based on cognitive load\n        const cognitiveLoad = enrollment.cognitiveLoad || 9; // Mathematics has high cognitive load\n        const preferredTimeSlots = getPreferredTimeSlots(cognitiveLoad);\n        let scheduled = false;\n        for (const timeIndex of preferredTimeSlots){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                subjectLessonCounts[enrollment.subjectId]++;\n                const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n                const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                weekSchedule[dayIndex][timeIndex] = lesson;\n                scheduled = true;\n                break;\n            }\n        }\n        // If no preferred slot available, find any available slot\n        if (!scheduled) {\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n                    const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    break;\n                }\n            }\n        }\n    }\n}\n// Schedule multiple lessons per day for high-frequency subjects\nfunction scheduleMultipleLessonsPerDay(enrollment, lessonsToSchedule, weekSchedule, academicWeek, studentGrade, subjectLessonCounts) {\n    const lessonsPerDay = Math.ceil(lessonsToSchedule / DAYS_OF_WEEK.length);\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length && subjectLessonCounts[enrollment.subjectId] < lessonsToSchedule; dayIndex++){\n        let lessonsScheduledThisDay = 0;\n        // Start from the best time slots (morning) for high cognitive load subjects\n        const cognitiveLoad = enrollment.cognitiveLoad || 5;\n        const startTimeIndex = cognitiveLoad >= 7 ? 0 : Math.floor(STANDARD_TIME_SLOTS.length / 2);\n        for(let timeIndex = startTimeIndex; timeIndex < STANDARD_TIME_SLOTS.length && lessonsScheduledThisDay < lessonsPerDay && subjectLessonCounts[enrollment.subjectId] < lessonsToSchedule; timeIndex++){\n            // Don't schedule back-to-back lessons - skip if previous slot has same subject\n            if (timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId) {\n                continue;\n            }\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                subjectLessonCounts[enrollment.subjectId]++;\n                const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n                const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                weekSchedule[dayIndex][timeIndex] = lesson;\n                lessonsScheduledThisDay++;\n            }\n        }\n    }\n}\n// Schedule one lesson per day for regular subjects\nfunction scheduleOneLessonPerDay(enrollment, lessonsToSchedule, weekSchedule, academicWeek, studentGrade, subjectLessonCounts, dayTracker) {\n    const daysToUse = Math.min(lessonsToSchedule, DAYS_OF_WEEK.length);\n    // Find available days for this subject (not already used)\n    const availableDays = [];\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        if (!dayTracker.has(dayIndex)) {\n            availableDays.push(dayIndex);\n        }\n    }\n    // If we need more days than available, we'll have to reuse some days\n    const daysToSchedule = Math.min(daysToUse, availableDays.length);\n    for(let i = 0; i < daysToSchedule; i++){\n        const dayIndex = availableDays[i];\n        dayTracker.add(dayIndex); // Mark this day as used for this subject\n        // Find the best available time slot based on cognitive load\n        const cognitiveLoad = enrollment.cognitiveLoad || 5;\n        const preferredTimeSlots = getPreferredTimeSlots(cognitiveLoad);\n        let scheduled = false;\n        for (const timeIndex of preferredTimeSlots){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                subjectLessonCounts[enrollment.subjectId]++;\n                const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n                const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                weekSchedule[dayIndex][timeIndex] = lesson;\n                scheduled = true;\n                break;\n            }\n        }\n        // If no preferred slot available, find any available slot\n        if (!scheduled) {\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    const lessonNumber = subjectLessonCounts[enrollment.subjectId];\n                    const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    scheduled = true;\n                    break;\n                }\n            }\n        }\n        if (!scheduled) {\n            console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} on ${DAYS_OF_WEEK[dayIndex]} - no available slots`);\n        }\n    }\n}\n// Get preferred time slots based on cognitive load\nfunction getPreferredTimeSlots(cognitiveLoad) {\n    if (cognitiveLoad >= 8) {\n        // High cognitive load: prefer early morning slots\n        return [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 6) {\n        // Medium cognitive load: prefer mid-morning slots\n        return [\n            1,\n            2,\n            3,\n            0,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 4) {\n        // Low-medium cognitive load: prefer afternoon slots\n        return [\n            3,\n            4,\n            5,\n            2,\n            6,\n            1,\n            0\n        ];\n    } else {\n        // Low cognitive load: prefer late slots\n        return [\n            5,\n            6,\n            4,\n            3,\n            2,\n            1,\n            0\n        ];\n    }\n}\n// Create a lesson object\nfunction createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade) {\n    const lessonReference = generateLessonReference(studentGrade, enrollment.subjectCode, academicWeek, lessonNumber, enrollment.lessonsPerWeek);\n    // Calculate lesson status\n    let status = 'upcoming';\n    try {\n        const statusResult = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_2__.calculateLessonStatus)(STANDARD_TIME_SLOTS[timeIndex], DAYS_OF_WEEK[dayIndex], new Date());\n        status = statusResult.status;\n    } catch (statusError) {\n        console.warn('[Timetable API] Error calculating lesson status:', statusError);\n    }\n    return {\n        id: `${lessonReference}_week${academicWeek}`,\n        lessonReference: lessonReference,\n        title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,\n        subject: enrollment.subjectName,\n        subjectId: enrollment.subjectId,\n        subjectCode: enrollment.subjectCode,\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        day: DAYS_OF_WEEK[dayIndex],\n        duration: 45,\n        status: status,\n        description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,\n        grade: studentGrade,\n        academicWeek: academicWeek,\n        lessonNumberInWeek: lessonNumber,\n        absoluteLessonNumber: (academicWeek - 1) * enrollment.lessonsPerWeek + lessonNumber,\n        totalWeeks: 30,\n        teacher: 'AI Instructor',\n        cognitiveLoad: enrollment.cognitiveLoad,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n}\n// Get student grade level\nasync function getStudentGrade(studentId) {\n    try {\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        for (const format of studentIdFormats){\n            const studentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const studentSnap = await studentRef.get();\n            if (studentSnap.exists) {\n                const studentData = studentSnap.data();\n                return studentData?.gradeLevel || studentData?.grade || 'P5';\n            }\n        }\n        return 'P5'; // Default grade\n    } catch (error) {\n        console.error('[Timetable API] Error fetching student grade:', error);\n        return 'P5';\n    }\n}\n// Verify Firebase Auth token\nasync function verifyAuthToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader?.startsWith('Bearer ')) {\n            return null;\n        }\n        const idToken = authHeader.split('Bearer ')[1];\n        const decodedToken = await (0,firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__.getAuth)().verifyIdToken(idToken);\n        return {\n            uid: decodedToken.uid\n        };\n    } catch (error) {\n        console.error('Auth verification failed:', error);\n        return null;\n    }\n}\n// Fill any remaining empty slots with \"Free Period\"\nfunction fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade) {\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                const freePeriod = {\n                    id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,\n                    lessonReference: `${studentGrade}-FREE-${String(academicWeek).padStart(3, '0')}-${String(timeIndex + 1).padStart(3, '0')}`,\n                    title: 'Free Period',\n                    subject: 'Free Period',\n                    subjectId: 'free_period',\n                    subjectCode: 'FREE',\n                    time: STANDARD_TIME_SLOTS[timeIndex],\n                    day: DAYS_OF_WEEK[dayIndex],\n                    duration: 45,\n                    status: 'upcoming',\n                    description: 'Free study period',\n                    grade: studentGrade,\n                    academicWeek: academicWeek,\n                    lessonNumberInWeek: timeIndex + 1,\n                    absoluteLessonNumber: null,\n                    totalWeeks: 30,\n                    teacher: null,\n                    cognitiveLoad: 1,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isFreePeriod: true\n                };\n                weekSchedule[dayIndex][timeIndex] = freePeriod;\n            }\n        }\n    }\n}\nasync function GET(request) {\n    try {\n        // Check if Firebase Admin is properly initialized\n        if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db) {\n            console.error('[Timetable API] Firebase Admin not properly initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Database not available'\n            }, {\n                status: 500\n            });\n        }\n        // For development/testing, allow unauthenticated requests\n        // In production, uncomment the authentication check below\n        /*\r\n    const authResult = await verifyAuthToken(request);\r\n    if (!authResult) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Unauthorized' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n    */ const { searchParams } = new URL(request.url);\n        const studentId = searchParams.get('studentId');\n        const date = searchParams.get('date');\n        const weekParam = searchParams.get('week');\n        // Parse week parameter (default to week 1)\n        const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;\n        if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid week number. Must be between 1 and 30.'\n            }, {\n                status: 400\n            });\n        }\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);\n        // Fetch student grade level\n        const studentGrade = await getStudentGrade(studentId);\n        console.log(`[Timetable API] Student grade: ${studentGrade}`);\n        // Fetch student enrollments (this is now the single source of truth)\n        const enrollments = await fetchStudentEnrollments(studentId);\n        if (enrollments.length === 0) {\n            console.log('[Timetable API] No active enrollments found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    schedule: [],\n                    timetable: [],\n                    totalLessons: 0,\n                    enrollments: [],\n                    student_id: studentId,\n                    academic_week: academicWeek,\n                    total_weeks: 30,\n                    student_grade: studentGrade,\n                    date: date\n                }\n            });\n        }\n        // Generate weekly timetable based on enrollments\n        const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade);\n        // Sort weekly schedule by day and time\n        const dayOrder = [\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday'\n        ];\n        weeklySchedule.sort((a, b)=>{\n            const dayA = dayOrder.indexOf(a.day);\n            const dayB = dayOrder.indexOf(b.day);\n            if (dayA !== dayB) {\n                return dayA - dayB;\n            }\n            // Sort by time within the same day\n            if (a.time && b.time) {\n                return a.time.localeCompare(b.time);\n            }\n            return 0;\n        });\n        console.log(`[Timetable API] Returning ${weeklySchedule.length} lessons for week ${academicWeek}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                schedule: weeklySchedule,\n                timetable: weeklySchedule,\n                totalLessons: weeklySchedule.length,\n                enrollments: enrollments,\n                student_id: studentId,\n                academic_week: academicWeek,\n                total_weeks: 30,\n                student_grade: studentGrade,\n                date: date\n            }\n        });\n    } catch (error) {\n        console.error('[Timetable API] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch timetable',\n            details: error instanceof Error ? error.stack : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/timetable/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/time-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/time-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateLessonStatus: () => (/* binding */ calculateLessonStatus),\n/* harmony export */   dayNameToNumber: () => (/* binding */ dayNameToNumber),\n/* harmony export */   formatTimeRange: () => (/* binding */ formatTimeRange),\n/* harmony export */   getCurrentAcademicWeek: () => (/* binding */ getCurrentAcademicWeek),\n/* harmony export */   getCurrentDayName: () => (/* binding */ getCurrentDayName),\n/* harmony export */   getTimeUntilNext: () => (/* binding */ getTimeUntilNext),\n/* harmony export */   isCurrentTimeInRange: () => (/* binding */ isCurrentTimeInRange),\n/* harmony export */   minutesToTimeString: () => (/* binding */ minutesToTimeString),\n/* harmony export */   parseTimeRange: () => (/* binding */ parseTimeRange),\n/* harmony export */   parseTimeToMinutes: () => (/* binding */ parseTimeToMinutes)\n/* harmony export */ });\n// src/lib/time-utils.ts\n// Standardized time parsing utilities for the application\n/**\r\n * Parses a time string in various formats and returns minutes since midnight\r\n * Supports formats: \"HH:MM\", \"H:MM\", with optional AM/PM\r\n */ function parseTimeToMinutes(timeStr) {\n    if (!timeStr || typeof timeStr !== 'string') {\n        throw new Error(`Invalid time input: ${timeStr}`);\n    }\n    const cleanTimeStr = timeStr.trim().toLowerCase();\n    // Handle AM/PM format\n    let isPM = false;\n    let timeOnly = cleanTimeStr;\n    if (cleanTimeStr.includes('am') || cleanTimeStr.includes('pm')) {\n        isPM = cleanTimeStr.includes('pm');\n        timeOnly = cleanTimeStr.replace(/\\s*(am|pm)\\s*/, '');\n    }\n    const parts = timeOnly.split(':');\n    if (parts.length !== 2) {\n        throw new Error(`Invalid time format: ${cleanTimeStr}`);\n    }\n    let hours = parseInt(parts[0], 10);\n    const minutes = parseInt(parts[1], 10);\n    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n        throw new Error(`Invalid time value in ${cleanTimeStr}`);\n    }\n    // Handle AM/PM conversion\n    if (isPM && hours !== 12) {\n        hours += 12;\n    } else if (!isPM && hours === 12) {\n        hours = 0;\n    }\n    return hours * 60 + minutes;\n}\n/**\r\n * Parses a time range string in format \"HH:MM - HH:MM\" or \"HH:MM-HH:MM\"\r\n */ function parseTimeRange(timeRangeStr) {\n    try {\n        if (!timeRangeStr || typeof timeRangeStr !== 'string') {\n            return {\n                startMinutes: 0,\n                endMinutes: 0,\n                isValid: false,\n                error: 'Invalid time range input'\n            };\n        }\n        // Split by hyphen, handle spaces around it\n        const parts = timeRangeStr.split(/\\s*-\\s*/);\n        if (parts.length !== 2) {\n            return {\n                startMinutes: 0,\n                endMinutes: 0,\n                isValid: false,\n                error: 'Time range must be in format \"HH:MM - HH:MM\"'\n            };\n        }\n        const startMinutes = parseTimeToMinutes(parts[0]);\n        const endMinutes = parseTimeToMinutes(parts[1]);\n        if (endMinutes <= startMinutes) {\n            return {\n                startMinutes,\n                endMinutes,\n                isValid: false,\n                error: 'End time must be after start time'\n            };\n        }\n        return {\n            startMinutes,\n            endMinutes,\n            isValid: true\n        };\n    } catch (error) {\n        return {\n            startMinutes: 0,\n            endMinutes: 0,\n            isValid: false,\n            error: error instanceof Error ? error.message : 'Unknown parsing error'\n        };\n    }\n}\n/**\r\n * Converts minutes since midnight to time string\r\n */ function minutesToTimeString(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\n/**\r\n * Gets the current day of week as string\r\n */ function getCurrentDayName() {\n    const days = [\n        'Sunday',\n        'Monday',\n        'Tuesday',\n        'Wednesday',\n        'Thursday',\n        'Friday',\n        'Saturday'\n    ];\n    return days[new Date().getDay()];\n}\n/**\r\n * Converts day name to number (0 = Sunday, 1 = Monday, etc.)\r\n */ function dayNameToNumber(dayName) {\n    const days = {\n        'sunday': 0,\n        'monday': 1,\n        'tuesday': 2,\n        'wednesday': 3,\n        'thursday': 4,\n        'friday': 5,\n        'saturday': 6\n    };\n    return days[dayName.toLowerCase()] ?? -1;\n}\n/**\r\n * Determines lesson status based on time and day\r\n */ function calculateLessonStatus(timeRange, lessonDay, currentTime, progressStatus) {\n    const now = currentTime || new Date();\n    const currentMinutes = now.getHours() * 60 + now.getMinutes();\n    const currentDay = now.getDay();\n    const lessonDayNumber = dayNameToNumber(lessonDay);\n    const isToday = currentDay === lessonDayNumber;\n    // If progress shows completed, always mark as completed\n    if (progressStatus === 'completed') {\n        return {\n            status: 'completed',\n            isToday\n        };\n    }\n    // Parse the time range\n    const timeParseResult = parseTimeRange(timeRange);\n    if (!timeParseResult.isValid) {\n        return {\n            status: 'upcoming',\n            isToday,\n            error: timeParseResult.error\n        };\n    }\n    const { startMinutes, endMinutes } = timeParseResult;\n    // If lesson is on a different day\n    if (!isToday) {\n        return {\n            status: lessonDayNumber < currentDay ? 'completed' : 'upcoming',\n            isToday: false\n        };\n    }\n    // Lesson is today - check time\n    if (currentMinutes >= endMinutes) {\n        return {\n            status: 'completed',\n            isToday: true\n        };\n    } else if (currentMinutes >= startMinutes && currentMinutes < endMinutes) {\n        return {\n            status: 'current',\n            timeUntilEnd: endMinutes - currentMinutes,\n            isToday: true\n        };\n    } else {\n        return {\n            status: 'upcoming',\n            timeUntilStart: startMinutes - currentMinutes,\n            isToday: true\n        };\n    }\n}\n/**\r\n * Formats time range for display\r\n */ function formatTimeRange(startTime, endTime) {\n    try {\n        const startMinutes = parseTimeToMinutes(startTime);\n        const endMinutes = parseTimeToMinutes(endTime);\n        return `${minutesToTimeString(startMinutes)} - ${minutesToTimeString(endMinutes)}`;\n    } catch (error) {\n        return `${startTime} - ${endTime}`; // Fallback to original strings\n    }\n}\n/**\r\n * Checks if current time is within a time range\r\n */ function isCurrentTimeInRange(timeRange, currentTime) {\n    const result = calculateLessonStatus(timeRange, getCurrentDayName(), currentTime);\n    return result.status === 'current';\n}\n/**\r\n * Gets time until next event (start or end)\r\n */ function getTimeUntilNext(timeRange, currentTime) {\n    const status = calculateLessonStatus(timeRange, getCurrentDayName(), currentTime);\n    if (status.timeUntilStart !== undefined) {\n        const hours = Math.floor(status.timeUntilStart / 60);\n        const mins = status.timeUntilStart % 60;\n        return {\n            type: 'start',\n            minutes: status.timeUntilStart,\n            message: hours > 0 ? `${hours}h ${mins}m until start` : `${mins}m until start`\n        };\n    }\n    if (status.timeUntilEnd !== undefined) {\n        const hours = Math.floor(status.timeUntilEnd / 60);\n        const mins = status.timeUntilEnd % 60;\n        return {\n            type: 'end',\n            minutes: status.timeUntilEnd,\n            message: hours > 0 ? `${hours}h ${mins}m remaining` : `${mins}m remaining`\n        };\n    }\n    return {\n        type: 'none',\n        minutes: 0,\n        message: status.status === 'completed' ? 'Completed' : 'Upcoming'\n    };\n}\n/**\r\n * Calculate the current academic week based on the school year start date.\r\n * Academic year starts in early September and has 30 teaching weeks.\r\n * Returns a value between 1 and 30.\r\n */ function getCurrentAcademicWeek() {\n    const now = new Date();\n    const currentYear = now.getFullYear();\n    // Academic year starts in September of the current year if we're past August,\n    // otherwise it started in September of the previous year\n    const academicYearStart = now.getMonth() >= 8 ? currentYear : currentYear - 1;\n    // School typically starts around September 1st\n    const schoolStartDate = new Date(academicYearStart, 8, 1); // September 1st\n    // Calculate the difference in weeks\n    const timeDiffMs = now.getTime() - schoolStartDate.getTime();\n    const weeksDiff = Math.floor(timeDiffMs / (1000 * 60 * 60 * 24 * 7));\n    // Ensure we return a value between 1 and 30\n    const academicWeek = Math.max(1, Math.min(30, weeksDiff + 1));\n    console.log(`[getCurrentAcademicWeek] School start: ${schoolStartDate.toDateString()}, Current: ${now.toDateString()}, Week: ${academicWeek}`);\n    return academicWeek;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/time-utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "firebase-admin/auth":
/*!**************************************!*\
  !*** external "firebase-admin/auth" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/auth");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();