/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/timetable/route";
exports.ids = ["app/api/timetable/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/timetable/route.ts */ \"(rsc)/./src/app/api/timetable/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/timetable/route\",\n        pathname: \"/api/timetable\",\n        filename: \"route\",\n        bundlePath: \"app/api/timetable/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\timetable\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/timetable/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/timetable/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n/* harmony import */ var firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase-admin/auth */ \"firebase-admin/auth\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__]);\nfirebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Define the standard time slots for timetable generation\nconst STANDARD_TIME_SLOTS = [\n    '08:00-08:45',\n    '08:50-09:35',\n    '09:40-10:25',\n    '10:40-11:25',\n    '11:30-12:15',\n    '13:15-14:00',\n    '14:15-15:00'\n];\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\n// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)\nconst SUBJECT_COGNITIVE_LOADS = {\n    'mathematics': 9,\n    'english_language': 8,\n    'basic_science_and_technology': 7,\n    'basic_science': 7,\n    'social_studies': 6,\n    'computer_studies': 8,\n    'computing': 8,\n    'artificial_intelligence': 9,\n    'french': 7,\n    'christian_religious_knowledge': 5,\n    'national_values_education': 4,\n    'entrepreneurship_education': 5,\n    'entrepreneurship': 5,\n    'financial_literacy': 6,\n    'creative_arts': 3,\n    'cultural_and_creative_arts': 3,\n    'art_and_design': 3,\n    'physical_health_education': 2,\n    'project_based_excellence': 6\n};\n// Calculate lesson reference for a specific week and subject\nfunction generateLessonReference(grade, subjectCode, academicWeek, lessonNumber, lessonsPerWeek) {\n    // Calculate the absolute lesson number across all weeks\n    const absoluteLessonNumber = (academicWeek - 1) * lessonsPerWeek + lessonNumber;\n    // Format lesson number with leading zeros (e.g., 001, 002, etc.)\n    const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');\n    // Convert grade to proper abbreviated format\n    const abbreviatedGrade = convertGradeToAbbreviation(grade);\n    return `${abbreviatedGrade}-${subjectCode}-${formattedLessonNumber}`;\n}\n// Convert grade level to proper abbreviated format\nfunction convertGradeToAbbreviation(grade) {\n    // Handle different grade formats\n    const gradeStr = grade.toLowerCase().trim();\n    // Primary grades\n    if (gradeStr.includes('primary') || gradeStr.startsWith('p')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `P${match[1]}`;\n        }\n    }\n    // Junior Secondary\n    if (gradeStr.includes('junior') || gradeStr.includes('jss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `JSS${match[1]}`;\n        }\n    }\n    // Senior Secondary\n    if (gradeStr.includes('senior') || gradeStr.includes('sss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `SSS${match[1]}`;\n        }\n    }\n    // If already in correct format, return as is\n    if (/^(P|JSS|SSS)\\d+$/i.test(grade)) {\n        return grade.toUpperCase();\n    }\n    // Default fallback\n    return grade;\n}\n// Fetch student enrollments from Firestore\nasync function fetchStudentEnrollments(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        let enrollments = [];\n        let studentRef = null;\n        for (const format of studentIdFormats){\n            console.log(`[Timetable API] Trying student ID format: ${format}`);\n            const testStudentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const testStudentSnap = await testStudentRef.get();\n            if (testStudentSnap.exists) {\n                console.log(`[Timetable API] Found student document with ID: ${format}`);\n                studentRef = testStudentRef;\n                break;\n            }\n        }\n        if (!studentRef) {\n            console.warn(`[Timetable API] No student document found for any ID format`);\n            return [];\n        }\n        // Fetch enrollments from the enrollments subcollection\n        const enrollmentsRef = studentRef.collection('enrollments');\n        const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();\n        console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);\n        enrollmentsSnap.docs.forEach((doc)=>{\n            const data = doc.data();\n            const subjectId = doc.id;\n            enrollments.push({\n                subjectId: subjectId,\n                subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),\n                lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,\n                status: data.status,\n                enrolledAt: data.enrolledAt || data.enrolled_at,\n                cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load\n            });\n        });\n        console.log(`[Timetable API] Processed enrollments:`, enrollments);\n        return enrollments;\n    } catch (error) {\n        console.error(`[Timetable API] Error fetching enrollments:`, error);\n        return [];\n    }\n}\n// Get subject code from subject ID\nfunction getSubjectCodeFromId(subjectId) {\n    // Normalize the input: convert to lowercase and replace hyphens/spaces with underscores\n    const normalizedId = subjectId.toLowerCase().replace(/[-\\s]+/g, '_').replace(/[^a-z0-9_]/g, ''); // Remove any other special characters\n    const codeMap = {\n        'mathematics': 'MAT',\n        'english_language': 'ENG',\n        'basic_science_and_technology': 'BST',\n        'basic_science': 'BST',\n        'social_studies': 'SST',\n        'computer_studies': 'COM',\n        'computing': 'COM',\n        'creative_arts': 'ART',\n        'cultural_and_creative_arts': 'CCA',\n        'art_and_design': 'ART',\n        'physical_health_education': 'PHE',\n        'national_values_education': 'NVE',\n        'entrepreneurship_education': 'ENT',\n        'entrepreneurship': 'ENT',\n        'financial_literacy': 'FIL',\n        'french': 'FRE',\n        'artificial_intelligence': 'AI',\n        'project_based_excellence': 'PBE',\n        'project_based_excellence': 'PBE',\n        'christian_religious_knowledge': 'CRK'\n    };\n    // Try the normalized ID first\n    if (codeMap[normalizedId]) {\n        return codeMap[normalizedId];\n    }\n    // Try the original ID as fallback\n    if (codeMap[subjectId.toLowerCase()]) {\n        return codeMap[subjectId.toLowerCase()];\n    }\n    // Special handling for project-based excellence variants\n    if (subjectId.toLowerCase().includes('project') && subjectId.toLowerCase().includes('excellence')) {\n        return 'PBE';\n    }\n    return 'GEN';\n}\n// Distribute lessons across the week based on lessons per week and cognitive load\nfunction distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade) {\n    const weeklyLessons = [];\n    console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);\n    // Create a weekly schedule grid: [day][timeSlot]\n    const weekSchedule = DAYS_OF_WEEK.map(()=>new Array(STANDARD_TIME_SLOTS.length).fill(null));\n    // Calculate total lessons needed\n    const totalLessonsNeeded = enrollments.reduce((sum, e)=>sum + e.lessonsPerWeek, 0);\n    const totalSlotsAvailable = DAYS_OF_WEEK.length * STANDARD_TIME_SLOTS.length; // 5 days × 7 slots = 35\n    console.log(`[Timetable API] Total lessons needed: ${totalLessonsNeeded}, Total slots available: ${totalSlotsAvailable}`);\n    // Sort enrollments by cognitive load (highest first) for optimal time slot assignment\n    const sortedEnrollments = [\n        ...enrollments\n    ].sort((a, b)=>(b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));\n    console.log(`[Timetable API] Sorted subjects by cognitive load:`, sortedEnrollments.map((e)=>`${e.subjectName} (${e.cognitiveLoad}) - ${e.lessonsPerWeek} lessons`));\n    // Track how many lessons each subject has been assigned\n    const subjectLessonCounts = {};\n    // Initialize all subjects\n    sortedEnrollments.forEach((enrollment)=>{\n        subjectLessonCounts[enrollment.subjectId] = 0;\n    });\n    // Schedule all lessons using a more aggressive approach\n    scheduleAllLessonsOptimally(sortedEnrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);\n    // Fill any remaining empty slots with \"Free Period\"\n    fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade);\n    // Convert the schedule grid back to lesson objects\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson) {\n                weeklyLessons.push(lesson);\n            }\n        }\n    }\n    console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (target: ${totalSlotsAvailable})`);\n    // Log final subject distribution\n    const finalCounts = {};\n    weeklyLessons.forEach((lesson)=>{\n        if (lesson.subjectId && lesson.subjectId !== 'free_period') {\n            finalCounts[lesson.subjectId] = (finalCounts[lesson.subjectId] || 0) + 1;\n        }\n    });\n    console.log(`[Timetable API] Final lesson distribution:`, finalCounts);\n    return weeklyLessons;\n}\n// New optimized scheduling function that ensures all lessons are scheduled\nfunction scheduleAllLessonsOptimally(enrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts) {\n    // Create a list of all lessons that need to be scheduled (without lesson numbers initially)\n    const lessonsToSchedule = [];\n    enrollments.forEach((enrollment)=>{\n        for(let i = 1; i <= enrollment.lessonsPerWeek; i++){\n            lessonsToSchedule.push({\n                enrollment\n            });\n        }\n    });\n    console.log(`[Timetable API] Total lessons to schedule: ${lessonsToSchedule.length}`);\n    // Sort lessons by cognitive load (highest first)\n    lessonsToSchedule.sort((a, b)=>{\n        return (b.enrollment.cognitiveLoad || 5) - (a.enrollment.cognitiveLoad || 5);\n    });\n    // Track lessons per day for each subject to enforce distribution rules\n    const subjectDailyCount = {};\n    enrollments.forEach((enrollment)=>{\n        subjectDailyCount[enrollment.subjectId] = new Array(DAYS_OF_WEEK.length).fill(0);\n    });\n    // Schedule each lesson (without assigning lesson numbers yet)\n    for (const lessonToSchedule of lessonsToSchedule){\n        const { enrollment } = lessonToSchedule;\n        let scheduled = false;\n        // Get preferred time slots based on cognitive load\n        const preferredTimeSlots = getPreferredTimeSlots(enrollment.cognitiveLoad || 5);\n        // Try to schedule on each day, prioritizing days with fewer lessons for this subject\n        const dayPriority = Array.from({\n            length: DAYS_OF_WEEK.length\n        }, (_, i)=>i).sort((a, b)=>subjectDailyCount[enrollment.subjectId][a] - subjectDailyCount[enrollment.subjectId][b]);\n        for (const dayIndex of dayPriority){\n            // Special rule for Mathematics: allow up to 2 lessons on Monday, 1 on other days\n            const maxLessonsPerDay = enrollment.subjectId === 'mathematics' && dayIndex === 0 ? 2 : enrollment.lessonsPerWeek >= 6 ? 2 : 1;\n            if (subjectDailyCount[enrollment.subjectId][dayIndex] >= maxLessonsPerDay) {\n                continue; // Skip this day if we've reached the limit\n            }\n            // Try preferred time slots first\n            for (const timeIndex of preferredTimeSlots){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    // Check if this would create back-to-back lessons (avoid if possible)\n                    const hasAdjacentLesson = timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId || timeIndex < STANDARD_TIME_SLOTS.length - 1 && weekSchedule[dayIndex][timeIndex + 1]?.subjectId === enrollment.subjectId;\n                    // For subjects with multiple lessons per week, allow adjacent lessons if necessary\n                    if (!hasAdjacentLesson || enrollment.lessonsPerWeek >= 4) {\n                        subjectLessonCounts[enrollment.subjectId]++;\n                        subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                        // Create lesson object with placeholder lesson number (will be fixed later)\n                        const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);\n                        weekSchedule[dayIndex][timeIndex] = lesson;\n                        scheduled = true;\n                        break;\n                    }\n                }\n            }\n            if (scheduled) break;\n            // If no preferred slot worked, try any available slot on this day\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                    // Create lesson object with placeholder lesson number (will be fixed later)\n                    const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    scheduled = true;\n                    break;\n                }\n            }\n            if (scheduled) break;\n        }\n        if (!scheduled) {\n            console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} lesson - no available slots`);\n        }\n    }\n    // Now fix lesson numbering to be chronological\n    fixLessonNumberingChronologically(weekSchedule, academicWeek, studentGrade);\n    // Log scheduling results\n    console.log(`[Timetable API] Scheduled lessons and fixed chronological numbering`);\n}\n// Fix lesson numbering to follow chronological order (day and time based)\nfunction fixLessonNumberingChronologically(weekSchedule, academicWeek, studentGrade) {\n    // Group lessons by subject\n    const subjectLessons = {};\n    // Collect all lessons for each subject\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson && lesson.subjectId && lesson.subjectId !== 'free_period') {\n                if (!subjectLessons[lesson.subjectId]) {\n                    subjectLessons[lesson.subjectId] = [];\n                }\n                subjectLessons[lesson.subjectId].push({\n                    lesson,\n                    dayIndex,\n                    timeIndex,\n                    position: dayIndex * STANDARD_TIME_SLOTS.length + timeIndex // For sorting\n                });\n            }\n        }\n    }\n    // For each subject, sort lessons chronologically and reassign lesson numbers\n    Object.entries(subjectLessons).forEach(([subjectId, lessons])=>{\n        // Sort by chronological position (day first, then time)\n        lessons.sort((a, b)=>a.position - b.position);\n        // Reassign lesson numbers chronologically\n        lessons.forEach((lessonData, index)=>{\n            const { lesson, dayIndex, timeIndex } = lessonData;\n            const newLessonNumber = index + 1;\n            // Update the lesson object with correct numbering\n            const enrollment = {\n                subjectId: lesson.subjectId,\n                subjectName: lesson.subject,\n                subjectCode: lesson.subjectCode,\n                lessonsPerWeek: lessons.length,\n                cognitiveLoad: lesson.cognitiveLoad,\n                status: 'active',\n                enrolledAt: lesson.enrolledAt || new Date()\n            };\n            const updatedLesson = createLessonObject(enrollment, newLessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n            // Replace the lesson in the schedule\n            weekSchedule[dayIndex][timeIndex] = updatedLesson;\n        });\n    });\n    console.log(`[Timetable API] Fixed lesson numbering for ${Object.keys(subjectLessons).length} subjects`);\n}\n// Get preferred time slots based on cognitive load\nfunction getPreferredTimeSlots(cognitiveLoad) {\n    if (cognitiveLoad >= 8) {\n        // High cognitive load: prefer early morning slots\n        return [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 6) {\n        // Medium cognitive load: prefer mid-morning slots\n        return [\n            1,\n            2,\n            3,\n            0,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 4) {\n        // Low-medium cognitive load: prefer afternoon slots\n        return [\n            3,\n            4,\n            5,\n            2,\n            6,\n            1,\n            0\n        ];\n    } else {\n        // Low cognitive load: prefer late slots\n        return [\n            5,\n            6,\n            4,\n            3,\n            2,\n            1,\n            0\n        ];\n    }\n}\n// Create a lesson object\nfunction createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade) {\n    const lessonReference = generateLessonReference(studentGrade, enrollment.subjectCode, academicWeek, lessonNumber, enrollment.lessonsPerWeek);\n    // Default all lessons to \"upcoming\" status\n    // Actual completion status will be applied later from Firestore data\n    const status = 'upcoming';\n    return {\n        id: `${lessonReference}_week${academicWeek}`,\n        lessonReference: lessonReference,\n        title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,\n        subject: enrollment.subjectName,\n        subjectId: enrollment.subjectId,\n        subjectCode: enrollment.subjectCode,\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        day: DAYS_OF_WEEK[dayIndex],\n        duration: 45,\n        status: status,\n        description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,\n        grade: studentGrade,\n        academicWeek: academicWeek,\n        lessonNumberInWeek: lessonNumber,\n        absoluteLessonNumber: (academicWeek - 1) * enrollment.lessonsPerWeek + lessonNumber,\n        totalWeeks: 30,\n        teacher: 'AI Instructor',\n        cognitiveLoad: enrollment.cognitiveLoad,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n}\n// Apply lesson completion states from Firestore to the generated lessons\nfunction applyLessonCompletionStates(lessons, completionStates) {\n    console.log(`[Timetable API] Applying completion states to ${lessons.length} lessons`);\n    const updatedLessons = lessons.map((lesson)=>{\n        if (!lesson.lessonReference || lesson.isFreePeriod) {\n            return lesson; // Skip lessons without lesson references or free periods\n        }\n        // Check if this lesson has completion data from session\n        const sessionData = completionStates[lesson.lessonReference];\n        if (sessionData) {\n            console.log(`[Timetable API] Found session data for ${lesson.lessonReference}:`, {\n                status: sessionData.status,\n                sessionId: sessionData.sessionId,\n                currentPhase: sessionData.currentPhase,\n                lastUpdated: sessionData.lastUpdated\n            });\n            // Update lesson with actual completion status from session\n            return {\n                ...lesson,\n                status: sessionData.status,\n                completed: sessionData.completed,\n                completedAt: sessionData.completedAt,\n                progress: sessionData.progress,\n                // Add session-specific metadata\n                sessionId: sessionData.sessionId,\n                currentPhase: sessionData.currentPhase,\n                workingLevel: sessionData.workingLevel,\n                lastUpdated: sessionData.lastUpdated,\n                sessionCreatedAt: sessionData.createdAt\n            };\n        }\n        // No session data found, keep as \"upcoming\"\n        return lesson;\n    });\n    // Calculate status summary (excluding free periods)\n    const nonFreePeriodLessons = updatedLessons.filter((l)=>!l.isFreePeriod);\n    const completedCount = nonFreePeriodLessons.filter((l)=>l.status === 'completed').length;\n    const inProgressCount = nonFreePeriodLessons.filter((l)=>l.status === 'in_progress').length;\n    const upcomingCount = nonFreePeriodLessons.filter((l)=>l.status === 'upcoming').length;\n    console.log(`[Timetable API] Lesson status summary (${nonFreePeriodLessons.length} total): ${completedCount} completed, ${inProgressCount} in progress, ${upcomingCount} upcoming`);\n    // Log details of completed/in-progress lessons\n    const activeLessons = nonFreePeriodLessons.filter((l)=>l.status !== 'upcoming');\n    if (activeLessons.length > 0) {\n        console.log(`[Timetable API] Active lessons:`, activeLessons.map((l)=>({\n                lessonRef: l.lessonReference,\n                status: l.status,\n                phase: l.currentPhase,\n                sessionId: l.sessionId\n            })));\n    }\n    return updatedLessons;\n}\n// Get student grade level\nasync function getStudentGrade(studentId) {\n    try {\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        for (const format of studentIdFormats){\n            const studentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const studentSnap = await studentRef.get();\n            if (studentSnap.exists) {\n                const studentData = studentSnap.data();\n                return studentData?.gradeLevel || studentData?.grade || 'P5';\n            }\n        }\n        return 'P5'; // Default grade\n    } catch (error) {\n        console.error('[Timetable API] Error fetching student grade:', error);\n        return 'P5';\n    }\n}\n// Fetch lesson completion states from Firestore using session-based data structure\nasync function fetchLessonCompletionStates(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching lesson completion states for student: ${studentId}`);\n        // Try different student ID formats for querying\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        const completionStates = {};\n        let totalSessionsFound = 0;\n        for (const format of studentIdFormats){\n            try {\n                console.log(`[Timetable API] Querying lesson_states collection for student_id: ${format}`);\n                // Query the lesson_states collection for all sessions belonging to this student\n                const lessonStatesQuery = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('lesson_states').where('student_id', '==', format);\n                const querySnapshot = await lessonStatesQuery.get();\n                if (!querySnapshot.empty) {\n                    console.log(`[Timetable API] Found ${querySnapshot.size} session(s) for student ID format: ${format}`);\n                    totalSessionsFound += querySnapshot.size;\n                    // Process each session document\n                    querySnapshot.docs.forEach((sessionDoc)=>{\n                        const sessionData = sessionDoc.data();\n                        const sessionId = sessionDoc.id;\n                        console.log(`[Timetable API] Processing session ${sessionId}:`, {\n                            lesson_ref: sessionData.lesson_ref,\n                            current_phase: sessionData.current_phase,\n                            last_updated: sessionData.last_updated,\n                            student_id: sessionData.student_id\n                        });\n                        // Extract lesson reference and completion data\n                        const lessonRef = sessionData.lesson_ref;\n                        if (lessonRef) {\n                            // Determine status from current_phase\n                            let status = 'upcoming';\n                            if (sessionData.current_phase === 'lesson_completion' || sessionData.current_phase === 'completed') {\n                                status = 'completed';\n                            } else if (sessionData.current_phase && sessionData.current_phase !== 'diagnostic_start_probe') {\n                                status = 'in_progress';\n                            }\n                            // Check if we already have data for this lesson reference\n                            const existingData = completionStates[lessonRef];\n                            const currentTimestamp = sessionData.last_updated?.toDate?.() || sessionData.last_updated || new Date(0);\n                            const existingTimestamp = existingData?.lastUpdated || new Date(0);\n                            // Use the most recent session data for this lesson reference\n                            if (!existingData || currentTimestamp > existingTimestamp) {\n                                completionStates[lessonRef] = {\n                                    status: status,\n                                    completed: status === 'completed',\n                                    completedAt: status === 'completed' ? currentTimestamp : null,\n                                    progress: status === 'completed' ? 100 : status === 'in_progress' ? 50 : 0,\n                                    sessionId: sessionId,\n                                    currentPhase: sessionData.current_phase,\n                                    workingLevel: sessionData.current_session_working_level,\n                                    lastUpdated: currentTimestamp,\n                                    createdAt: sessionData.created_at?.toDate?.() || sessionData.created_at,\n                                    studentId: sessionData.student_id,\n                                    lessonRef: lessonRef\n                                };\n                            }\n                        } else {\n                            console.warn(`[Timetable API] Session ${sessionId} missing lesson_ref field`);\n                        }\n                    });\n                    break;\n                } else {\n                    console.log(`[Timetable API] No sessions found for student ID format: ${format}`);\n                }\n            } catch (formatError) {\n                console.warn(`[Timetable API] Error querying lesson states for format ${format}:`, formatError);\n                continue;\n            }\n        }\n        const completionCount = Object.keys(completionStates).length;\n        console.log(`[Timetable API] Processed ${totalSessionsFound} session(s), found ${completionCount} unique lesson completion records`);\n        // Log summary of completion states\n        if (completionCount > 0) {\n            const statusSummary = Object.values(completionStates).reduce((acc, state)=>{\n                acc[state.status] = (acc[state.status] || 0) + 1;\n                return acc;\n            }, {});\n            console.log(`[Timetable API] Completion status summary:`, statusSummary);\n        }\n        return completionStates;\n    } catch (error) {\n        console.error('[Timetable API] Error fetching lesson completion states:', error);\n        return {};\n    }\n}\n// Verify Firebase Auth token\nasync function verifyAuthToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader?.startsWith('Bearer ')) {\n            return null;\n        }\n        const idToken = authHeader.split('Bearer ')[1];\n        const decodedToken = await (0,firebase_admin_auth__WEBPACK_IMPORTED_MODULE_2__.getAuth)().verifyIdToken(idToken);\n        return {\n            uid: decodedToken.uid\n        };\n    } catch (error) {\n        console.error('Auth verification failed:', error);\n        return null;\n    }\n}\n// Fill any remaining empty slots with \"Free Period\"\nfunction fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade) {\n    const abbreviatedGrade = convertGradeToAbbreviation(studentGrade);\n    let freePeriodCounter = 1;\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                const freePeriod = {\n                    id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,\n                    lessonReference: `${abbreviatedGrade}-${String(academicWeek).padStart(3, '0')}-${String(freePeriodCounter).padStart(3, '0')}`,\n                    title: 'Free Period',\n                    subject: 'Free Period',\n                    subjectId: 'free_period',\n                    subjectCode: null,\n                    time: STANDARD_TIME_SLOTS[timeIndex],\n                    day: DAYS_OF_WEEK[dayIndex],\n                    duration: 45,\n                    status: 'upcoming',\n                    description: 'Free study period',\n                    grade: studentGrade,\n                    academicWeek: academicWeek,\n                    lessonNumberInWeek: freePeriodCounter,\n                    absoluteLessonNumber: null,\n                    totalWeeks: 30,\n                    teacher: null,\n                    cognitiveLoad: 1,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isFreePeriod: true\n                };\n                weekSchedule[dayIndex][timeIndex] = freePeriod;\n                freePeriodCounter++;\n            }\n        }\n    }\n}\nasync function GET(request) {\n    try {\n        // Check if Firebase Admin is properly initialized\n        if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db) {\n            console.error('[Timetable API] Firebase Admin not properly initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Database not available'\n            }, {\n                status: 500\n            });\n        }\n        // For development/testing, allow unauthenticated requests\n        // In production, uncomment the authentication check below\n        /*\r\n    const authResult = await verifyAuthToken(request);\r\n    if (!authResult) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Unauthorized' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n    */ const { searchParams } = new URL(request.url);\n        const studentId = searchParams.get('studentId');\n        const date = searchParams.get('date');\n        const weekParam = searchParams.get('week');\n        // Parse week parameter (default to week 1)\n        const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;\n        if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid week number. Must be between 1 and 30.'\n            }, {\n                status: 400\n            });\n        }\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);\n        // Fetch student grade level\n        const studentGrade = await getStudentGrade(studentId);\n        console.log(`[Timetable API] Student grade: ${studentGrade}`);\n        // Fetch student enrollments (this is now the single source of truth)\n        const enrollments = await fetchStudentEnrollments(studentId);\n        if (enrollments.length === 0) {\n            console.log('[Timetable API] No active enrollments found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    schedule: [],\n                    timetable: [],\n                    totalLessons: 0,\n                    enrollments: [],\n                    student_id: studentId,\n                    academic_week: academicWeek,\n                    total_weeks: 30,\n                    student_grade: studentGrade,\n                    date: date\n                }\n            });\n        }\n        // Generate weekly timetable based on enrollments\n        const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade);\n        // Fetch lesson completion states from Firestore\n        const completionStates = await fetchLessonCompletionStates(studentId);\n        // Apply actual completion states to the generated lessons\n        const scheduleWithCompletionStates = applyLessonCompletionStates(weeklySchedule, completionStates);\n        // Sort weekly schedule by day and time\n        const dayOrder = [\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday'\n        ];\n        scheduleWithCompletionStates.sort((a, b)=>{\n            const dayA = dayOrder.indexOf(a.day);\n            const dayB = dayOrder.indexOf(b.day);\n            if (dayA !== dayB) {\n                return dayA - dayB;\n            }\n            // Sort by time within the same day\n            if (a.time && b.time) {\n                return a.time.localeCompare(b.time);\n            }\n            return 0;\n        });\n        console.log(`[Timetable API] Returning ${scheduleWithCompletionStates.length} lessons for week ${academicWeek}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                schedule: scheduleWithCompletionStates,\n                timetable: scheduleWithCompletionStates,\n                totalLessons: scheduleWithCompletionStates.length,\n                enrollments: enrollments,\n                student_id: studentId,\n                academic_week: academicWeek,\n                total_weeks: 30,\n                student_grade: studentGrade,\n                date: date\n            }\n        });\n    } catch (error) {\n        console.error('[Timetable API] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch timetable',\n            details: error instanceof Error ? error.stack : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/timetable/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "firebase-admin/auth":
/*!**************************************!*\
  !*** external "firebase-admin/auth" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/auth");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();