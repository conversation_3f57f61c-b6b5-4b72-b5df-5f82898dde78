/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/timetable/route";
exports.ids = ["app/api/timetable/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/timetable/route.ts */ \"(rsc)/./src/app/api/timetable/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/timetable/route\",\n        pathname: \"/api/timetable\",\n        filename: \"route\",\n        bundlePath: \"app/api/timetable/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\timetable\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_timetable_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/timetable/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/timetable/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n/* harmony import */ var _lib_time_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/time-utils */ \"(rsc)/./src/lib/time-utils.ts\");\n/* harmony import */ var firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase-admin/auth */ \"firebase-admin/auth\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__]);\nfirebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Define the standard time slots for timetable generation\nconst STANDARD_TIME_SLOTS = [\n    '08:00-08:45',\n    '08:50-09:35',\n    '09:40-10:25',\n    '10:40-11:25',\n    '11:30-12:15',\n    '13:15-14:00',\n    '14:15-15:00'\n];\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\n// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)\nconst SUBJECT_COGNITIVE_LOADS = {\n    'mathematics': 9,\n    'english_language': 8,\n    'basic_science_and_technology': 7,\n    'basic_science': 7,\n    'social_studies': 6,\n    'computer_studies': 8,\n    'computing': 8,\n    'artificial_intelligence': 9,\n    'french': 7,\n    'christian_religious_knowledge': 5,\n    'national_values_education': 4,\n    'entrepreneurship_education': 5,\n    'entrepreneurship': 5,\n    'financial_literacy': 6,\n    'creative_arts': 3,\n    'cultural_and_creative_arts': 3,\n    'art_and_design': 3,\n    'physical_health_education': 2,\n    'project_based_excellence': 6\n};\n// Calculate lesson reference for a specific week and subject\nfunction generateLessonReference(grade, subjectCode, academicWeek, lessonNumber, lessonsPerWeek) {\n    // Calculate the absolute lesson number across all weeks\n    const absoluteLessonNumber = (academicWeek - 1) * lessonsPerWeek + lessonNumber;\n    // Format lesson number with leading zeros (e.g., 001, 002, etc.)\n    const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');\n    // Convert grade to proper abbreviated format\n    const abbreviatedGrade = convertGradeToAbbreviation(grade);\n    return `${abbreviatedGrade}-${subjectCode}-${formattedLessonNumber}`;\n}\n// Convert grade level to proper abbreviated format\nfunction convertGradeToAbbreviation(grade) {\n    // Handle different grade formats\n    const gradeStr = grade.toLowerCase().trim();\n    // Primary grades\n    if (gradeStr.includes('primary') || gradeStr.startsWith('p')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `P${match[1]}`;\n        }\n    }\n    // Junior Secondary\n    if (gradeStr.includes('junior') || gradeStr.includes('jss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `JSS${match[1]}`;\n        }\n    }\n    // Senior Secondary\n    if (gradeStr.includes('senior') || gradeStr.includes('sss')) {\n        const match = gradeStr.match(/(\\d+)/);\n        if (match) {\n            return `SSS${match[1]}`;\n        }\n    }\n    // If already in correct format, return as is\n    if (/^(P|JSS|SSS)\\d+$/i.test(grade)) {\n        return grade.toUpperCase();\n    }\n    // Default fallback\n    return grade;\n}\n// Fetch student enrollments from Firestore\nasync function fetchStudentEnrollments(studentId) {\n    try {\n        console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        let enrollments = [];\n        let studentRef = null;\n        for (const format of studentIdFormats){\n            console.log(`[Timetable API] Trying student ID format: ${format}`);\n            const testStudentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const testStudentSnap = await testStudentRef.get();\n            if (testStudentSnap.exists) {\n                console.log(`[Timetable API] Found student document with ID: ${format}`);\n                studentRef = testStudentRef;\n                break;\n            }\n        }\n        if (!studentRef) {\n            console.warn(`[Timetable API] No student document found for any ID format`);\n            return [];\n        }\n        // Fetch enrollments from the enrollments subcollection\n        const enrollmentsRef = studentRef.collection('enrollments');\n        const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();\n        console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);\n        enrollmentsSnap.docs.forEach((doc)=>{\n            const data = doc.data();\n            const subjectId = doc.id;\n            enrollments.push({\n                subjectId: subjectId,\n                subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase()),\n                subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),\n                lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,\n                status: data.status,\n                enrolledAt: data.enrolledAt || data.enrolled_at,\n                cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load\n            });\n        });\n        console.log(`[Timetable API] Processed enrollments:`, enrollments);\n        return enrollments;\n    } catch (error) {\n        console.error(`[Timetable API] Error fetching enrollments:`, error);\n        return [];\n    }\n}\n// Get subject code from subject ID\nfunction getSubjectCodeFromId(subjectId) {\n    const codeMap = {\n        'mathematics': 'MAT',\n        'english_language': 'ENG',\n        'basic_science_and_technology': 'BST',\n        'basic_science': 'BST',\n        'social_studies': 'SST',\n        'computer_studies': 'COM',\n        'computing': 'COM',\n        'creative_arts': 'ART',\n        'cultural_and_creative_arts': 'CCA',\n        'art_and_design': 'ART',\n        'physical_health_education': 'PHE',\n        'national_values_education': 'NVE',\n        'entrepreneurship_education': 'ENT',\n        'entrepreneurship': 'ENT',\n        'financial_literacy': 'FIL',\n        'french': 'FRE',\n        'artificial_intelligence': 'AI',\n        'project_based_excellence': 'PBE',\n        'christian_religious_knowledge': 'CRK'\n    };\n    return codeMap[subjectId] || 'GEN';\n}\n// Distribute lessons across the week based on lessons per week and cognitive load\nfunction distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade) {\n    const weeklyLessons = [];\n    console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);\n    // Create a weekly schedule grid: [day][timeSlot]\n    const weekSchedule = DAYS_OF_WEEK.map(()=>new Array(STANDARD_TIME_SLOTS.length).fill(null));\n    // Calculate total lessons needed\n    const totalLessonsNeeded = enrollments.reduce((sum, e)=>sum + e.lessonsPerWeek, 0);\n    const totalSlotsAvailable = DAYS_OF_WEEK.length * STANDARD_TIME_SLOTS.length; // 5 days × 7 slots = 35\n    console.log(`[Timetable API] Total lessons needed: ${totalLessonsNeeded}, Total slots available: ${totalSlotsAvailable}`);\n    // Sort enrollments by cognitive load (highest first) for optimal time slot assignment\n    const sortedEnrollments = [\n        ...enrollments\n    ].sort((a, b)=>(b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));\n    console.log(`[Timetable API] Sorted subjects by cognitive load:`, sortedEnrollments.map((e)=>`${e.subjectName} (${e.cognitiveLoad}) - ${e.lessonsPerWeek} lessons`));\n    // Track how many lessons each subject has been assigned\n    const subjectLessonCounts = {};\n    // Initialize all subjects\n    sortedEnrollments.forEach((enrollment)=>{\n        subjectLessonCounts[enrollment.subjectId] = 0;\n    });\n    // Schedule all lessons using a more aggressive approach\n    scheduleAllLessonsOptimally(sortedEnrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);\n    // Fill any remaining empty slots with \"Free Period\"\n    fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade);\n    // Convert the schedule grid back to lesson objects\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            const lesson = weekSchedule[dayIndex][timeIndex];\n            if (lesson) {\n                weeklyLessons.push(lesson);\n            }\n        }\n    }\n    console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (target: ${totalSlotsAvailable})`);\n    // Log final subject distribution\n    const finalCounts = {};\n    weeklyLessons.forEach((lesson)=>{\n        if (lesson.subjectId && lesson.subjectId !== 'free_period') {\n            finalCounts[lesson.subjectId] = (finalCounts[lesson.subjectId] || 0) + 1;\n        }\n    });\n    console.log(`[Timetable API] Final lesson distribution:`, finalCounts);\n    return weeklyLessons;\n}\n// New optimized scheduling function that ensures all lessons are scheduled\nfunction scheduleAllLessonsOptimally(enrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts) {\n    // Create a list of all lessons that need to be scheduled\n    const lessonsToSchedule = [];\n    enrollments.forEach((enrollment)=>{\n        for(let i = 1; i <= enrollment.lessonsPerWeek; i++){\n            lessonsToSchedule.push({\n                enrollment,\n                lessonNumber: i\n            });\n        }\n    });\n    console.log(`[Timetable API] Total lessons to schedule: ${lessonsToSchedule.length}`);\n    // Sort lessons by cognitive load (highest first) and then by lesson number\n    lessonsToSchedule.sort((a, b)=>{\n        const cognitiveLoadDiff = (b.enrollment.cognitiveLoad || 5) - (a.enrollment.cognitiveLoad || 5);\n        if (cognitiveLoadDiff !== 0) return cognitiveLoadDiff;\n        return a.lessonNumber - b.lessonNumber;\n    });\n    // Track lessons per day for each subject to enforce distribution rules\n    const subjectDailyCount = {};\n    enrollments.forEach((enrollment)=>{\n        subjectDailyCount[enrollment.subjectId] = new Array(DAYS_OF_WEEK.length).fill(0);\n    });\n    // Schedule each lesson\n    for (const lessonToSchedule of lessonsToSchedule){\n        const { enrollment, lessonNumber } = lessonToSchedule;\n        let scheduled = false;\n        // Get preferred time slots based on cognitive load\n        const preferredTimeSlots = getPreferredTimeSlots(enrollment.cognitiveLoad || 5);\n        // Try to schedule on each day, prioritizing days with fewer lessons for this subject\n        const dayPriority = Array.from({\n            length: DAYS_OF_WEEK.length\n        }, (_, i)=>i).sort((a, b)=>subjectDailyCount[enrollment.subjectId][a] - subjectDailyCount[enrollment.subjectId][b]);\n        for (const dayIndex of dayPriority){\n            // Special rule for Mathematics: allow up to 2 lessons on Monday, 1 on other days\n            const maxLessonsPerDay = enrollment.subjectId === 'mathematics' && dayIndex === 0 ? 2 : enrollment.lessonsPerWeek >= 6 ? 2 : 1;\n            if (subjectDailyCount[enrollment.subjectId][dayIndex] >= maxLessonsPerDay) {\n                continue; // Skip this day if we've reached the limit\n            }\n            // Try preferred time slots first\n            for (const timeIndex of preferredTimeSlots){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    // Check if this would create back-to-back lessons (avoid if possible)\n                    const hasAdjacentLesson = timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId || timeIndex < STANDARD_TIME_SLOTS.length - 1 && weekSchedule[dayIndex][timeIndex + 1]?.subjectId === enrollment.subjectId;\n                    // For subjects with multiple lessons per week, allow adjacent lessons if necessary\n                    if (!hasAdjacentLesson || enrollment.lessonsPerWeek >= 4) {\n                        subjectLessonCounts[enrollment.subjectId]++;\n                        subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                        const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                        weekSchedule[dayIndex][timeIndex] = lesson;\n                        scheduled = true;\n                        break;\n                    }\n                }\n            }\n            if (scheduled) break;\n            // If no preferred slot worked, try any available slot on this day\n            for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n                if (weekSchedule[dayIndex][timeIndex] === null) {\n                    subjectLessonCounts[enrollment.subjectId]++;\n                    subjectDailyCount[enrollment.subjectId][dayIndex]++;\n                    const lesson = createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade);\n                    weekSchedule[dayIndex][timeIndex] = lesson;\n                    scheduled = true;\n                    break;\n                }\n            }\n            if (scheduled) break;\n        }\n        if (!scheduled) {\n            console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} lesson ${lessonNumber} - no available slots`);\n        }\n    }\n    // Log scheduling results\n    console.log(`[Timetable API] Scheduled ${lessonsToSchedule.filter((_, i)=>i < Object.values(subjectLessonCounts).reduce((a, b)=>a + b, 0)).length} out of ${lessonsToSchedule.length} lessons`);\n}\n// Get preferred time slots based on cognitive load\nfunction getPreferredTimeSlots(cognitiveLoad) {\n    if (cognitiveLoad >= 8) {\n        // High cognitive load: prefer early morning slots\n        return [\n            0,\n            1,\n            2,\n            3,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 6) {\n        // Medium cognitive load: prefer mid-morning slots\n        return [\n            1,\n            2,\n            3,\n            0,\n            4,\n            5,\n            6\n        ];\n    } else if (cognitiveLoad >= 4) {\n        // Low-medium cognitive load: prefer afternoon slots\n        return [\n            3,\n            4,\n            5,\n            2,\n            6,\n            1,\n            0\n        ];\n    } else {\n        // Low cognitive load: prefer late slots\n        return [\n            5,\n            6,\n            4,\n            3,\n            2,\n            1,\n            0\n        ];\n    }\n}\n// Create a lesson object\nfunction createLessonObject(enrollment, lessonNumber, dayIndex, timeIndex, academicWeek, studentGrade) {\n    const lessonReference = generateLessonReference(studentGrade, enrollment.subjectCode, academicWeek, lessonNumber, enrollment.lessonsPerWeek);\n    // Calculate lesson status\n    let status = 'upcoming';\n    try {\n        const statusResult = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_2__.calculateLessonStatus)(STANDARD_TIME_SLOTS[timeIndex], DAYS_OF_WEEK[dayIndex], new Date());\n        status = statusResult.status;\n    } catch (statusError) {\n        console.warn('[Timetable API] Error calculating lesson status:', statusError);\n    }\n    return {\n        id: `${lessonReference}_week${academicWeek}`,\n        lessonReference: lessonReference,\n        title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,\n        subject: enrollment.subjectName,\n        subjectId: enrollment.subjectId,\n        subjectCode: enrollment.subjectCode,\n        time: STANDARD_TIME_SLOTS[timeIndex],\n        day: DAYS_OF_WEEK[dayIndex],\n        duration: 45,\n        status: status,\n        description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,\n        grade: studentGrade,\n        academicWeek: academicWeek,\n        lessonNumberInWeek: lessonNumber,\n        absoluteLessonNumber: (academicWeek - 1) * enrollment.lessonsPerWeek + lessonNumber,\n        totalWeeks: 30,\n        teacher: 'AI Instructor',\n        cognitiveLoad: enrollment.cognitiveLoad,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    };\n}\n// Get student grade level\nasync function getStudentGrade(studentId) {\n    try {\n        // Try different student ID formats\n        const studentIdFormats = [\n            studentId,\n            `andrea_ugono_33305`,\n            studentId.toLowerCase(),\n            studentId.replace(/\\s+/g, '_').toLowerCase()\n        ];\n        for (const format of studentIdFormats){\n            const studentRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(format);\n            const studentSnap = await studentRef.get();\n            if (studentSnap.exists) {\n                const studentData = studentSnap.data();\n                return studentData?.gradeLevel || studentData?.grade || 'P5';\n            }\n        }\n        return 'P5'; // Default grade\n    } catch (error) {\n        console.error('[Timetable API] Error fetching student grade:', error);\n        return 'P5';\n    }\n}\n// Verify Firebase Auth token\nasync function verifyAuthToken(request) {\n    try {\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader?.startsWith('Bearer ')) {\n            return null;\n        }\n        const idToken = authHeader.split('Bearer ')[1];\n        const decodedToken = await (0,firebase_admin_auth__WEBPACK_IMPORTED_MODULE_3__.getAuth)().verifyIdToken(idToken);\n        return {\n            uid: decodedToken.uid\n        };\n    } catch (error) {\n        console.error('Auth verification failed:', error);\n        return null;\n    }\n}\n// Fill any remaining empty slots with \"Free Period\"\nfunction fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade) {\n    const abbreviatedGrade = convertGradeToAbbreviation(studentGrade);\n    for(let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++){\n        for(let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++){\n            if (weekSchedule[dayIndex][timeIndex] === null) {\n                const freePeriod = {\n                    id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,\n                    lessonReference: `${abbreviatedGrade}-FREE-${String(academicWeek).padStart(3, '0')}-${String(timeIndex + 1).padStart(3, '0')}`,\n                    title: 'Free Period',\n                    subject: 'Free Period',\n                    subjectId: 'free_period',\n                    subjectCode: 'FREE',\n                    time: STANDARD_TIME_SLOTS[timeIndex],\n                    day: DAYS_OF_WEEK[dayIndex],\n                    duration: 45,\n                    status: 'upcoming',\n                    description: 'Free study period',\n                    grade: studentGrade,\n                    academicWeek: academicWeek,\n                    lessonNumberInWeek: timeIndex + 1,\n                    absoluteLessonNumber: null,\n                    totalWeeks: 30,\n                    teacher: null,\n                    cognitiveLoad: 1,\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    isFreePeriod: true\n                };\n                weekSchedule[dayIndex][timeIndex] = freePeriod;\n            }\n        }\n    }\n}\nasync function GET(request) {\n    try {\n        // Check if Firebase Admin is properly initialized\n        if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db) {\n            console.error('[Timetable API] Firebase Admin not properly initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Database not available'\n            }, {\n                status: 500\n            });\n        }\n        // For development/testing, allow unauthenticated requests\n        // In production, uncomment the authentication check below\n        /*\r\n    const authResult = await verifyAuthToken(request);\r\n    if (!authResult) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Unauthorized' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n    */ const { searchParams } = new URL(request.url);\n        const studentId = searchParams.get('studentId');\n        const date = searchParams.get('date');\n        const weekParam = searchParams.get('week');\n        // Parse week parameter (default to week 1)\n        const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;\n        if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Invalid week number. Must be between 1 and 30.'\n            }, {\n                status: 400\n            });\n        }\n        if (!studentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student ID is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);\n        // Fetch student grade level\n        const studentGrade = await getStudentGrade(studentId);\n        console.log(`[Timetable API] Student grade: ${studentGrade}`);\n        // Fetch student enrollments (this is now the single source of truth)\n        const enrollments = await fetchStudentEnrollments(studentId);\n        if (enrollments.length === 0) {\n            console.log('[Timetable API] No active enrollments found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: {\n                    schedule: [],\n                    timetable: [],\n                    totalLessons: 0,\n                    enrollments: [],\n                    student_id: studentId,\n                    academic_week: academicWeek,\n                    total_weeks: 30,\n                    student_grade: studentGrade,\n                    date: date\n                }\n            });\n        }\n        // Generate weekly timetable based on enrollments\n        const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade);\n        // Sort weekly schedule by day and time\n        const dayOrder = [\n            'Monday',\n            'Tuesday',\n            'Wednesday',\n            'Thursday',\n            'Friday'\n        ];\n        weeklySchedule.sort((a, b)=>{\n            const dayA = dayOrder.indexOf(a.day);\n            const dayB = dayOrder.indexOf(b.day);\n            if (dayA !== dayB) {\n                return dayA - dayB;\n            }\n            // Sort by time within the same day\n            if (a.time && b.time) {\n                return a.time.localeCompare(b.time);\n            }\n            return 0;\n        });\n        console.log(`[Timetable API] Returning ${weeklySchedule.length} lessons for week ${academicWeek}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                schedule: weeklySchedule,\n                timetable: weeklySchedule,\n                totalLessons: weeklySchedule.length,\n                enrollments: enrollments,\n                student_id: studentId,\n                academic_week: academicWeek,\n                total_weeks: 30,\n                student_grade: studentGrade,\n                date: date\n            }\n        });\n    } catch (error) {\n        console.error('[Timetable API] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Failed to fetch timetable',\n            details: error instanceof Error ? error.stack : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/timetable/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/time-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/time-utils.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateLessonStatus: () => (/* binding */ calculateLessonStatus),\n/* harmony export */   dayNameToNumber: () => (/* binding */ dayNameToNumber),\n/* harmony export */   formatTimeRange: () => (/* binding */ formatTimeRange),\n/* harmony export */   getCurrentAcademicWeek: () => (/* binding */ getCurrentAcademicWeek),\n/* harmony export */   getCurrentDayName: () => (/* binding */ getCurrentDayName),\n/* harmony export */   getTimeUntilNext: () => (/* binding */ getTimeUntilNext),\n/* harmony export */   isCurrentTimeInRange: () => (/* binding */ isCurrentTimeInRange),\n/* harmony export */   minutesToTimeString: () => (/* binding */ minutesToTimeString),\n/* harmony export */   parseTimeRange: () => (/* binding */ parseTimeRange),\n/* harmony export */   parseTimeToMinutes: () => (/* binding */ parseTimeToMinutes)\n/* harmony export */ });\n// src/lib/time-utils.ts\n// Standardized time parsing utilities for the application\n/**\r\n * Parses a time string in various formats and returns minutes since midnight\r\n * Supports formats: \"HH:MM\", \"H:MM\", with optional AM/PM\r\n */ function parseTimeToMinutes(timeStr) {\n    if (!timeStr || typeof timeStr !== 'string') {\n        throw new Error(`Invalid time input: ${timeStr}`);\n    }\n    const cleanTimeStr = timeStr.trim().toLowerCase();\n    // Handle AM/PM format\n    let isPM = false;\n    let timeOnly = cleanTimeStr;\n    if (cleanTimeStr.includes('am') || cleanTimeStr.includes('pm')) {\n        isPM = cleanTimeStr.includes('pm');\n        timeOnly = cleanTimeStr.replace(/\\s*(am|pm)\\s*/, '');\n    }\n    const parts = timeOnly.split(':');\n    if (parts.length !== 2) {\n        throw new Error(`Invalid time format: ${cleanTimeStr}`);\n    }\n    let hours = parseInt(parts[0], 10);\n    const minutes = parseInt(parts[1], 10);\n    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n        throw new Error(`Invalid time value in ${cleanTimeStr}`);\n    }\n    // Handle AM/PM conversion\n    if (isPM && hours !== 12) {\n        hours += 12;\n    } else if (!isPM && hours === 12) {\n        hours = 0;\n    }\n    return hours * 60 + minutes;\n}\n/**\r\n * Parses a time range string in format \"HH:MM - HH:MM\" or \"HH:MM-HH:MM\"\r\n */ function parseTimeRange(timeRangeStr) {\n    try {\n        if (!timeRangeStr || typeof timeRangeStr !== 'string') {\n            return {\n                startMinutes: 0,\n                endMinutes: 0,\n                isValid: false,\n                error: 'Invalid time range input'\n            };\n        }\n        // Split by hyphen, handle spaces around it\n        const parts = timeRangeStr.split(/\\s*-\\s*/);\n        if (parts.length !== 2) {\n            return {\n                startMinutes: 0,\n                endMinutes: 0,\n                isValid: false,\n                error: 'Time range must be in format \"HH:MM - HH:MM\"'\n            };\n        }\n        const startMinutes = parseTimeToMinutes(parts[0]);\n        const endMinutes = parseTimeToMinutes(parts[1]);\n        if (endMinutes <= startMinutes) {\n            return {\n                startMinutes,\n                endMinutes,\n                isValid: false,\n                error: 'End time must be after start time'\n            };\n        }\n        return {\n            startMinutes,\n            endMinutes,\n            isValid: true\n        };\n    } catch (error) {\n        return {\n            startMinutes: 0,\n            endMinutes: 0,\n            isValid: false,\n            error: error instanceof Error ? error.message : 'Unknown parsing error'\n        };\n    }\n}\n/**\r\n * Converts minutes since midnight to time string\r\n */ function minutesToTimeString(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\n/**\r\n * Gets the current day of week as string\r\n */ function getCurrentDayName() {\n    const days = [\n        'Sunday',\n        'Monday',\n        'Tuesday',\n        'Wednesday',\n        'Thursday',\n        'Friday',\n        'Saturday'\n    ];\n    return days[new Date().getDay()];\n}\n/**\r\n * Converts day name to number (0 = Sunday, 1 = Monday, etc.)\r\n */ function dayNameToNumber(dayName) {\n    const days = {\n        'sunday': 0,\n        'monday': 1,\n        'tuesday': 2,\n        'wednesday': 3,\n        'thursday': 4,\n        'friday': 5,\n        'saturday': 6\n    };\n    return days[dayName.toLowerCase()] ?? -1;\n}\n/**\r\n * Determines lesson status based on time and day\r\n */ function calculateLessonStatus(timeRange, lessonDay, currentTime, progressStatus) {\n    const now = currentTime || new Date();\n    const currentMinutes = now.getHours() * 60 + now.getMinutes();\n    const currentDay = now.getDay();\n    const lessonDayNumber = dayNameToNumber(lessonDay);\n    const isToday = currentDay === lessonDayNumber;\n    // If progress shows completed, always mark as completed\n    if (progressStatus === 'completed') {\n        return {\n            status: 'completed',\n            isToday\n        };\n    }\n    // Parse the time range\n    const timeParseResult = parseTimeRange(timeRange);\n    if (!timeParseResult.isValid) {\n        return {\n            status: 'upcoming',\n            isToday,\n            error: timeParseResult.error\n        };\n    }\n    const { startMinutes, endMinutes } = timeParseResult;\n    // If lesson is on a different day\n    if (!isToday) {\n        return {\n            status: lessonDayNumber < currentDay ? 'completed' : 'upcoming',\n            isToday: false\n        };\n    }\n    // Lesson is today - check time\n    if (currentMinutes >= endMinutes) {\n        return {\n            status: 'completed',\n            isToday: true\n        };\n    } else if (currentMinutes >= startMinutes && currentMinutes < endMinutes) {\n        return {\n            status: 'current',\n            timeUntilEnd: endMinutes - currentMinutes,\n            isToday: true\n        };\n    } else {\n        return {\n            status: 'upcoming',\n            timeUntilStart: startMinutes - currentMinutes,\n            isToday: true\n        };\n    }\n}\n/**\r\n * Formats time range for display\r\n */ function formatTimeRange(startTime, endTime) {\n    try {\n        const startMinutes = parseTimeToMinutes(startTime);\n        const endMinutes = parseTimeToMinutes(endTime);\n        return `${minutesToTimeString(startMinutes)} - ${minutesToTimeString(endMinutes)}`;\n    } catch (error) {\n        return `${startTime} - ${endTime}`; // Fallback to original strings\n    }\n}\n/**\r\n * Checks if current time is within a time range\r\n */ function isCurrentTimeInRange(timeRange, currentTime) {\n    const result = calculateLessonStatus(timeRange, getCurrentDayName(), currentTime);\n    return result.status === 'current';\n}\n/**\r\n * Gets time until next event (start or end)\r\n */ function getTimeUntilNext(timeRange, currentTime) {\n    const status = calculateLessonStatus(timeRange, getCurrentDayName(), currentTime);\n    if (status.timeUntilStart !== undefined) {\n        const hours = Math.floor(status.timeUntilStart / 60);\n        const mins = status.timeUntilStart % 60;\n        return {\n            type: 'start',\n            minutes: status.timeUntilStart,\n            message: hours > 0 ? `${hours}h ${mins}m until start` : `${mins}m until start`\n        };\n    }\n    if (status.timeUntilEnd !== undefined) {\n        const hours = Math.floor(status.timeUntilEnd / 60);\n        const mins = status.timeUntilEnd % 60;\n        return {\n            type: 'end',\n            minutes: status.timeUntilEnd,\n            message: hours > 0 ? `${hours}h ${mins}m remaining` : `${mins}m remaining`\n        };\n    }\n    return {\n        type: 'none',\n        minutes: 0,\n        message: status.status === 'completed' ? 'Completed' : 'Upcoming'\n    };\n}\n/**\r\n * Calculate the current academic week based on the school year start date.\r\n * Academic year starts in early September and has 30 teaching weeks.\r\n * Returns a value between 1 and 30.\r\n */ function getCurrentAcademicWeek() {\n    const now = new Date();\n    const currentYear = now.getFullYear();\n    // Academic year starts in September of the current year if we're past August,\n    // otherwise it started in September of the previous year\n    const academicYearStart = now.getMonth() >= 8 ? currentYear : currentYear - 1;\n    // School typically starts around September 1st\n    const schoolStartDate = new Date(academicYearStart, 8, 1); // September 1st\n    // Calculate the difference in weeks\n    const timeDiffMs = now.getTime() - schoolStartDate.getTime();\n    const weeksDiff = Math.floor(timeDiffMs / (1000 * 60 * 60 * 24 * 7));\n    // Ensure we return a value between 1 and 30\n    const academicWeek = Math.max(1, Math.min(30, weeksDiff + 1));\n    console.log(`[getCurrentAcademicWeek] School start: ${schoolStartDate.toDateString()}, Current: ${now.toDateString()}, Week: ${academicWeek}`);\n    return academicWeek;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/time-utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "firebase-admin/auth":
/*!**************************************!*\
  !*** external "firebase-admin/auth" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("firebase-admin/auth");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftimetable%2Froute&page=%2Fapi%2Ftimetable%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftimetable%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();