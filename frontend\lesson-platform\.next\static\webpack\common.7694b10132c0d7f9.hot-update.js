"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("common",{

/***/ "(app-pages-browser)/./src/components/shadcn/button.tsx":
/*!******************************************!*\
  !*** ./src/components/shadcn/button.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n// components/shadcn/button.tsx\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-400 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700\",\n            destructive: \"bg-red-500 text-white hover:bg-red-600\",\n            outline: \"border border-gray-200 hover:bg-gray-100\",\n            secondary: \"bg-gray-200 text-gray-900 hover:bg-gray-300\",\n            ghost: \"hover:bg-gray-100\",\n            link: \"underline-offset-4 hover:underline text-blue-600\"\n        },\n        size: {\n            default: \"h-10 py-2 px-4\",\n            sm: \"h-9 px-3 rounded-md\",\n            lg: \"h-11 px-8 rounded-md\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, size, asChild = false, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\shadcn\\\\button.tsx\",\n        lineNumber: 42,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Button;\nButton.displayName = \"Button\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Button$React.forwardRef\");\n$RefreshReg$(_c1, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shadcn/button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   extractContextFromRequest: () => (/* binding */ extractContextFromRequest),\n/* harmony export */   fetchWithErrorHandling: () => (/* binding */ fetchWithErrorHandling),\n/* harmony export */   formatGradeLevelForDisplay: () => (/* binding */ formatGradeLevelForDisplay),\n/* harmony export */   getParentId: () => (/* binding */ getParentId),\n/* harmony export */   mapFrontendToBackendFields: () => (/* binding */ mapFrontendToBackendFields),\n/* harmony export */   normalizeGradeLevel: () => (/* binding */ normalizeGradeLevel),\n/* harmony export */   prepareBackendRequest: () => (/* binding */ prepareBackendRequest),\n/* harmony export */   returnToParentAccount: () => (/* binding */ returnToParentAccount),\n/* harmony export */   switchToChildAccount: () => (/* binding */ switchToChildAccount)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.m.js\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Utility function to combine Tailwind CSS class names intelligently.\r\n * Handles merging and conflict resolution.\r\n * @param inputs Class values to combine.\r\n * @returns Merged class string.\r\n */ function cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\r\n * Formats a grade level string for display.\r\n * Handles formats like \"primary-5\", \"jss1\", \"SSS2\", \"jss-1-3\", etc.\r\n * @param gradeLevelInput Raw grade level string (e.g., \"primary-5\", \"jss1\")\r\n * @returns Formatted string (e.g., \"Primary 5\", \"Junior Secondary School 1\", \"Junior Secondary School 1-3\") or the original input if no specific format matches.\r\n */ function formatGradeLevelForDisplay(gradeLevelInput) {\n    if (!gradeLevelInput) {\n        return 'N/A'; // Or return empty string ''\n    }\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Handle \"primary-X\" format\n    if (input.startsWith('primary-')) {\n        const parts = input.split('-');\n        if (parts.length === 2 && !isNaN(parseInt(parts[1], 10))) {\n            return \"Primary \".concat(parts[1]);\n        }\n    }\n    // Handle specific range case \"jss-1-3\" first\n    if (input === 'jss-1-3') {\n        return 'Junior Secondary School 1-3';\n    }\n    // Handle \"jssX\" or \"jss-X\" format (single number)\n    if (input.startsWith('jss')) {\n        const numPart = input.replace('jss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"Junior Secondary School \".concat(numPart);\n        }\n    }\n    // Handle specific range case \"sss-1-3\" first\n    if (input === 'sss-1-3') {\n        return 'Senior Secondary School 1-3';\n    }\n    // Handle \"sssX\" or \"sss-X\" format (single number)\n    if (input.startsWith('sss')) {\n        const numPart = input.replace('sss', '').replace('-', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"Senior Secondary School \".concat(numPart);\n        }\n    }\n    // Add more specific rules if needed (e.g., \"P5\" -> \"Primary 5\")\n    if (input === 'p5') {\n        return 'Primary 5';\n    }\n    // Add more rules like the one above for P1, P2, etc. if needed\n    // Fallback: Return the original input string if no rules matched\n    // Consider capitalizing if appropriate as a fallback\n    // return gradeLevelInput.charAt(0).toUpperCase() + gradeLevelInput.slice(1);\n    return gradeLevelInput;\n}\n/**\r\n * Normalizes various grade level input strings to a consistent internal format.\r\n * Example: \"Primary 5\" -> \"primary-5\", \"Junior Secondary School 1\" -> \"jss1\"\r\n * @param gradeLevelInput Raw or display grade level string.\r\n * @returns Normalized string (lowercase, specific format) or original lowercase if no rule matches.\r\n */ function normalizeGradeLevel(gradeLevelInput) {\n    if (!gradeLevelInput) return '';\n    const input = gradeLevelInput.trim().toLowerCase();\n    // Normalization rules\n    if (input.startsWith('primary')) {\n        const numPart = input.replace('primary', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"primary-\".concat(numPart); // \"primary 5\" -> \"primary-5\"\n        }\n    }\n    if (input.startsWith('junior secondary school')) {\n        // Handle range first\n        if (input === 'junior secondary school 1-3') return 'jss-1-3';\n        const numPart = input.replace('junior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"jss\".concat(numPart); // \"Junior Secondary School 1\" -> \"jss1\"\n        }\n    }\n    if (input.startsWith('senior secondary school')) {\n        // Handle range first\n        if (input === 'senior secondary school 1-3') return 'sss-1-3';\n        const numPart = input.replace('senior secondary school', '').trim();\n        if (!isNaN(parseInt(numPart, 10))) {\n            return \"sss\".concat(numPart); // \"Senior Secondary School 2\" -> \"sss2\"\n        }\n    }\n    // Add more normalization rules as needed (e.g., p5 -> primary-5)\n    if (input === 'p5') return 'primary-5';\n    // Add rules for p1, p2, etc.\n    // Return input directly if it already matches a normalized format or no rule applies\n    return input;\n}\n// lib/api-utils.ts\n/**\r\n * Helper function to fetch data with built-in error handling\r\n */ async function fetchWithErrorHandling(url, options) {\n    try {\n        console.log(\"Fetching: \".concat(url));\n        const response = await fetch(url, options);\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error (\".concat(response.status, \"):\"), errorText);\n            throw new Error(\"API request failed with status \".concat(response.status, \": \").concat(response.statusText));\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        return data;\n    } catch (error) {\n        console.error('Fetch error:', error);\n        throw error;\n    }\n}\n/**\r\n * Helper to get parent ID from localStorage\r\n */ function getParentId() {\n    return localStorage.getItem('parent_id') || localStorage.getItem('current_session');\n}\n/**\r\n * Helper to switch to a child's account (for parent viewing)\r\n */ function switchToChildAccount(childId, parentId) {\n    // Store the child's session ID in localStorage\n    localStorage.setItem('current_session', childId);\n    // Set parent ID for returning back\n    localStorage.setItem('parent_id', parentId);\n    // Set flag to indicate parent is viewing child's dashboard\n    localStorage.setItem('viewing_as_child', 'true');\n    // Store a message to display on the child's dashboard\n    localStorage.setItem('parentEnrollmentMessage', \"You're viewing your child's dashboard. All actions will be performed on their behalf.\");\n}\n/**\r\n * Helper to switch back to parent account\r\n */ function returnToParentAccount() {\n    const parentId = localStorage.getItem('parent_id');\n    if (parentId) {\n        // Clear the viewing_as_child flag\n        localStorage.removeItem('viewing_as_child');\n        // Set the parent ID as the current session\n        localStorage.setItem('current_session', parentId);\n        // Clear the parent message\n        localStorage.removeItem('parentEnrollmentMessage');\n    }\n}\n/**\r\n * Extracts context parameters from a request, including URL parameters and headers.\r\n * Used to properly pass required data to backend APIs.\r\n */ function extractContextFromRequest(request) {\n    const url = new URL(request.url);\n    return {\n        studentId: request.headers.get('X-Student-ID') || url.searchParams.get('studentId'),\n        country: request.headers.get('X-Country') || url.searchParams.get('country'),\n        curriculum: request.headers.get('X-Curriculum') || url.searchParams.get('curriculum'),\n        grade: request.headers.get('X-Grade') || url.searchParams.get('grade'),\n        level: request.headers.get('X-Level') || url.searchParams.get('level'),\n        subject: request.headers.get('X-Subject') || url.searchParams.get('subject')\n    };\n}\n/**\r\n * Maps frontend field names to backend field names for API requests.\r\n * Helps maintain consistent API communication when field naming differs.\r\n */ function mapFrontendToBackendFields(data) {\n    const fieldMapping = {\n        'lesson_ref': 'lessonRef',\n        'sessionId': 'session_id'\n    };\n    const result = {\n        ...data\n    };\n    // Apply field mappings\n    Object.entries(data).forEach((param)=>{\n        let [key, value] = param;\n        if (fieldMapping[key] && !result[fieldMapping[key]]) {\n            result[fieldMapping[key]] = value;\n        }\n    });\n    // Ensure critical fields are present\n    if (data.lesson_ref && !result.lessonRef) {\n        result.lessonRef = data.lesson_ref;\n    }\n    return result;\n}\n/**\r\n * Prepares a standardized request body for backend APIs, ensuring all required parameters are included.\r\n */ function prepareBackendRequest(request, body) {\n    // Extract context parameters\n    const context = extractContextFromRequest(request);\n    // Map frontend fields to backend fields\n    const mappedData = mapFrontendToBackendFields(body);\n    // Combine data with context parameters\n    return {\n        ...mappedData,\n        lessonRef: mappedData.lessonRef || body.lesson_ref,\n        student_id: context.studentId || mappedData.studentId || body.studentId,\n        country: context.country || mappedData.country || body.country,\n        curriculum: context.curriculum || mappedData.curriculum || body.curriculum,\n        grade: context.grade || mappedData.grade || body.grade,\n        level: context.level || mappedData.level || body.level,\n        subject: context.subject || mappedData.subject || body.subject\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});