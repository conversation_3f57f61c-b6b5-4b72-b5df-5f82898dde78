"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student-dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/components/dashboard/SummaryTab.tsx":
/*!*****************************************************!*\
  !*** ./src/app/components/dashboard/SummaryTab.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/list-checks.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_LoadingState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoadingState */ \"(app-pages-browser)/./src/components/LoadingState.tsx\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/LessonProgressService */ \"(app-pages-browser)/./src/services/LessonProgressService.ts\");\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/authService */ \"(app-pages-browser)/./src/lib/authService.ts\");\n/* harmony import */ var _lib_time_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/time-utils */ \"(app-pages-browser)/./src/lib/time-utils.ts\");\n// src/app/components/dashboard/SummaryTab.tsx\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n // Combined imports\n\n\n // For assignment priority\n\n\n\n\n // Updated to match project structure\n // Import getAuthHeaders\n // Import standardized time utilities including getCurrentAcademicWeek\n// --- Inline Progress Component ---\nconst getProgressColor = (value)=>{\n    if (value >= 80) return 'bg-green-500';\n    if (value >= 60) return 'bg-blue-500';\n    if (value >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n};\nconst Progress = (param)=>{\n    let { value = 0, className = \"\" } = param;\n    const bgColor = getProgressColor(value);\n    const widthPercentage = Math.max(0, Math.min(100, value)); // Ensure value is between 0 and 100\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-2 w-full bg-gray-200 rounded-full overflow-hidden \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full rounded-full \".concat(bgColor, \" transition-all duration-300 ease-in-out\"),\n            style: {\n                width: \"\".concat(widthPercentage, \"%\")\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Progress;\n// --- Main Component ---\n// Floating action button for parents\nconst ParentFAB = (param)=>{\n    let { isParent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    if (!isParent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"outline\",\n            className: \"rounded-full p-4 shadow-lg bg-white hover:bg-gray-50\",\n            onClick: ()=>router.push('/parent-dashboard'),\n            children: \"Back to Parent Dashboard\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ParentFAB, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c1 = ParentFAB;\nfunction SummaryTab(param) {\n    let { studentId, studentData, gradeLevel, isParentView = false, onTabChange = ()=>{}, onStartLesson = ()=>{} } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_7__.getAuth)(); // Get auth instance\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__.useToast)(); // Use the toast hook properly\n    // --- State ---\n    const [todaysSchedule, setTodaysSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSchedule, setLoadingSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleError, setScheduleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lessonProgress, setLessonProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loadingProgress, setLoadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progressError, setProgressError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // --- Fetch Today's Schedule ---\n    const fetchTodaysSchedule = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SummaryTab.useCallback[fetchTodaysSchedule]\": async ()=>{\n            if (!studentId) return;\n            console.log(\"[SummaryTab] Fetching schedule for \".concat(studentId));\n            setLoadingSchedule(true);\n            setScheduleError(null);\n            setTodaysSchedule(null); // Clear previous schedule\n            try {\n                var _result_data;\n                // Get current academic week instead of using date\n                const currentAcademicWeek = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentAcademicWeek)();\n                const today = new Date();\n                const currentDayName = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentDayName)(); // Get current day name (e.g., \"monday\")\n                console.log(\"[SummaryTab] Fetching schedule for week: \".concat(currentAcademicWeek, \", day: \").concat(currentDayName));\n                // Use week-based API instead of date-based\n                const response = await fetch(\"/api/timetable?studentId=\".concat(studentId, \"&week=\").concat(currentAcademicWeek), {\n                    headers: (0,_lib_authService__WEBPACK_IMPORTED_MODULE_10__.getAuthHeaders)()\n                });\n                const result = await response.json();\n                if (response.ok && result.success && Array.isArray((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.schedule)) {\n                    console.log(\"[SummaryTab] Received \".concat(result.data.schedule.length, \" schedule items from API.\"));\n                    // Filter for today's lessons only\n                    const todaysLessons = result.data.schedule.filter({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule].todaysLessons\": (item)=>{\n                            var _item_day;\n                            // Check if the lesson is for today\n                            const lessonDay = (_item_day = item.day) === null || _item_day === void 0 ? void 0 : _item_day.toLowerCase();\n                            return lessonDay === currentDayName.toLowerCase();\n                        }\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule].todaysLessons\"]);\n                    console.log(\"[SummaryTab] Filtered \".concat(todaysLessons.length, \" lessons for today (\").concat(currentDayName, \").\"));\n                    // Process items *without* relying on API status for sorting/initial display state\n                    const processedSchedule = todaysLessons.map({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule].processedSchedule\": (item)=>({\n                                ...item,\n                                // Use status from API primarily for initial visual cue, but rely on time/progress for buttons\n                                statusFromApi: item.status || 'upcoming',\n                                time: item.time || 'Time not set',\n                                subject: item.subject || 'Untitled Lesson',\n                                lessonRef: item.lessonRef || null,\n                                day: item.day,\n                                // *** Ensure these fields are present from API ***\n                                grade: item.grade || (studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel) || '',\n                                level: item.level || '',\n                                curriculum: item.curriculum || '',\n                                country: item.country || '',\n                                academicWeek: item.academicWeek || currentAcademicWeek\n                            })\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule].processedSchedule\"]);\n                    // *** MODIFIED SORTING: Strictly Chronological ***\n                    processedSchedule.sort({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule]\": (a, b)=>{\n                            try {\n                                // Helper to parse start time string (e.g., \"08:00\") into minutes since midnight\n                                const parseStartTimeToMinutes = {\n                                    \"SummaryTab.useCallback[fetchTodaysSchedule].parseStartTimeToMinutes\": (timeStr)=>{\n                                        if (!timeStr || !timeStr.includes(' - ')) return Infinity; // Handle invalid format\n                                        const startTimePart = timeStr.split(' - ')[0].trim();\n                                        const [hours, minutes] = startTimePart.split(':').map(Number);\n                                        if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n                                            console.warn(\"[Sort] Invalid time format detected: \".concat(startTimePart));\n                                            return Infinity; // Put invalid times last\n                                        }\n                                        return hours * 60 + minutes;\n                                    }\n                                }[\"SummaryTab.useCallback[fetchTodaysSchedule].parseStartTimeToMinutes\"];\n                                const aStartMinutes = parseStartTimeToMinutes(a.time);\n                                const bStartMinutes = parseStartTimeToMinutes(b.time);\n                                return aStartMinutes - bStartMinutes;\n                            } catch (e) {\n                                console.error(\"[Sort] Error parsing time for sorting:\", a.time, b.time, e);\n                                return 0; // Avoid crashing sort on error\n                            }\n                        }\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule]\"]);\n                    console.log(\"[SummaryTab] Sorted schedule chronologically.\");\n                    setTodaysSchedule(processedSchedule);\n                    // *** MODIFIED: Call fetchLessonProgress WITHOUT lessonRefs ***\n                    // Fetch progress data after schedule is set\n                    if (processedSchedule.length > 0) {\n                        // Check if *any* item has a lessonRef before fetching progress\n                        const hasRefs = processedSchedule.some({\n                            \"SummaryTab.useCallback[fetchTodaysSchedule].hasRefs\": (item)=>item.lessonRef\n                        }[\"SummaryTab.useCallback[fetchTodaysSchedule].hasRefs\"]);\n                        if (hasRefs) {\n                            fetchLessonProgress(); // Call without arguments\n                        } else {\n                            setLessonProgress({}); // No lessons with refs, clear progress\n                            setLoadingProgress(false);\n                            console.log(\"[SummaryTab] No lessonRefs found in schedule, skipping progress fetch.\");\n                        }\n                    } else {\n                        setLoadingProgress(false); // No schedule items, no progress to load\n                    }\n                } else {\n                    throw new Error(result.error || result.message || \"Could not load today's schedule from the weekly timetable.\");\n                }\n            } catch (error) {\n                console.error('[SummaryTab] Error fetching schedule:', error);\n                setScheduleError(error.message || \"An unexpected error occurred\");\n                setTodaysSchedule([]); // Set empty array on error\n            } finally{\n                setLoadingSchedule(false);\n            }\n        }\n    }[\"SummaryTab.useCallback[fetchTodaysSchedule]\"], [\n        studentId,\n        studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel\n    ]); // Add studentData dependency if using fallback\n    // --- Fetch Lesson Progress (Modified to fetch ALL) ---\n    // *** REMOVED lessonRefs parameter ***\n    const fetchLessonProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SummaryTab.useCallback[fetchLessonProgress]\": async ()=>{\n            if (!studentId) {\n                setLessonProgress({});\n                setLoadingProgress(false);\n                return;\n            }\n            console.log(\"[SummaryTab] Fetching ALL lesson progress for student \".concat(studentId, \"...\"));\n            setLoadingProgress(true);\n            setProgressError(null);\n            try {\n                // *** Use getAllLessonProgress instead ***\n                const progressMap = await _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__.LessonProgressService.getAllLessonProgress(studentId);\n                console.log(\"[SummaryTab] Fetched all progress data (\".concat(Object.keys(progressMap).length, \" entries):\"), progressMap);\n                setLessonProgress(progressMap);\n            } catch (error) {\n                console.error('[SummaryTab] Error fetching all lesson progress:', error);\n                // Check if the error is because the function doesn't exist\n                if (error instanceof TypeError && error.message.includes(\"is not a function\")) {\n                    setProgressError(\"Service Error: getAllLessonProgress method not found.\");\n                    console.error(\"LessonProgressService might be missing the getAllLessonProgress static method.\");\n                } else {\n                    setProgressError(error.message || 'Could not load lesson progress.');\n                }\n                setLessonProgress({}); // Clear progress on error\n            } finally{\n                setLoadingProgress(false);\n            }\n        // Keep only studentId as dependency, as we fetch all progress based on it\n        }\n    }[\"SummaryTab.useCallback[fetchLessonProgress]\"], [\n        studentId\n    ]);\n    // Fetch schedule on initial load or when studentId changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SummaryTab.useEffect\": ()=>{\n            if (studentId) {\n                fetchTodaysSchedule(); // This will trigger fetchLessonProgress if needed\n            } else {\n                // Clear state if studentId becomes null/undefined\n                setTodaysSchedule(null);\n                setLessonProgress({});\n                setLoadingSchedule(false);\n                setLoadingProgress(false);\n            }\n        }\n    }[\"SummaryTab.useEffect\"], [\n        studentId,\n        fetchTodaysSchedule\n    ]);\n    // --- Derived Data ---\n    const upcomingAssignments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[upcomingAssignments]\": ()=>(studentData === null || studentData === void 0 ? void 0 : studentData.upcomingAssignments) || []\n    }[\"SummaryTab.useMemo[upcomingAssignments]\"], [\n        studentData\n    ]);\n    const recentActivities = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[recentActivities]\": ()=>(studentData === null || studentData === void 0 ? void 0 : studentData.recentActivities) || []\n    }[\"SummaryTab.useMemo[recentActivities]\"], [\n        studentData\n    ]);\n    const subjectProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[subjectProgress]\": ()=>{\n            var _studentData_completionStatus;\n            return (studentData === null || studentData === void 0 ? void 0 : (_studentData_completionStatus = studentData.completionStatus) === null || _studentData_completionStatus === void 0 ? void 0 : _studentData_completionStatus.subjects) || {};\n        }\n    }[\"SummaryTab.useMemo[subjectProgress]\"], [\n        studentData\n    ]);\n    const displayGradeLevel = gradeLevel || (studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel) || 'N/A';\n    const overallProgressValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[overallProgressValue]\": ()=>{\n            var _studentData_overallProgress;\n            return (_studentData_overallProgress = studentData === null || studentData === void 0 ? void 0 : studentData.overallProgress) !== null && _studentData_overallProgress !== void 0 ? _studentData_overallProgress : 0;\n        }\n    }[\"SummaryTab.useMemo[overallProgressValue]\"], [\n        studentData\n    ]);\n    // Keep only this declaration of completedTodayCount\n    const completedTodayCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[completedTodayCount]\": ()=>{\n            var _todaysSchedule_filter_length;\n            return (_todaysSchedule_filter_length = todaysSchedule === null || todaysSchedule === void 0 ? void 0 : todaysSchedule.filter({\n                \"SummaryTab.useMemo[completedTodayCount]\": (item)=>item.statusFromApi === 'completed'\n            }[\"SummaryTab.useMemo[completedTodayCount]\"]).length) !== null && _todaysSchedule_filter_length !== void 0 ? _todaysSchedule_filter_length : 0;\n        }\n    }[\"SummaryTab.useMemo[completedTodayCount]\"], [\n        todaysSchedule\n    ]);\n    // Recalculate nextLesson based on the *chronologically sorted* list\n    const nextLesson = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[nextLesson]\": ()=>{\n            if (!todaysSchedule) return null;\n            const now = new Date();\n            const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();\n            // Helper to parse start time string (e.g., \"08:00\") into minutes\n            const parseStartTime = {\n                \"SummaryTab.useMemo[nextLesson].parseStartTime\": (timeStr)=>{\n                    try {\n                        if (!timeStr || !timeStr.includes(' - ')) return Infinity;\n                        const startTimePart = timeStr.split(' - ')[0].trim();\n                        const [hours, minutes] = startTimePart.split(':').map(Number);\n                        if (isNaN(hours) || isNaN(minutes)) return Infinity;\n                        return hours * 60 + minutes;\n                    } catch (e) {\n                        return Infinity;\n                    }\n                }\n            }[\"SummaryTab.useMemo[nextLesson].parseStartTime\"];\n            // Helper to parse end time string (e.g., \"08:45\") into minutes\n            const parseEndTime = {\n                \"SummaryTab.useMemo[nextLesson].parseEndTime\": (timeStr)=>{\n                    try {\n                        if (!timeStr || !timeStr.includes(' - ')) return -1;\n                        const endTimePart = timeStr.split(' - ')[1].trim();\n                        const [hours, minutes] = endTimePart.split(':').map(Number);\n                        if (isNaN(hours) || isNaN(minutes)) return -1;\n                        return hours * 60 + minutes;\n                    } catch (e) {\n                        return -1;\n                    }\n                }\n            }[\"SummaryTab.useMemo[nextLesson].parseEndTime\"];\n            // Priority 1: Find the first lesson that is currently happening based on time\n            const currentLesson = todaysSchedule.find({\n                \"SummaryTab.useMemo[nextLesson].currentLesson\": (item)=>{\n                    const startMinutes = parseStartTime(item.time);\n                    const endMinutes = parseEndTime(item.time);\n                    return currentTimeMinutes >= startMinutes && currentTimeMinutes < endMinutes;\n                }\n            }[\"SummaryTab.useMemo[nextLesson].currentLesson\"]);\n            if (currentLesson) {\n                console.log(\"[SummaryTab] Next Lesson (P1 - Current by Time):\", currentLesson.subject);\n                return {\n                    ...currentLesson,\n                    derivedStatus: 'current'\n                }; // Add derived status\n            }\n            // Priority 2: Find the first lesson starting later today\n            const upcomingLesson = todaysSchedule.find({\n                \"SummaryTab.useMemo[nextLesson].upcomingLesson\": (item)=>{\n                    const startMinutes = parseStartTime(item.time);\n                    return startMinutes > currentTimeMinutes;\n                }\n            }[\"SummaryTab.useMemo[nextLesson].upcomingLesson\"]);\n            if (upcomingLesson) {\n                console.log(\"[SummaryTab] Next Lesson (P2 - Upcoming by Time):\", upcomingLesson.subject);\n                return {\n                    ...upcomingLesson,\n                    derivedStatus: 'upcoming'\n                }; // Add derived status\n            }\n            // If no current or upcoming found (all might be completed by time)\n            console.log(\"[SummaryTab] No current or upcoming lesson found based on time.\");\n            return null;\n        }\n    }[\"SummaryTab.useMemo[nextLesson]\"], [\n        todaysSchedule\n    ]);\n    // Function to determine button state using standardized time utilities\n    const getButtonState = (item)=>{\n        const lessonRef = item.lessonRef;\n        const progressData = lessonRef ? lessonProgress[lessonRef] : null;\n        const progressStatus = (progressData === null || progressData === void 0 ? void 0 : progressData.status) || 'not_started';\n        // Fix isStudent detection: For students, buttons should be enabled when not in parent view\n        // The auth comparison was causing issues since studentId (username) != auth.currentUser.uid (Firebase UID)\n        // Instead, rely on isParentView prop which correctly indicates if this is a parent viewing\n        const isStudent = !isParentView;\n        // Use API status as primary source of truth for actual completion\n        // Only use time-based status for current/upcoming determination\n        const apiStatus = item.statusFromApi || 'upcoming';\n        // Determine the effective lesson status\n        let effectiveStatus = apiStatus;\n        // If API says upcoming, check if it should be current based on time\n        if (apiStatus === 'upcoming') {\n            const timeStatus = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.calculateLessonStatus)(item.time, item.day || (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentDayName)(), new Date(), progressStatus);\n            // Only override to 'current' if time-based calculation says it's current\n            // Never override to 'completed' based on time alone\n            if (timeStatus.status === 'current') {\n                effectiveStatus = 'current';\n            }\n        }\n        console.log('[SummaryTab] Lesson status for \"'.concat(item.subject, '\": ').concat(effectiveStatus), {\n            apiStatus,\n            timeRange: item.time,\n            day: item.day,\n            progressStatus,\n            lessonRef\n        });\n        // --- Define Action with ALL parameters ---\n        let action = ()=>{\n            if (lessonRef) {\n                // Collect all available data from the item\n                const params = {\n                    lessonRef: lessonRef,\n                    studentId: studentId || '',\n                    subject: item.subject || '',\n                    grade: item.grade || '',\n                    level: item.level || '',\n                    curriculum: item.curriculum || '',\n                    country: item.country || ''\n                };\n                // Check if we have the minimum required data\n                const missingCriticalParams = [\n                    'lessonRef',\n                    'studentId'\n                ].filter((key)=>!params[key]);\n                if (missingCriticalParams.length > 0) {\n                    console.error(\"Cannot navigate to lesson: Missing critical parameters: \".concat(missingCriticalParams.join(', ')));\n                    toast({\n                        title: \"Error\",\n                        description: \"Cannot start lesson: Missing critical information\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Log and navigate\n                console.log('[SummaryTab] Navigating to lesson with params:', params);\n                const queryParams = new URLSearchParams(params).toString();\n                router.push(\"/classroom?\".concat(queryParams));\n            }\n        };\n        let buttonText = 'View';\n        let buttonVariant = 'secondary';\n        let buttonClass = 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n        let buttonDisabled = !lessonRef; // Generally disabled if no lessonRef\n        let IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        // --- Standardized Logic Flow ---\n        if (effectiveStatus === 'completed') {\n            // For students, update action to go to /start-lesson for review as well\n            if (isStudent) {\n                action = ()=>{\n                    if (lessonRef && studentId) {\n                        const params = {\n                            lessonRef: lessonRef,\n                            studentId: studentId || '',\n                            subject: item.subject || '',\n                            grade: item.grade || '',\n                            level: item.level || '',\n                            curriculum: item.curriculum || '',\n                            country: item.country || ''\n                        };\n                        const missingCriticalParams = [\n                            'lessonRef',\n                            'studentId'\n                        ].filter((key)=>!params[key]);\n                        if (missingCriticalParams.length > 0) {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot review lesson: Missing critical information\",\n                                variant: \"destructive\"\n                            });\n                            return;\n                        }\n                        const queryParams = new URLSearchParams(params).toString();\n                        router.push(\"/start-lesson?\".concat(queryParams));\n                    } else {\n                        toast({\n                            title: \"Error\",\n                            description: \"Cannot review lesson: Missing lessonRef or studentId\",\n                            variant: \"destructive\"\n                        });\n                    }\n                };\n            }\n            buttonText = isStudent ? 'Review' : 'View';\n            buttonVariant = 'outline';\n            buttonClass = isStudent ? 'text-green-700 bg-green-50 hover:bg-green-100 border-green-300' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n            buttonDisabled = !lessonRef;\n            IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        } else if (effectiveStatus === 'current') {\n            // Lesson is happening now\n            if (progressStatus === 'in_progress') {\n                buttonText = isStudent ? 'Resume' : 'View';\n                buttonVariant = 'outline';\n                buttonClass = isStudent ? 'text-yellow-800 bg-yellow-50 hover:bg-yellow-100 border-yellow-300' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n                buttonDisabled = !isStudent || !lessonRef;\n                IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n            } else {\n                buttonText = isStudent ? 'Join' : 'View';\n                buttonVariant = isStudent ? 'default' : 'outline';\n                buttonClass = isStudent ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n                buttonDisabled = !isStudent || !lessonRef;\n                IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                // Update action to go to /start-lesson for students\n                if (isStudent) {\n                    action = ()=>{\n                        if (lessonRef && studentId) {\n                            const params = {\n                                lessonRef: lessonRef,\n                                studentId: studentId || '',\n                                subject: item.subject || '',\n                                grade: item.grade || '',\n                                level: item.level || '',\n                                curriculum: item.curriculum || '',\n                                country: item.country || ''\n                            };\n                            const missingCriticalParams = [\n                                'lessonRef',\n                                'studentId'\n                            ].filter((key)=>!params[key]);\n                            if (missingCriticalParams.length > 0) {\n                                toast({\n                                    title: \"Error\",\n                                    description: \"Cannot start lesson: Missing critical information\",\n                                    variant: \"destructive\"\n                                });\n                                return;\n                            }\n                            const queryParams = new URLSearchParams(params).toString();\n                            router.push(\"/start-lesson?\".concat(queryParams));\n                        } else {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot start lesson: Missing lessonRef or studentId\",\n                                variant: \"destructive\"\n                            });\n                        }\n                    };\n                }\n            }\n        } else {\n            // Future lesson (upcoming)\n            // Enable buttons for upcoming lessons so students can start them\n            if (isStudent && lessonRef) {\n                buttonText = 'Start';\n                buttonVariant = 'default';\n                buttonClass = 'bg-blue-600 hover:bg-blue-700 text-white';\n                buttonDisabled = false;\n                IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                action = ()=>{\n                    if (lessonRef && studentId) {\n                        const params = {\n                            lessonRef: lessonRef,\n                            studentId: studentId || '',\n                            subject: item.subject || '',\n                            grade: item.grade || '',\n                            level: item.level || '',\n                            curriculum: item.curriculum || '',\n                            country: item.country || ''\n                        };\n                        const missingCriticalParams = [\n                            'lessonRef',\n                            'studentId'\n                        ].filter((key)=>!params[key]);\n                        if (missingCriticalParams.length > 0) {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot start lesson: Missing critical information\",\n                                variant: \"destructive\"\n                            });\n                            return;\n                        }\n                        const queryParams = new URLSearchParams(params).toString();\n                        router.push(\"/start-lesson?\".concat(queryParams));\n                    } else {\n                        toast({\n                            title: \"Error\",\n                            description: \"Cannot start lesson: Missing lessonRef or studentId\",\n                            variant: \"destructive\"\n                        });\n                    }\n                };\n            } else {\n                // For parents or lessons without lessonRef\n                buttonText = 'Upcoming';\n                buttonVariant = 'secondary';\n                buttonClass = 'text-gray-500 bg-gray-200 cursor-not-allowed';\n                buttonDisabled = true;\n                action = ()=>{};\n                IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n            }\n        }\n        console.log('[SummaryTab] Final button state for \"'.concat(item.subject, '\":\\n      - Effective Status: ').concat(effectiveStatus, \"\\n      - API Status: \").concat(apiStatus, \"\\n      - Progress status: \").concat(progressStatus, \"\\n      - Button text: \").concat(buttonText, \"\\n      - Button disabled: \").concat(buttonDisabled, \"\\n      - Is Student: \").concat(isStudent, \"\\n      - Has lessonRef: \").concat(!!lessonRef));\n        return {\n            buttonText,\n            buttonVariant,\n            buttonClass,\n            buttonDisabled,\n            action,\n            IconComponent\n        };\n    };\n    // --- Render Logic ---\n    if (!studentData && !isParentView) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        message: \"Loading student data...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 553,\n        columnNumber: 45\n    }, this);\n    if (loadingSchedule && !todaysSchedule) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        message: \"Loading schedule...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 554,\n        columnNumber: 50\n    }, this); // Show loading if schedule is null\n    // Helper function for priority badge class\n    const getPriorityBadgeClass = (priority)=>{\n        switch(priority.toLowerCase()){\n            case 'high':\n                return 'bg-red-100 text-red-800';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'low':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // Helper function for activity icon\n    const getActivityIcon = (type)=>{\n        switch(type.toLowerCase()){\n            case 'quiz':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n            case 'assignment':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case 'lesson':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        }\n    };\n    var _todaysSchedule_length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-1 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 shadow-md hover:shadow-lg transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg font-semibold text-blue-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: 20,\n                                            className: \"mr-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Next Up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 14\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex flex-col items-center justify-center text-center pt-2 pb-6 min-h-[200px]\",\n                                children: [\n                                    \" \",\n                                    loadingSchedule || loadingProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-500 my-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 21\n                                    }, this) : scheduleError || progressError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-600 text-sm px-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 25\n                                            }, this),\n                                            scheduleError || progressError || \"Error loading data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 21\n                                    }, this) : nextLesson ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-800 mb-1 truncate w-full px-4\",\n                                                title: nextLesson.subject,\n                                                children: nextLesson.subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base text-gray-600 mb-4\",\n                                                children: nextLesson.derivedStatus === 'current' ? \"In Progress (\".concat(nextLesson.time.split(' - ')[1], \")\") : \"Starts at \".concat(nextLesson.time.split(' - ')[0])\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 25\n                                            }, this),\n                                            (()=>{\n                                                var _auth_currentUser;\n                                                const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(nextLesson);\n                                                const isStudent = !isParentView && ((_auth_currentUser = auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid) === studentId;\n                                                // Adjust button text/action specifically for the \"Next Up\" card\n                                                let nextUpText = buttonText;\n                                                let nextUpAction = action;\n                                                let nextUpDisabled = buttonDisabled;\n                                                let nextUpClass = buttonClass;\n                                                let nextUpVariant = buttonVariant;\n                                                if (nextLesson.derivedStatus === 'current' && isStudent && !buttonDisabled) {\n                                                    nextUpText = 'Join Lesson Now';\n                                                    nextUpClass = 'bg-blue-600 hover:bg-blue-700 text-white'; // Ensure correct class for Join\n                                                    nextUpVariant = 'default';\n                                                } else if (nextLesson.derivedStatus === 'upcoming') {\n                                                    nextUpText = 'View on Timetable';\n                                                    nextUpAction = ()=>onTabChange('timetable');\n                                                    nextUpDisabled = false; // Allow viewing timetable\n                                                    nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';\n                                                    nextUpVariant = 'default'; // Use default style for primary action\n                                                } else if (buttonText === 'Review' && isStudent) {\n                                                    // If it's reviewable but also the \"next\" item (unlikely unless only one lesson)\n                                                    nextUpText = 'Review Lesson';\n                                                } else if (!isStudent) {\n                                                    // Parent view adjustments for Next Up\n                                                    nextUpText = 'View Details';\n                                                    nextUpAction = ()=>onTabChange('timetable');\n                                                    nextUpDisabled = false;\n                                                    nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';\n                                                    nextUpVariant = 'default';\n                                                }\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: nextUpVariant,\n                                                    className: \"w-full max-w-xs \".concat(nextUpClass, \" font-semibold transition-colors shadow-md hover:shadow-lg\"),\n                                                    onClick: nextUpAction,\n                                                    disabled: nextUpDisabled,\n                                                    children: [\n                                                        nextUpText,\n                                                        !nextUpDisabled && nextUpAction !== onTabChange ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 88\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 33\n                                                }, this);\n                                            })()\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-10 w-10 text-green-500 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-700\",\n                                                children: \"All Classes Done!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: \"Great job today!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                className: \"mt-4\",\n                                                onClick: ()=>onTabChange('timetable'),\n                                                children: \" View Full Timetable \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 26\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 10\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Overall Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 18,\n                                                    className: \"text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-800\",\n                                            children: [\n                                                overallProgressValue,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                            value: overallProgressValue,\n                                            className: \"h-1.5 mt-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Today's Classes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 18,\n                                                    className: \"text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        loadingSchedule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6 animate-spin text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 18\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-800\",\n                                                    children: (_todaysSchedule_length = todaysSchedule === null || todaysSchedule === void 0 ? void 0 : todaysSchedule.length) !== null && _todaysSchedule_length !== void 0 ? _todaysSchedule_length : 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: [\n                                                        completedTodayCount,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5 flex flex-col justify-between h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"AI Form Tutor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"text-amber-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 mb-3\",\n                                                    children: \"Need help or guidance?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full border-amber-400 text-amber-700 hover:bg-amber-50\",\n                                            onClick: ()=>router.push('/ai-form-tutor'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Ask Tutor\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-lg font-semibold text-gray-800\",\n                                        children: \"Today's Schedule\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-2 pb-4 pr-2\",\n                                    children: loadingSchedule && !todaysSchedule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex items-center justify-center text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-6 w-6 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" Loading Schedule...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 18\n                                    }, this) : scheduleError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex flex-col items-center justify-center text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-8 w-8 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 95\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: scheduleError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 136\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 18\n                                    }, this) : todaysSchedule && todaysSchedule.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative pl-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 19\n                                            }, this),\n                                            todaysSchedule.map((item, index)=>{\n                                                var _lessonProgress_lessonRef;\n                                                const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(item);\n                                                const lessonRef = item.lessonRef;\n                                                const progressStatus = lessonRef ? (_lessonProgress_lessonRef = lessonProgress[lessonRef]) === null || _lessonProgress_lessonRef === void 0 ? void 0 : _lessonProgress_lessonRef.status : 'not_started';\n                                                const timeStatusFromApi = item.statusFromApi; // Use the status calculated by API for dot color\n                                                // Determine dot color based on API status primarily\n                                                let dotClass = 'bg-gray-300'; // Default: upcoming\n                                                if (timeStatusFromApi === 'completed' || progressStatus === 'completed') {\n                                                    dotClass = 'bg-green-500';\n                                                } else if (timeStatusFromApi === 'current') {\n                                                    dotClass = 'bg-blue-500 ring-4 ring-blue-200 animate-pulse';\n                                                } else if (progressStatus === 'in_progress') {\n                                                    dotClass = 'bg-yellow-500'; // In progress but maybe not current time\n                                                }\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative pl-8 py-4 group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-[18px] top-7 -ml-[7px] w-3.5 h-3.5 rounded-full border-2 border-white group-hover:scale-110 transition-transform \".concat(dotClass)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between gap-4 ml-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-grow min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wider mb-0.5\",\n                                                                            children: item.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-gray-800 truncate\",\n                                                                            title: item.subject,\n                                                                            children: item.subject\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5 flex items-center\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    size: 12,\n                                                                                    className: \"mr-1 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 92\n                                                                                }, this),\n                                                                                \" \",\n                                                                                item.instructor || \"AI Instructor\",\n                                                                                \" \"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        loadingProgress && lessonRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 italic ml-2\",\n                                                                            children: \" (loading status...)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 64\n                                                                        }, this),\n                                                                        progressError && lessonRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-red-500 ml-2\",\n                                                                            children: \" (status error)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 60\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: loadingProgress && lessonRef ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 animate-spin text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: buttonVariant,\n                                                                        className: \"px-3 py-1.5 rounded-md text-xs font-medium transition-all shadow-sm hover:shadow-md \".concat(buttonClass),\n                                                                        onClick: action,\n                                                                        disabled: buttonDisabled || loadingProgress,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                size: 14,\n                                                                                className: \"mr-1.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            buttonText\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, item.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this); // End of return for map callback\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex flex-col items-center justify-center text-center text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-10 w-10 text-gray-400 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No classes scheduled for today.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Subject Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            Object.keys(subjectProgress).length > 0 ? Object.entries(subjectProgress).map((param)=>{\n                                                let [subject, progress] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-baseline mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-800\",\n                                                                    children: subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-semibold text-gray-600\",\n                                                                    children: [\n                                                                        progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                            value: progress,\n                                                            className: \"h-1.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, subject, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center text-gray-500 py-4\",\n                                                children: \"No progress data available.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 18\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                size: \"sm\",\n                                                className: \"p-0 h-auto text-blue-600 text-xs\",\n                                                onClick: ()=>onTabChange('reports'),\n                                                children: \"View Detailed Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 18\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Upcoming Assignments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            upcomingAssignments.length > 0 ? upcomingAssignments.slice(0, 3).map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between gap-2 p-2 rounded bg-gray-50/50 border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-purple-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-grow min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-800 truncate\",\n                                                                    title: assignment.title,\n                                                                    children: [\n                                                                        assignment.title,\n                                                                        \" (\",\n                                                                        assignment.subject,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        assignment.dueDate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs \".concat(getPriorityBadgeClass(assignment.priority)),\n                                                            children: assignment.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, assignment.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center text-gray-500 py-4\",\n                                                children: \"No upcoming assignments.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 18\n                                            }, this),\n                                            upcomingAssignments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                size: \"sm\",\n                                                className: \"p-0 h-auto text-blue-600 text-xs\",\n                                                onClick: ()=>onTabChange('homework'),\n                                                children: \"View All Assignments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 841,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 732,\n                columnNumber: 7\n            }, this),\n            isParentView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParentFAB, {\n                isParent: isParentView\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 870,\n                columnNumber: 24\n            }, this),\n            \" \"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_s1(SummaryTab, \"qSsrS3Whspl5xtqgLkkzbJGhNGc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c2 = SummaryTab;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Progress\");\n$RefreshReg$(_c1, \"ParentFAB\");\n$RefreshReg$(_c2, \"SummaryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/dashboard/SummaryTab.tsx\n"));

/***/ })

});