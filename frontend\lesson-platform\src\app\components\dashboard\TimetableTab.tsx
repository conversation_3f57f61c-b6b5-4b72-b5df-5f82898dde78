// src/components/student/TimetableTab.tsx

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/providers/AuthProvider';
import {
  Card, CardContent, CardHeader, CardTitle, CardDescription,
  Button,
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle,
  Alert, AlertDescription,
  Badge
} from '@/components/ui';
import { Progress } from "@/components/ui/progress";
import {
  Download,
  RefreshCw,
  Clock,
  ChevronRight,
  List,
  Grid,
  ChevronDown,
  ChevronUp,
  Filter,
  Cpu,
  ArrowRight,
  AlertCircle,
  CalendarDays,
  View,
  Loader2
} from 'lucide-react';
import { format, getDay, isToday } from 'date-fns';
import LoadingState from '@/components/LoadingState';
import ErrorDisplay from '@/components/ui/ErrorDisplay';
import { normalizeSubjectName } from '@/lib/subject-utils-client';
import { LessonProgressService } from '@/services/LessonProgressService';
import { useToast } from '@/app/providers/ClientToastWrapper';
import { fetchOrCreateSessionId } from '@/app/utils/sessionUtils';

// Interfaces
interface TimetableEntry {
    subject_id: string;
    subject: string;
    startTime: string;
    endTime: string;
    teacher?: string;
    cognitive_load?: number;
    cognitiveLoad?: number; // Support both naming conventions
    country?: string;
    curriculum?: string;
    gradeLevel?: string;
    level?: string;
    lessonRef?: string;
    lessonReference?: string;
    academicWeek?: number;
    absoluteLessonNumber?: number;
    description?: string;
    status?: string;
}

interface DailyScheduleMap {
    [timeSlotOrBreakKey: string]: TimetableEntry | string;
}

interface WeeklyTimetableMap {
    [day: string]: DailyScheduleMap;
}

interface TimetableTabProps {
  studentId: string;
  isParentView?: boolean;
}

const DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const MAX_LESSONS_PER_WEEK = 35;
// --- Main Component ---
export default function TimetableTab({ studentId: studentIdProp, isParentView = false }: TimetableTabProps) {
  // New state management for week-based timetable
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [weeklySchedule, setWeeklySchedule] = useState<any[]>([]);
  const [schedule, setSchedule] = useState<Record<string, Record<string, TimetableEntry>>>({});
  const [timeSlots, setTimeSlots] = useState<string[]>([]);
  
  // Function to fetch week-specific timetable
  const fetchWeeklyTimetable = useCallback(async (week: number, forceRefresh: boolean = false) => {
    if (!studentIdProp) return;
    
    setIsLoading(!forceRefresh);
    setIsRefreshing(forceRefresh);
    setError(null);
    
    try {
      const today = new Date().toISOString().split('T')[0];
      const response = await fetch(`/api/timetable?studentId=${studentIdProp}&week=${week}&date=${today}`, {
        headers: {
          'Cache-Control': forceRefresh ? 'no-cache' : 'default',
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch timetable: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success && result.data?.schedule) {
        setWeeklySchedule(result.data.schedule);
        
        console.log(`[TimetableTab] Raw schedule data:`, result.data.schedule);
        
        // Convert the weekly schedule to the expected format
        const newSchedule: Record<string, Record<string, TimetableEntry>> = {};
        const newTimeSlots = new Set<string>();
        
        result.data.schedule.forEach((lesson: any, index: number) => {
          const day = lesson.day;
          const timeSlot = lesson.time;
          
          console.log(`[TimetableTab] Processing lesson ${index + 1}:`, {
            subject: lesson.subject,
            day: day,
            time: timeSlot,
            lessonRef: lesson.lessonReference
          });
          
          if (!newSchedule[day]) {
            newSchedule[day] = {};
            console.log(`[TimetableTab] Created new day schedule for: ${day}`);
          }
          
          newSchedule[day][timeSlot] = {
            subject_id: lesson.subjectId,
            subject: lesson.subject,
            startTime: timeSlot,
            endTime: timeSlot, // This might need to be calculated based on duration
            lessonReference: lesson.lessonReference,
            academicWeek: lesson.academicWeek,
            absoluteLessonNumber: lesson.absoluteLessonNumber,
            description: lesson.description,
            status: lesson.status
          };
          
          newTimeSlots.add(timeSlot);
        });
        
        console.log(`[TimetableTab] Final schedule object:`, newSchedule);
        console.log(`[TimetableTab] Time slots:`, Array.from(newTimeSlots));
        
        setSchedule(newSchedule);
        setTimeSlots(Array.from(newTimeSlots).sort());
        
        console.log(`[TimetableTab] Loaded ${result.data.schedule.length} lessons for week ${week}`);
      } else {
        throw new Error(result.error || 'No schedule data received');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch timetable';
      setError(errorMessage);
      console.error('[TimetableTab] Error fetching weekly timetable:', err);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setIsGenerating(false);
    }
  }, [studentIdProp]);

  // UI State - Managed within the component, not the data hook
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');
  const [displayMode, setDisplayMode] = useState<'week' | 'day'>('week');
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [academicWeek, setAcademicWeek] = useState<number>(1); // Add week selector state
  const currentDayIndex = Math.max(0, Math.min(4, getDay(new Date()) - 1));
  const [selectedDay, setSelectedDay] = useState<string>(DAYS_OF_WEEK[currentDayIndex]);
  const router = useRouter();
  
  // Add new state for progress data
  const [lessonProgress, setLessonProgress] = useState<Record<string, any>>({});

  // Add function to fetch progress data
  const fetchProgressData = useCallback(async () => {
    if (!studentIdProp) return;
    
    try {
      const progressData = await LessonProgressService.getAllLessonProgress(studentIdProp);
      setLessonProgress(progressData);
      console.log('[TimetableTab] Fetched lesson progress data:', progressData);
    } catch (error) {
      console.error('[TimetableTab] Error fetching lesson progress:', error);
    }
  }, [studentIdProp]);

  // Effect to fetch timetable when week changes
  useEffect(() => {
    if (studentIdProp && academicWeek) {
      fetchWeeklyTimetable(academicWeek);
    }
  }, [studentIdProp, academicWeek, fetchWeeklyTimetable]);

  // --- Refresh Handler ---
  const handleRefresh = useCallback(() => {
    if (academicWeek) {
      fetchWeeklyTimetable(academicWeek, true);
    }
  }, [academicWeek, fetchWeeklyTimetable]);

  const getSubjectClass = (subject: string = '') => {
    const subjectLower = subject.toLowerCase();
    if (subjectLower.includes('math')) return 'bg-blue-100 border-blue-400 text-blue-800';
    if (subjectLower.includes('english')) return 'bg-green-100 border-green-400 text-green-800';
    if (subjectLower.includes('science') || subjectLower.includes('physics') || subjectLower.includes('chemistry') || subjectLower.includes('biology') || subjectLower.includes('basic science')) return 'bg-purple-100 border-purple-400 text-purple-800';
    if (subjectLower.includes('social') || subjectLower.includes('history') || subjectLower.includes('geography') || subjectLower.includes('national values') || subjectLower.includes('civic')) return 'bg-amber-100 border-amber-400 text-amber-800';
    if (subjectLower.includes('computer') || subjectLower.includes('computing') || subjectLower.includes('ict') || subjectLower.includes('artificial intelligence')) return 'bg-cyan-100 border-cyan-400 text-cyan-800';
    if (subjectLower.includes('art') || subjectLower.includes('creative')) return 'bg-pink-100 border-pink-400 text-pink-800';
    if (subjectLower.includes('entrepreneurship') || subjectLower.includes('business')) return 'bg-indigo-100 border-indigo-400 text-indigo-800';
    if (subjectLower.includes('financial') || subjectLower.includes('literacy') || subjectLower.includes('economics')) return 'bg-yellow-100 border-yellow-400 text-yellow-800';
    if (subjectLower.includes('physical') || subjectLower.includes('health')) return 'bg-lime-100 border-lime-400 text-lime-800';
    if (subjectLower.includes('project') || subjectLower.includes('excellence')) return 'bg-teal-100 border-teal-400 text-teal-800';
    if (subjectLower.includes('language') && !subjectLower.includes('english')) return 'bg-orange-100 border-orange-400 text-orange-800';
    return 'bg-gray-100 border-gray-400 text-gray-800';
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'upcoming': return 'bg-gray-100 text-gray-800';
      case 'past': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // --- Handle Join Lesson ---
  const handleJoinLesson = async (lesson: TimetableEntry) => {
    try {
      if (!lesson.lessonRef && !lesson.lessonReference) {
        throw new Error('Missing lesson reference');
      }

      const lessonRef = lesson.lessonRef || lesson.lessonReference;
      console.log('[TimetableTab] Joining lesson with reference:', lessonRef);

      // Navigate to start-lesson page with proper parameters
      const params = {
        lessonRef: lessonRef!,
        studentId: studentIdProp,
        subject: lesson.subject || '',
        grade: lesson.grade || '',
        level: lesson.level || '',
        curriculum: lesson.curriculum || '',
        country: lesson.country || '',
      };

      // Validate critical parameters
      const missingCriticalParams = ['lessonRef', 'studentId'].filter(key => !params[key as keyof typeof params]);
      if (missingCriticalParams.length > 0) {
        throw new Error(`Cannot start lesson: Missing critical information (${missingCriticalParams.join(', ')})`);
      }

      const queryParams = new URLSearchParams(params).toString();
      console.log('[TimetableTab] Navigating to start-lesson with params:', params);
      router.push(`/start-lesson?${queryParams}`);

    } catch (error: any) {
      console.error('Failed to join lesson:', error);
      // Use toast notification if available
      if (typeof window !== 'undefined' && window.alert) {
        window.alert(`Failed to join lesson: ${error.message}`);
      }
    }
  };

  // Effect to fetch progress data when component loads
  useEffect(() => {
    fetchProgressData();
  }, [fetchProgressData]);

  // --- Weekly Summary Calculation ---
  const weeklySummary = useMemo(() => {
    if (Object.keys(schedule).length === 0) return { sortedSubjects: [], totalLessons: 0 };
    const subjectCounts: Record<string, number> = {};
    let totalLessons = 0;
    DAYS_OF_WEEK.forEach(day => {
      const daySchedule = schedule[day] || {};
      Object.values(daySchedule).forEach(entry => {
        // Check if it's a valid lesson object AND NOT a free/study period
        if (
          typeof entry === 'object' &&    // Is it an object?
          entry !== null &&               // Is it not null?
          entry.subject &&                // Does it have a subject name?
          entry.subject_id !== 'free_period' && // Explicitly exclude free periods by ID
          entry.subject !== 'Free Period'       // Also check subject name
        ) {
          // It's a valid lesson to count
          subjectCounts[entry.subject] = (subjectCounts[entry.subject] || 0) + 1;
          totalLessons++;
        }
      });
    });
    const sortedSubjects = Object.entries(subjectCounts).map(([name, count]) => ({ name, count })).sort((a, b) => b.count - a.count);
    return { sortedSubjects, totalLessons };
  }, [schedule]);

  // --- Loading and Error States Rendering ---
  // Use states returned from the hook
  if (isGenerating) return <LoadingState message="Generating timetable, please wait..." />;
  if (isRefreshing) return <LoadingState message="Refreshing timetable..." />;
  if (isLoading) return <LoadingState message="Loading timetable..." />;
  if (error) return <ErrorDisplay title="Timetable Error" message={error} onAction={handleRefresh} actionText="Retry / Regenerate Timetable" />;

  const isScheduleEffectivelyEmpty = Object.keys(schedule).length === 0 || DAYS_OF_WEEK.every(day => Object.values(schedule[day] || {}).every(entry => typeof entry !== 'object'));
  if (isScheduleEffectivelyEmpty && !isLoading && !isGenerating && !isRefreshing) {
      return <ErrorDisplay title="Timetable Unavailable" message="No timetable data could be loaded or generated." onAction={handleRefresh} actionText="Refresh Timetable" />;
  }


  // --- RENDER LOGIC ---

  // Parent View
    if (isParentView) {
        // Simplified rendering for parent view (Grid only)
        return (
            <div className="space-y-6">
                 <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                   <div>
                       <h2 className="text-2xl font-bold tracking-tight">Child's Timetable</h2>
                       <p className="text-muted-foreground">
                         Week {academicWeek} of 30 - Viewing your child's weekly class schedule
                       </p>
                       {weeklySchedule.length > 0 && (
                         <div className="text-sm text-gray-600 mt-1">
                           {weeklySchedule.length} lessons scheduled this week
                         </div>
                       )}
                   </div>
                   {/* Week Selector for Parent View */}
                   <div className="flex items-center gap-2">
                     <label htmlFor="parent-week-select" className="text-sm font-medium text-muted-foreground">
                       Week:
                     </label>
                     <select
                       id="parent-week-select"
                       value={academicWeek}
                       onChange={(e) => setAcademicWeek(parseInt(e.target.value))}
                       disabled={isLoading || isRefreshing || isGenerating}
                       className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium min-w-[80px] bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     >
                       {Array.from({ length: 30 }, (_, i) => i + 1).map(week => (
                         <option key={week} value={week}>
                           Week {week}
                         </option>
                       ))}
                     </select>
                   </div>
               </div>
                <Card>
                  <CardContent className="p-0 overflow-auto">
                    <table className="w-full border-collapse min-w-[800px]">
                       <thead>
                         <tr className="bg-muted">
                             <th className="p-3 border-r text-left font-medium text-muted-foreground sticky left-0 bg-muted z-10">Time</th>
                             {DAYS_OF_WEEK.map(day => (<th key={day} className="p-3 text-center font-medium">{day}</th>))}
                         </tr>
                       </thead>
                       <tbody>
                         {timeSlots.length > 0 ? (
                           timeSlots.map((timeSlotKey, index) => (
                             <tr key={timeSlotKey} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                               <td className="p-3 border-r text-sm text-muted-foreground font-medium whitespace-nowrap sticky left-0 bg-inherit z-10">{timeSlotKey}</td>
                               {DAYS_OF_WEEK.map(day => {
                                 const daySchedule = schedule[day] || {};
                                 const entry = daySchedule[timeSlotKey];
                                 const isLesson = typeof entry === 'object';
                                 return (
                                   <td key={`${day}-${timeSlotKey}`} className="p-2 text-center border-l h-24 align-top">
                                     {isLesson ? (
                                       <div className={`p-2 rounded border ${getSubjectClass(entry.subject)} h-full flex flex-col justify-between text-xs sm:text-sm`}>                                           <div>
                                             <div className="font-semibold">{entry.subject}</div>
                                             <div className="text-gray-600 mt-1">{entry.teacher || "AI Instructor"}</div>
                                             {/* Show lesson status, reference, and cognitive load */}
                                             {entry.lessonReference && (
                                               <div className="mt-1">
                                                 <div className="text-xs text-gray-500">{entry.lessonReference}</div>
                                                 <div className="flex flex-wrap gap-1 mt-1">
                                                   <span className={`inline-block px-1 py-0.5 rounded-sm text-xs ${getStatusBadgeClass(entry.status || 'upcoming')}`}>
                                                     {entry.status === 'completed' ? 'Done' :
                                                     entry.status === 'in_progress' ? 'Active' :
                                                     entry.status === 'past' ? 'Past' :
                                                     'Upcoming'}
                                                   </span>
                                                   {(entry.cognitiveLoad || entry.cognitive_load) && (
                                                     <span className="inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800">
                                                       CL: {entry.cognitiveLoad || entry.cognitive_load}
                                                     </span>
                                                   )}
                                                 </div>
                                               </div>
                                             )}
                                           </div>
                                       </div>
                                     ) : (
                                       <div className="text-xs text-gray-400 h-full flex items-center justify-center">
                                         <span>Free</span>
                                       </div>
                                     )}
                                   </td>
                                 );
                               })}
                             </tr>
                           ))
                         ) : ( <tr><td colSpan={6} className="p-6 text-center text-muted-foreground">Timetable data is loading or unavailable.</td></tr> )}
                       </tbody>
                    </table>
                  </CardContent>
                </Card>
                {/* Weekly Summary for Parent */}
                 <Card className="mt-6">
                    <CardHeader><CardTitle>Weekly Lesson Summary</CardTitle></CardHeader>
                    <CardContent>
                      <p className={`mb-3 font-medium ${weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK ? 'text-red-600' : 'text-gray-700'}`}>
                        Total Lessons Scheduled: {weeklySummary.totalLessons} / {MAX_LESSONS_PER_WEEK}
                      </p>
                      {weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK && (
                           <Alert variant="destructive" className="mb-4"><AlertCircle className="h-4 w-4" /><AlertDescription>Lesson limit exceeded.</AlertDescription></Alert>
                      )}
                      <div className="space-y-2">
                         {weeklySummary.sortedSubjects.map(sub => (
                           <div key={sub.name} className="flex justify-between items-center text-sm">
                             <span>{sub.name}</span>
                             <Badge variant="secondary">{sub.count} lesson{sub.count > 1 ? 's' : ''}</Badge>
                           </div>
                         ))}
                       </div>
                     </CardContent>
                 </Card>
            </div>
        );
    }


  // --- STUDENT VIEW ---
  return (
    <div className="space-y-6">
      {/* Header and Buttons */}
       <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
           <div>
               <h2 className="text-2xl font-bold tracking-tight">Weekly Timetable</h2>
               <p className="text-muted-foreground">
                 Week {academicWeek} of 30 - Your class schedule based on your enrollments
               </p>
               {weeklySchedule.length > 0 && (
                 <div className="text-sm text-gray-600 mt-1">
                   {weeklySchedule.length} lessons scheduled this week
                 </div>
               )}
           </div>
           {/* --- BUTTON CONTROLS --- */}
           <div className="flex flex-wrap items-center gap-2">
                {/* Week Selector - Dropdown */}
                <div className="flex items-center gap-2">
                  <label htmlFor="week-select" className="text-sm font-medium text-muted-foreground">
                    Week:
                  </label>
                  <select
                    id="week-select"
                    value={academicWeek}
                    onChange={(e) => setAcademicWeek(parseInt(e.target.value))}
                    disabled={isLoading || isRefreshing || isGenerating}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium min-w-[80px] bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 30 }, (_, i) => i + 1).map(week => (
                      <option key={week} value={week}>
                        Week {week}
                      </option>
                    ))}
                  </select>
                </div>
                {/* Refresh Button */}
                <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing || isLoading || isGenerating}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing || isGenerating ? 'animate-spin' : ''}`} />
                  {isGenerating ? 'Generating...' : (isRefreshing ? 'Refreshing...' : 'Refresh')}
                </Button>
                 {/* Week/Day Toggle */}
                 <div className="flex items-center border rounded-md p-1">
                     <Button aria-label="View Weekly Timetable" variant={displayMode === 'week' ? 'secondary' : 'ghost'} size="sm" className="px-2 py-1 h-auto text-xs" onClick={() => setDisplayMode('week')}>
                       <CalendarDays className="h-3 w-3 mr-1" /> Week
                     </Button>
                     <Button aria-label="View Daily Timetable" variant={displayMode === 'day' ? 'secondary' : 'ghost'} size="sm" className="px-2 py-1 h-auto text-xs" onClick={() => setDisplayMode('day')}>
                       <View className="h-3 w-3 mr-1" /> Day
                     </Button>
                 </div>
                 {/* Grid/List Toggle */}
                 <Button variant="outline" size="sm" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
                   {viewMode === 'grid' ? <List className="h-4 w-4 mr-2" /> : <Grid className="h-4 w-4 mr-2" />}
                   {viewMode === 'grid' ? 'List View' : 'Grid View'}
                 </Button>
                 {/* Download Button */}
                 <Button variant="outline" size="sm" onClick={() => { /* Implement download */ }}>
                    <Download className="h-4 w-4 mr-2" /> Download
                 </Button>
           </div>
       </div>

        {/* --- DAY SELECTOR (Conditional) --- */}
        {displayMode === 'day' && (
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {DAYS_OF_WEEK.map(day => (
              <Button key={day} variant={selectedDay === day ? 'default' : 'outline'} size="sm" onClick={() => setSelectedDay(day)} className="flex-grow sm:flex-grow-0">
                {day}
              </Button>
            ))}
          </div>
        )}

      {/* --- TIMETABLE DISPLAY AREA --- */}
      {/* WEEK VIEW */}
      {displayMode === 'week' && (
          <>
            {/* Grid View (Week) */}
            {viewMode === 'grid' && (
              <Card>
                <CardContent className="p-0 overflow-auto">
                  <table className="w-full border-collapse min-w-[800px]">
                     <thead><tr className="bg-muted"><th className="p-3 border-r text-left font-medium text-muted-foreground sticky left-0 bg-muted z-10">Time</th>{DAYS_OF_WEEK.map(day => (<th key={day} className="p-3 text-center font-medium">{day}</th>))}</tr></thead>
                     <tbody>{timeSlots.length > 0 ? (timeSlots.map((timeSlotKey, index) => (<tr key={timeSlotKey} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}><td className="p-3 border-r text-sm text-muted-foreground font-medium whitespace-nowrap sticky left-0 bg-inherit z-10">{timeSlotKey}</td>{DAYS_OF_WEEK.map(day => {const dS = schedule[day]||{}; const e = dS[timeSlotKey]; const iL = typeof e==='object'; return (<td key={`${day}-${timeSlotKey}`} className="p-2 text-center border-l h-20 align-top">
                     {iL ? (
                        <div className={`p-2 rounded border ${getSubjectClass(e.subject)} h-full flex flex-col justify-between text-xs sm:text-sm`}>
                          <div>
                            <div className="font-semibold">{e.subject}</div>
                            <div className="text-gray-600 mt-1">{e.teacher||"AI Instructor"}</div>
                            {/* Show lesson status and reference */}
                            {e.lessonReference && (
                              <div className="mt-1">
                                <div className="text-xs text-gray-500">{e.lessonReference}</div>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  <span className={`inline-block px-1 py-0.5 rounded-sm text-xs ${getStatusBadgeClass(e.status || 'upcoming')}`}>
                                    {e.status === 'completed' ? 'Completed' :
                                    e.status === 'in_progress' ? 'In Progress' :
                                    e.status === 'past' ? 'Past' :
                                    'Upcoming'}
                                  </span>
                                  {(e.cognitiveLoad || e.cognitive_load) && (
                                    <span className="inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800">
                                      CL: {e.cognitiveLoad || e.cognitive_load}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                          {/* Join Lesson Button */}
                          {e.lessonReference && e.status !== 'completed' && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2 text-xs h-6 px-2"
                              onClick={(event) => {
                                event.stopPropagation();
                                handleJoinLesson(e);
                              }}
                            >
                              Join Lesson
                            </Button>
                          )}
                        </div>
                     ) : (
                        <div className="text-xs text-gray-400 h-full flex items-center justify-center">
                          <span>Free Period</span>
                        </div>
                     )}
                     </td>);})}</tr>))) : ( <tr><td colSpan={6} className="p-6 text-center">Loading...</td></tr> )}</tbody>
                  </table>
                </CardContent>
              </Card>
            )}
            {/* List View (Week) */}
            {viewMode === 'list' && (
               <div className="space-y-4"> 
                 {DAYS_OF_WEEK.map(day => { 
                   const dayScheduleMap = schedule[day] || {}; 
                   const lessons = Object.entries(dayScheduleMap)
                     .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
                     .filter(([, value]) => typeof value === 'object')
                     .map(([, value]) => value as TimetableEntry); 
                   
                   return (
                     <Card key={day}>
                       <CardHeader className="pb-2">
                         <CardTitle>{day}</CardTitle>
                       </CardHeader>
                       <CardContent>
                         {lessons.length > 0 ? (
                           <div className="space-y-3">
                             {lessons.map((lesson, idx) => (
                               <div key={`${day}-l-${idx}`} className={`p-3 rounded border ${getSubjectClass(lesson.subject)} flex justify-between items-center`}>
                                 <div className="flex-1">
                                   <div className="font-medium">{lesson.subject}</div>
                                   <div className="text-xs mt-1">{lesson.teacher || "AI Instructor"}</div>
                                   {lesson.lessonReference && (
                                     <div className="mt-1">
                                       <div className="text-xs text-gray-500">{lesson.lessonReference}</div>
                                       <div className="flex flex-wrap gap-1 mt-1">
                                         <span className={`inline-block px-1 py-0.5 rounded-sm text-xs ${getStatusBadgeClass(lesson.status || 'upcoming')}`}>
                                           {lesson.status === 'completed' ? 'Completed' :
                                           lesson.status === 'in_progress' ? 'In Progress' :
                                           lesson.status === 'past' ? 'Past' :
                                           'Upcoming'}
                                         </span>
                                         {(lesson.cognitiveLoad || lesson.cognitive_load) && (
                                           <span className="inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800">
                                             CL: {lesson.cognitiveLoad || lesson.cognitive_load}
                                           </span>
                                         )}
                                       </div>
                                     </div>
                                   )}
                                 </div>
                                 <div className="flex items-center gap-3">
                                   <div className="flex items-center">
                                     <Clock className="h-4 w-4 mr-1"/>
                                     <span className="text-sm">{lesson.startTime}</span>
                                   </div>
                                   {lesson.lessonReference && lesson.status !== 'completed' && (
                                     <Button
                                       size="sm"
                                       onClick={() => handleJoinLesson(lesson)}
                                     >
                                       Join Lesson
                                     </Button>
                                   )}
                                   <ChevronRight 
                                     className="h-4 w-4 cursor-pointer" 
                                     onClick={() => {
                                       const subjectId = lesson.subject_id || normalizeSubjectName(lesson.subject).toLowerCase().replace(/\s+/g, '_');
                                       router.push(`/subjects/${subjectId}/lessons?studentId=${studentIdProp}`);
                                     }}
                                   />
                                 </div>
                               </div>
                             ))}
                           </div>
                         ) : ( 
                           <div className="text-center py-4">No classes scheduled</div> 
                         )}
                       </CardContent>
                     </Card>
                   ); 
                 })}
               </div>
            )}
        </>
      )}
      {/* DAY VIEW (List Only) */}
       {displayMode === 'day' && (
           <Card>
               <CardHeader><CardTitle>{selectedDay}'s Schedule - Week {academicWeek}</CardTitle></CardHeader>
               <CardContent>
                   {(() => {
                       const dayScheduleMap = schedule[selectedDay] || {}; 
                       const lessons = Object.entries(dayScheduleMap)
                         .filter(([, value]) => typeof value === 'object')
                         .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
                         .map(([, value]) => value as TimetableEntry);
                       
                       if (lessons.length > 0) { 
                         return (
                           <div className="space-y-3">
                             {lessons.map((lesson, idx) => (
                               <div key={`${selectedDay}-d-${idx}`} className={`p-3 rounded border ${getSubjectClass(lesson.subject)} flex justify-between items-center`}>
                                 <div className="flex-1">
                                   <div className="font-medium">{lesson.subject}</div>
                                   <div className="text-xs mt-1">{lesson.teacher || "AI Instructor"}</div>
                                   {lesson.lessonReference && (
                                     <div className="mt-1">
                                       <div className="text-xs text-gray-500">{lesson.lessonReference}</div>
                                       <div className="flex flex-wrap gap-1 mt-1">
                                         <span className={`inline-block px-1 py-0.5 rounded-sm text-xs ${getStatusBadgeClass(lesson.status || 'upcoming')}`}>
                                           {lesson.status === 'completed' ? 'Completed' :
                                           lesson.status === 'in_progress' ? 'In Progress' :
                                           lesson.status === 'past' ? 'Past' :
                                           'Upcoming'}
                                         </span>
                                         {(lesson.cognitiveLoad || lesson.cognitive_load) && (
                                           <span className="inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800">
                                             CL: {lesson.cognitiveLoad || lesson.cognitive_load}
                                           </span>
                                         )}
                                       </div>
                                     </div>
                                   )}
                                 </div>
                                 <div className="flex items-center gap-3">
                                   <div className="flex items-center">
                                     <Clock className="h-4 w-4 mr-1"/>
                                     <span className="text-sm">{lesson.startTime}</span>
                                   </div>
                                   {lesson.lessonReference && lesson.status !== 'completed' && (
                                     <Button
                                       size="sm"
                                       onClick={() => handleJoinLesson(lesson)}
                                     >
                                       Join Lesson
                                     </Button>
                                   )}
                                   <ChevronRight 
                                     className="h-4 w-4 cursor-pointer" 
                                     onClick={() => {
                                       const subjectId = lesson.subject_id || normalizeSubjectName(lesson.subject).toLowerCase().replace(/\s+/g, '_');
                                       router.push(`/subjects/${subjectId}/lessons?studentId=${studentIdProp}`);
                                     }}
                                   />
                                 </div>
                               </div>
                             ))}
                           </div>
                         ); 
                       } else { 
                         return <div className="text-center py-6">No classes scheduled for {selectedDay}.</div>; 
                       }
                   })()}
               </CardContent>
           </Card>
       )}

      {/* --- Weekly Summary Section --- */}
       <Card className="mt-6">
           <CardHeader>
               <CardTitle>Week {academicWeek} Lesson Summary</CardTitle>
               <CardDescription>
                 Total lessons scheduled per subject for week {academicWeek} of 30.
               </CardDescription>
           </CardHeader>
           <CardContent>
               <div className="mb-4">
                   <div className="flex justify-between items-center mb-1">
                       <span className="text-sm font-medium text-gray-600">Total Weekly Lessons:</span>
                       <span className={`font-bold text-lg ${weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK ? 'text-red-600' : 'text-gray-800'}`}>
                           {weeklySummary.totalLessons} / {MAX_LESSONS_PER_WEEK}
                       </span>
                   </div>
                   <Progress value={(weeklySummary.totalLessons / MAX_LESSONS_PER_WEEK) * 100} className="h-2" />
                   {weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK && (
                       <p className="text-xs text-red-600 mt-1">Warning: Maximum recommended lessons exceeded.</p>
                   )}
               </div>
               <div className="space-y-2">
                   {weeklySummary.sortedSubjects.length > 0 ? (
                       weeklySummary.sortedSubjects.map(sub => (
                           <div key={sub.name} className="flex justify-between items-center text-sm p-2 rounded bg-gray-50">
                               <span className="font-medium text-gray-700">{sub.name}</span>
                               <Badge variant="secondary" className="text-xs">
                                   {sub.count} lesson{sub.count !== 1 ? 's' : ''}
                               </Badge>
                           </div>
                       ))
                   ) : (
                       <p className="text-sm text-center text-gray-500 py-4">No lessons scheduled to summarize.</p>
                   )}
               </div>
           </CardContent>
       </Card>

      {/* AI Optimization Card */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 mt-6">
            <CardContent className="p-4 flex items-center">
             <Cpu className="h-10 w-10 text-blue-500 mr-4 flex-shrink-0" />
             <div>
               <h3 className="font-medium text-blue-700">AI Timetable Optimization</h3>
               <p className="text-sm text-blue-600 mt-1">
                 This timetable was generated by AI, balancing subject distribution and cognitive load for optimal learning across {academicWeek === 1 ? 'the first week' : `week ${academicWeek}`} of 30 weeks.
               </p>
             </div>
           </CardContent>
      </Card>
    </div>
  );
};