// src/app/components/dashboard/SummaryTab.tsx

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Activity, // Icon for Recent Activity
    AlertCircle,
    Award,
    BarChart2,
    BookOpen,
    Brain,
    Calendar, // Keep for empty state
    CheckCircle,
    ChevronRight, // Icon for links
    Clock,
    Cpu,
    Eye, // Icon for View (Parent)
    ListChecks, // Icon for Assignments
    Loader2,
    MessageSquare,
    PlayCircle, // Icon for Join/Resume
    TrendingUp, // Icon for Progress
    Zap // Icon for interactive lessons?
} from 'lucide-react'; // Combined imports
import { Button } from "@/components/ui/button";
import {
    Card, CardContent, CardHeader, CardTitle, CardDescription
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge"; // For assignment priority
import { useRouter } from 'next/navigation';
import LoadingState from '@/components/LoadingState';
import type { ScheduleItem } from '@/types/dashboard';
import { db } from '@/lib/firebase'; // Import Firestore instance
import { collection, query, where, getDocs, Timestamp, doc, getDoc } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { LessonProgressService } from '@/services/LessonProgressService';
import type { LessonProgress as ServiceLessonProgress } from '@/services/LessonProgressService';
import { useToast } from '@/app/providers/ClientToastWrapper'; // Updated to match project structure
import { getAuthHeaders } from '@/lib/authService'; // Import getAuthHeaders
import { 
  calculateLessonStatus, 
  parseTimeRange, 
  getCurrentDayName,
  formatTimeRange,
  getCurrentAcademicWeek 
} from '@/lib/time-utils'; // Import standardized time utilities including getCurrentAcademicWeek

// --- Interfaces ---
interface Assignment { id: string; title: string; dueDate: string; priority: string; subject: string; }
interface RecentActivity { id: string; type: 'quiz' | 'assignment' | 'lesson'; title: string; subject: string; date: string; }
interface StudentData { id: string; name: string; gradeLevel: string; upcomingAssignments?: Assignment[]; recentActivities?: RecentActivity[]; completionStatus?: { overall?: number; subjects?: Record<string, number>; }; overallProgress?: number; subjectsCompleted?: number; isWeekend?: boolean; }
interface SummaryTabProps { studentId: string; studentData: StudentData | null; gradeLevel?: string; isParentView?: boolean; onTabChange?: (tab: string) => void; onStartLesson?: (lessonRef: string, subjectName: string) => void; }

// Extended ScheduleItem interface to include day
interface ExtendedScheduleItem extends ScheduleItem {
  day?: string;
}

// --- Inline Progress Component ---
const getProgressColor = (value: number) => {
  if (value >= 80) return 'bg-green-500';
  if (value >= 60) return 'bg-blue-500';
  if (value >= 40) return 'bg-yellow-500';
  return 'bg-red-500';
};

const Progress: React.FC<{ value?: number; className?: string }> = ({ value = 0, className = "" }) => {
  const bgColor = getProgressColor(value);
  const widthPercentage = Math.max(0, Math.min(100, value)); // Ensure value is between 0 and 100

  return (
    <div className={`h-2 w-full bg-gray-200 rounded-full overflow-hidden ${className}`}>
      <div
        className={`h-full rounded-full ${bgColor} transition-all duration-300 ease-in-out`}
        style={{ width: `${widthPercentage}%` }}
      />
    </div>
  );
};

// --- Main Component ---
// Floating action button for parents
const ParentFAB = ({ isParent }: { isParent: boolean }) => {
  const router = useRouter();
  if (!isParent) return null;
  
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        variant="outline"
        className="rounded-full p-4 shadow-lg bg-white hover:bg-gray-50"
        onClick={() => router.push('/parent-dashboard')}
      >
        Back to Parent Dashboard
      </Button>
    </div>
  );
};

export default function SummaryTab({
  studentId,
  studentData,
  gradeLevel,
  isParentView = false,
  onTabChange = () => {},
  onStartLesson = () => {}
}: SummaryTabProps) {
  const router = useRouter();
  const auth = getAuth(); // Get auth instance
  const { toast } = useToast(); // Use the toast hook properly

  // --- State ---
  const [todaysSchedule, setTodaysSchedule] = useState<ScheduleItem[] | null>(null);
  const [loadingSchedule, setLoadingSchedule] = useState<boolean>(true);
  const [scheduleError, setScheduleError] = useState<string | null>(null);
  const [lessonProgress, setLessonProgress] = useState<Record<string, ServiceLessonProgress>>({});
  const [loadingProgress, setLoadingProgress] = useState<boolean>(false);
  const [progressError, setProgressError] = useState<string | null>(null);

  // --- Fetch Today's Schedule ---
  const fetchTodaysSchedule = useCallback(async () => {
    if (!studentId) return;
    console.log(`[SummaryTab] Fetching schedule for ${studentId}`);
    setLoadingSchedule(true);
    setScheduleError(null);
    setTodaysSchedule(null); // Clear previous schedule

    try {
      // Get current academic week instead of using date
      const currentAcademicWeek = getCurrentAcademicWeek();
      const today = new Date();
      const currentDayName = getCurrentDayName(); // Get current day name (e.g., "monday")
      console.log(`[SummaryTab] Fetching schedule for week: ${currentAcademicWeek}, day: ${currentDayName}`);

      // Use week-based API instead of date-based
      const response = await fetch(`/api/timetable?studentId=${studentId}&week=${currentAcademicWeek}`, {
        headers: getAuthHeaders(), // Use the function to get headers
      });
      const result = await response.json();

      if (response.ok && result.success && Array.isArray(result.data?.schedule)) {
        console.log(`[SummaryTab] Received ${result.data.schedule.length} schedule items from API.`);

        // Filter for today's lessons only
        const todaysLessons = result.data.schedule.filter((item: any) => {
          // Check if the lesson is for today
          const lessonDay = item.day?.toLowerCase();
          return lessonDay === currentDayName.toLowerCase();
        });

        console.log(`[SummaryTab] Filtered ${todaysLessons.length} lessons for today (${currentDayName}).`);

        // Process items *without* relying on API status for sorting/initial display state
        const processedSchedule = todaysLessons.map((item: any) => ({
          ...item,
          // Use status from API primarily for initial visual cue, but rely on time/progress for buttons
          statusFromApi: item.status || 'upcoming',
          time: item.time || 'Time not set',
          subject: item.subject || 'Untitled Lesson',
          lessonRef: item.lessonRef || null,
          day: item.day, // Ensure day is included
          // *** Ensure these fields are present from API ***
          grade: item.grade || studentData?.gradeLevel || '', // Get grade from item, fallback
          level: item.level || '',       // Get level from item
          curriculum: item.curriculum || '', // Get curriculum from item
          country: item.country || '',     // Get country from item
          academicWeek: item.academicWeek || currentAcademicWeek, // Ensure academic week is set
        }));

        // *** MODIFIED SORTING: Strictly Chronological ***
        processedSchedule.sort((a, b) => {
          try {
             // Helper to parse start time string (e.g., "08:00") into minutes since midnight
            const parseStartTimeToMinutes = (timeStr: string): number => {
              if (!timeStr || !timeStr.includes(' - ')) return Infinity; // Handle invalid format
              const startTimePart = timeStr.split(' - ')[0].trim();
              const [hours, minutes] = startTimePart.split(':').map(Number);
              if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
                console.warn(`[Sort] Invalid time format detected: ${startTimePart}`);
                return Infinity; // Put invalid times last
              }
              return hours * 60 + minutes;
            };

            const aStartMinutes = parseStartTimeToMinutes(a.time);
            const bStartMinutes = parseStartTimeToMinutes(b.time);

            return aStartMinutes - bStartMinutes;
          } catch (e) {
            console.error("[Sort] Error parsing time for sorting:", a.time, b.time, e);
            return 0; // Avoid crashing sort on error
          }
        });

        console.log(`[SummaryTab] Sorted schedule chronologically.`);
        setTodaysSchedule(processedSchedule);

        // *** MODIFIED: Call fetchLessonProgress WITHOUT lessonRefs ***
        // Fetch progress data after schedule is set
        if (processedSchedule.length > 0) {
            // Check if *any* item has a lessonRef before fetching progress
            const hasRefs = processedSchedule.some(item => item.lessonRef);
            if (hasRefs) {
                fetchLessonProgress(); // Call without arguments
            } else {
                 setLessonProgress({}); // No lessons with refs, clear progress
                 setLoadingProgress(false);
                 console.log("[SummaryTab] No lessonRefs found in schedule, skipping progress fetch.");
            }
        } else {
            setLoadingProgress(false); // No schedule items, no progress to load
        }

      } else {
        throw new Error(result.error || result.message || "Could not load today's schedule from the weekly timetable.");
      }
    } catch (error: any) {
      console.error('[SummaryTab] Error fetching schedule:', error);
      setScheduleError(error.message || "An unexpected error occurred");
      setTodaysSchedule([]); // Set empty array on error
    } finally {
      setLoadingSchedule(false);
    }
  }, [studentId, studentData?.gradeLevel]); // Add studentData dependency if using fallback

  // --- Fetch Lesson Progress (Modified to fetch ALL) ---
  // *** REMOVED lessonRefs parameter ***
  const fetchLessonProgress = useCallback(async () => {
     if (!studentId) {
         setLessonProgress({});
         setLoadingProgress(false);
         return;
     }
     console.log(`[SummaryTab] Fetching ALL lesson progress for student ${studentId}...`);
     setLoadingProgress(true);
     setProgressError(null);
     try {
        // *** Use getAllLessonProgress instead ***
        const progressMap = await LessonProgressService.getAllLessonProgress(studentId);
        console.log(`[SummaryTab] Fetched all progress data (${Object.keys(progressMap).length} entries):`, progressMap);
        setLessonProgress(progressMap);
     } catch (error: any) {
        console.error('[SummaryTab] Error fetching all lesson progress:', error);
        // Check if the error is because the function doesn't exist
        if (error instanceof TypeError && error.message.includes("is not a function")) {
             setProgressError(`Service Error: getAllLessonProgress method not found.`);
             console.error("LessonProgressService might be missing the getAllLessonProgress static method.");
        } else {
             setProgressError(error.message || 'Could not load lesson progress.');
        }
        setLessonProgress({}); // Clear progress on error
     } finally {
        setLoadingProgress(false);
     }
  // Keep only studentId as dependency, as we fetch all progress based on it
  }, [studentId]);

  // Fetch schedule on initial load or when studentId changes
  useEffect(() => {
    if (studentId) {
      fetchTodaysSchedule(); // This will trigger fetchLessonProgress if needed
    } else {
        // Clear state if studentId becomes null/undefined
        setTodaysSchedule(null);
        setLessonProgress({});
        setLoadingSchedule(false);
        setLoadingProgress(false);
    }
  }, [studentId, fetchTodaysSchedule]);


  // --- Derived Data ---
  const upcomingAssignments = useMemo(() => studentData?.upcomingAssignments || [], [studentData]);
  const recentActivities = useMemo(() => studentData?.recentActivities || [], [studentData]);
  const subjectProgress = useMemo(() => studentData?.completionStatus?.subjects || {}, [studentData]);
  const displayGradeLevel = gradeLevel || studentData?.gradeLevel || 'N/A';
  const overallProgressValue = useMemo(() => studentData?.overallProgress ?? 0, [studentData]);

  // Keep only this declaration of completedTodayCount
  const completedTodayCount = useMemo(() => {
      return todaysSchedule?.filter(item => item.statusFromApi === 'completed').length ?? 0;
  }, [todaysSchedule]);

  // Recalculate nextLesson based on the *chronologically sorted* list
  const nextLesson = useMemo(() => {
    if (!todaysSchedule) return null;

    const now = new Date();
    const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();

    // Helper to parse start time string (e.g., "08:00") into minutes
    const parseStartTime = (timeStr: string): number => {
        try {
            if (!timeStr || !timeStr.includes(' - ')) return Infinity;
            const startTimePart = timeStr.split(' - ')[0].trim();
            const [hours, minutes] = startTimePart.split(':').map(Number);
            if (isNaN(hours) || isNaN(minutes)) return Infinity;
            return hours * 60 + minutes;
        } catch { return Infinity; }
    };
     // Helper to parse end time string (e.g., "08:45") into minutes
    const parseEndTime = (timeStr: string): number => {
        try {
            if (!timeStr || !timeStr.includes(' - ')) return -1;
            const endTimePart = timeStr.split(' - ')[1].trim();
            const [hours, minutes] = endTimePart.split(':').map(Number);
            if (isNaN(hours) || isNaN(minutes)) return -1;
            return hours * 60 + minutes;
        } catch { return -1; }
    };

    // Priority 1: Find the first lesson that is currently happening based on time
    const currentLesson = todaysSchedule.find(item => {
        const startMinutes = parseStartTime(item.time);
        const endMinutes = parseEndTime(item.time);
        return currentTimeMinutes >= startMinutes && currentTimeMinutes < endMinutes;
    });
    if (currentLesson) {
         console.log("[SummaryTab] Next Lesson (P1 - Current by Time):", currentLesson.subject);
         return { ...currentLesson, derivedStatus: 'current' }; // Add derived status
    }

    // Priority 2: Find the first lesson starting later today
    const upcomingLesson = todaysSchedule.find(item => {
        const startMinutes = parseStartTime(item.time);
        return startMinutes > currentTimeMinutes;
    });
     if (upcomingLesson) {
        console.log("[SummaryTab] Next Lesson (P2 - Upcoming by Time):", upcomingLesson.subject);
        return { ...upcomingLesson, derivedStatus: 'upcoming' }; // Add derived status
     }

    // If no current or upcoming found (all might be completed by time)
    console.log("[SummaryTab] No current or upcoming lesson found based on time.");
    return null;

  }, [todaysSchedule]);

  // Function to determine button state using standardized time utilities
  const getButtonState = (item: ExtendedScheduleItem) => {
      const lessonRef = item.lessonRef;
      const progressData = lessonRef ? lessonProgress[lessonRef] : null;
      const progressStatus = progressData?.status || 'not_started';

      // Fix isStudent detection: For students, buttons should be enabled when not in parent view
      // The auth comparison was causing issues since studentId (username) != auth.currentUser.uid (Firebase UID)
      // Instead, rely on isParentView prop which correctly indicates if this is a parent viewing
      const isStudent = !isParentView;

      // Use API status as primary source of truth for actual completion
      // Only use time-based status for current/upcoming determination
      const apiStatus = item.statusFromApi || 'upcoming';

      // Determine the effective lesson status
      let effectiveStatus = apiStatus;

      // If API says upcoming, check if it should be current based on time
      if (apiStatus === 'upcoming') {
        const timeStatus = calculateLessonStatus(
          item.time,
          item.day || getCurrentDayName(),
          new Date(),
          progressStatus
        );

        // Only override to 'current' if time-based calculation says it's current
        // Never override to 'completed' based on time alone
        if (timeStatus.status === 'current') {
          effectiveStatus = 'current';
        }
      }

      console.log(`[SummaryTab] Lesson status for "${item.subject}": ${effectiveStatus}`, {
        apiStatus,
        timeRange: item.time,
        day: item.day,
        progressStatus,
        lessonRef
      });

      // --- Define Action with ALL parameters ---
      let action = () => {
        if (lessonRef) {
          // Collect all available data from the item
          const params = {
            lessonRef: lessonRef,
            studentId: studentId || '',
            subject: item.subject || '',
            grade: item.grade || '',
            level: item.level || '',
            curriculum: item.curriculum || '',
            country: item.country || '',
          };

          // Check if we have the minimum required data
          const missingCriticalParams = ['lessonRef', 'studentId'].filter(key => !params[key]);
          
          if (missingCriticalParams.length > 0) {
            console.error(`Cannot navigate to lesson: Missing critical parameters: ${missingCriticalParams.join(', ')}`);
            toast({ title: "Error", description: `Cannot start lesson: Missing critical information`, variant: "destructive" });
            return;
          }

          // Log and navigate
          console.log('[SummaryTab] Navigating to lesson with params:', params);
          const queryParams = new URLSearchParams(params).toString();
          router.push(`/classroom?${queryParams}`);
        }
      };

      let buttonText = 'View';
      let buttonVariant: "default" | "outline" | "secondary" = 'secondary';
      let buttonClass = 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';
      let buttonDisabled = !lessonRef; // Generally disabled if no lessonRef
      let IconComponent = Eye;

      // --- Standardized Logic Flow ---
      if (effectiveStatus === 'completed') {
        // For students, update action to go to /start-lesson for review as well
        if (isStudent) {
          action = () => {
            if (lessonRef && studentId) {
              const params = {
                lessonRef: lessonRef,
                studentId: studentId || '',
                subject: item.subject || '',
                grade: item.grade || '',
                level: item.level || '',
                curriculum: item.curriculum || '',
                country: item.country || '',
              };
              const missingCriticalParams = ['lessonRef', 'studentId'].filter(key => !params[key]);
              if (missingCriticalParams.length > 0) {
                toast({ title: "Error", description: `Cannot review lesson: Missing critical information`, variant: "destructive" });
                return;
              }
              const queryParams = new URLSearchParams(params).toString();
              router.push(`/start-lesson?${queryParams}`);
            } else {
              toast({ title: "Error", description: `Cannot review lesson: Missing lessonRef or studentId`, variant: "destructive" });
            }
          };
        }

        buttonText = isStudent ? 'Review' : 'View';
        buttonVariant = 'outline';
        buttonClass = isStudent
            ? 'text-green-700 bg-green-50 hover:bg-green-100 border-green-300'
            : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';
        buttonDisabled = !lessonRef;
        IconComponent = isStudent ? CheckCircle : Eye;

      } else if (effectiveStatus === 'current') {
        // Lesson is happening now
        if (progressStatus === 'in_progress') {
            buttonText = isStudent ? 'Resume' : 'View';
            buttonVariant = 'outline';
            buttonClass = isStudent
                ? 'text-yellow-800 bg-yellow-50 hover:bg-yellow-100 border-yellow-300'
                : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';
            buttonDisabled = !isStudent || !lessonRef;
            IconComponent = isStudent ? PlayCircle : Eye;
        } else {
            buttonText = isStudent ? 'Join' : 'View';
            buttonVariant = isStudent ? 'default' : 'outline';
            buttonClass = isStudent 
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';
            buttonDisabled = !isStudent || !lessonRef;
            IconComponent = isStudent ? PlayCircle : Eye;

            // Update action to go to /start-lesson for students
            if (isStudent) {
              action = () => {
                if (lessonRef && studentId) {
                  const params = {
                    lessonRef: lessonRef,
                    studentId: studentId || '',
                    subject: item.subject || '',
                    grade: item.grade || '',
                    level: item.level || '',
                    curriculum: item.curriculum || '',
                    country: item.country || '',
                  };
                  const missingCriticalParams = ['lessonRef', 'studentId'].filter(key => !params[key]);
                  if (missingCriticalParams.length > 0) {
                    toast({ title: "Error", description: `Cannot start lesson: Missing critical information`, variant: "destructive" });
                    return;
                  }
                  const queryParams = new URLSearchParams(params).toString();
                  router.push(`/start-lesson?${queryParams}`);
                } else {
                  toast({ title: "Error", description: `Cannot start lesson: Missing lessonRef or studentId`, variant: "destructive" });
                }
              };
            }
        }
      } else {
        // Future lesson (upcoming)
        // Enable buttons for upcoming lessons so students can start them
        if (isStudent && lessonRef) {
          buttonText = 'Start';
          buttonVariant = 'default';
          buttonClass = 'bg-blue-600 hover:bg-blue-700 text-white';
          buttonDisabled = false;
          IconComponent = PlayCircle;

          action = () => {
            if (lessonRef && studentId) {
              const params = {
                lessonRef: lessonRef,
                studentId: studentId || '',
                subject: item.subject || '',
                grade: item.grade || '',
                level: item.level || '',
                curriculum: item.curriculum || '',
                country: item.country || '',
              };
              const missingCriticalParams = ['lessonRef', 'studentId'].filter(key => !params[key]);
              if (missingCriticalParams.length > 0) {
                toast({ title: "Error", description: `Cannot start lesson: Missing critical information`, variant: "destructive" });
                return;
              }
              const queryParams = new URLSearchParams(params).toString();
              router.push(`/start-lesson?${queryParams}`);
            } else {
              toast({ title: "Error", description: `Cannot start lesson: Missing lessonRef or studentId`, variant: "destructive" });
            }
          };
        } else {
          // For parents or lessons without lessonRef
          buttonText = 'Upcoming';
          buttonVariant = 'secondary';
          buttonClass = 'text-gray-500 bg-gray-200 cursor-not-allowed';
          buttonDisabled = true;
          action = () => {};
          IconComponent = Clock;
        }
      }

      console.log(`[SummaryTab] Final button state for "${item.subject}":
      - Effective Status: ${effectiveStatus}
      - API Status: ${apiStatus}
      - Progress status: ${progressStatus}
      - Button text: ${buttonText}
      - Button disabled: ${buttonDisabled}
      - Is Student: ${isStudent}
      - Has lessonRef: ${!!lessonRef}`);

      return { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent };
  };

  // --- Render Logic ---
  if (!studentData && !isParentView) return <LoadingState message="Loading student data..." />;
  if (loadingSchedule && !todaysSchedule) return <LoadingState message="Loading schedule..." />; // Show loading if schedule is null

  // Helper function for priority badge class
  const getPriorityBadgeClass = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function for activity icon
  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'quiz':
        return BookOpen;
      case 'assignment':
        return ListChecks;
      case 'lesson':
        return Zap;
      default:
        return Activity;
    }
  };

  return (
    <div className="space-y-8">

      {/* --- Row 1: Key Metrics & Next Action --- */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
         {/* Next Class Card */}
         <Card className="lg:col-span-1 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 shadow-md hover:shadow-lg transition-shadow">
             <CardHeader>
              <CardTitle className="text-lg font-semibold text-blue-800 flex items-center">
                <Clock size={20} className="mr-2 text-blue-600" /> Next Up
              </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col items-center justify-center text-center pt-2 pb-6 min-h-[200px]"> {/* Added min-height */}
                 {loadingSchedule || loadingProgress ? (
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 my-4" />
                 ) : scheduleError || progressError ? (
                    <div className="text-red-600 text-sm px-4">
                        <AlertCircle className="inline h-4 w-4 mr-1" />
                        {scheduleError || progressError || "Error loading data"}
                    </div>
                 ) : nextLesson ? (
                    <>
                        <h3 className="text-2xl font-bold text-gray-800 mb-1 truncate w-full px-4" title={nextLesson.subject}>
                            {nextLesson.subject}
                        </h3>
                        <p className="text-base text-gray-600 mb-4">
                            {nextLesson.derivedStatus === 'current'
                                ? `In Progress (${nextLesson.time.split(' - ')[1]})`
                                : `Starts at ${nextLesson.time.split(' - ')[0]}`
                            }
                        </p>
                        {(() => {
                            const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(nextLesson);
                            const isStudent = !isParentView;

                            // Adjust button text/action specifically for the "Next Up" card
                            let nextUpText = buttonText;
                            let nextUpAction = action;
                            let nextUpDisabled = buttonDisabled;
                            let nextUpClass = buttonClass;
                            let nextUpVariant = buttonVariant;

                            if (nextLesson.derivedStatus === 'current' && isStudent && !buttonDisabled) {
                                nextUpText = 'Join Lesson Now';
                                nextUpClass = 'bg-blue-600 hover:bg-blue-700 text-white'; // Ensure correct class for Join
                                nextUpVariant = 'default';
                            } else if (nextLesson.derivedStatus === 'upcoming') {
                                nextUpText = 'View on Timetable';
                                nextUpAction = () => onTabChange('timetable');
                                nextUpDisabled = false; // Allow viewing timetable
                                nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                                nextUpVariant = 'default'; // Use default style for primary action
                            } else if (buttonText === 'Review' && isStudent) {
                                // If it's reviewable but also the "next" item (unlikely unless only one lesson)
                                nextUpText = 'Review Lesson';
                            } else if (!isStudent) {
                                // Parent view adjustments for Next Up
                                nextUpText = 'View Details';
                                nextUpAction = () => onTabChange('timetable');
                                nextUpDisabled = false;
                                nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                                nextUpVariant = 'default';
                            }

                            return (
                                <Button
                                    size="lg"
                                    variant={nextUpVariant}
                                    className={`w-full max-w-xs ${nextUpClass} font-semibold transition-colors shadow-md hover:shadow-lg`}
                                    onClick={nextUpAction}
                                    disabled={nextUpDisabled}
                                >
                                     {nextUpText}
                                    {!nextUpDisabled && nextUpAction !== onTabChange ? <ChevronRight size={18} className="ml-2" /> : null}
                                </Button>
                            );
                        })()}
                    </>
                 ) : (
                     <>
                        <Calendar className="h-10 w-10 text-green-500 mb-3" />
                        <h3 className="text-xl font-semibold text-gray-700">All Classes Done!</h3>
                        <p className="text-sm text-gray-500 mt-1">Great job today!</p>
                         <Button
                             size="sm"
                             variant="outline"
                             className="mt-4"
                             onClick={() => onTabChange('timetable')}
                         > View Full Timetable </Button>
                     </>
                 )}
             </CardContent>
         </Card>

         {/* Other Stats Cards */}
        <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Overall Progress */}
          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-5">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-500">Overall Progress</h3>
                <TrendingUp size={18} className="text-green-500" />
              </div>
              <p className="text-3xl font-bold text-gray-800">{overallProgressValue}%</p>
              <Progress value={overallProgressValue} className="h-1.5 mt-3" />
            </CardContent>
          </Card>
          {/* Today's Classes */}
          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-5">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-500">Today's Classes</h3>
                <ListChecks size={18} className="text-purple-500" />
              </div>
              {loadingSchedule ? (
                 <Loader2 className="h-6 w-6 animate-spin text-purple-500" />
              ) : (
                 <>
                    <p className="text-3xl font-bold text-gray-800">{todaysSchedule?.length ?? 0}</p>
                    <p className="text-xs text-gray-500 mt-2">{completedTodayCount} completed</p>
                 </>
              )}
            </CardContent>
          </Card>
          {/* AI Tutor */}
          <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <CardContent className="p-5 flex flex-col justify-between h-full">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500">AI Form Tutor</h3>
                  <Brain size={18} className="text-amber-500" />
                </div>
                <p className="text-xs text-gray-600 mb-3">Need help or guidance?</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full border-amber-400 text-amber-700 hover:bg-amber-50"
                onClick={() => router.push('/ai-form-tutor')}
              >
                <MessageSquare size={14} className="mr-2" /> Ask Tutor
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* --- Row 2: Schedule & Supporting Info --- */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

        {/* Today's Schedule (Timeline View) */}
        <div className="lg:col-span-2">
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-gray-800">Today's Schedule</CardTitle>
            </CardHeader>
            <CardContent className="pt-2 pb-4 pr-2">
              {loadingSchedule && !todaysSchedule ? ( // Show loading only if data is null initially
                 <div className="h-60 flex items-center justify-center text-gray-500"><Loader2 className="h-6 w-6 animate-spin mr-2" /> Loading Schedule...</div>
              ) : scheduleError ? (
                 <div className="h-60 flex flex-col items-center justify-center text-red-600"><AlertCircle className="h-8 w-8 mb-2" /> <p>{scheduleError}</p></div>
              ) : todaysSchedule && todaysSchedule.length > 0 ? (
                <div className="relative pl-6">
                  {/* Timeline Line */}
                  <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  {todaysSchedule.map((item, index) => {
                    const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(item);
                    const lessonRef = item.lessonRef;
                    const progressStatus = lessonRef ? lessonProgress[lessonRef]?.status : 'not_started';
                    const timeStatusFromApi = item.statusFromApi; // Use the status calculated by API for dot color

                    // Determine dot color based on API status primarily
                    let dotClass = 'bg-gray-300'; // Default: upcoming
                    if (timeStatusFromApi === 'completed' || progressStatus === 'completed') {
                        dotClass = 'bg-green-500';
                    } else if (timeStatusFromApi === 'current') {
                        dotClass = 'bg-blue-500 ring-4 ring-blue-200 animate-pulse';
                    } else if (progressStatus === 'in_progress') {
                         dotClass = 'bg-yellow-500'; // In progress but maybe not current time
                    }

                    return (
                      <div key={item.id || index} className="relative pl-8 py-4 group">
                        {/* Timeline Dot */}
                        <div className={`absolute left-[18px] top-7 -ml-[7px] w-3.5 h-3.5 rounded-full border-2 border-white group-hover:scale-110 transition-transform ${dotClass}`}></div>

                        {/* Content */}
                        <div className="flex items-center justify-between gap-4 ml-1">
                          {/* Left: Subject Info */}
                          <div className="flex-grow min-w-0">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-0.5">{item.time}</p>
                            <h4 className="font-semibold text-gray-800 truncate" title={item.subject}>{item.subject}</h4>
                            <p className="text-xs text-gray-500 mt-0.5 flex items-center"> <Cpu size={12} className="mr-1 text-gray-400"/> {item.instructor || "AI Instructor"} </p>
                             {/* Show progress status if loading or available */}
                            {(loadingProgress && lessonRef) && <span className="text-xs text-gray-400 italic ml-2"> (loading status...)</span>}
                            {progressError && lessonRef && <span className="text-xs text-red-500 ml-2"> (status error)</span>}
                          </div>

                          {/* Right: Button - Use the calculated state */}
                          <div className="flex-shrink-0">
                            {loadingProgress && lessonRef ? (
                                <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                             ) : (
                                <Button
                                  size="sm"
                                  variant={buttonVariant}
                                  className={`px-3 py-1.5 rounded-md text-xs font-medium transition-all shadow-sm hover:shadow-md ${buttonClass}`}
                                  onClick={action}
                                  disabled={buttonDisabled || loadingProgress} // Disable while progress loads
                                >
                                  <IconComponent size={14} className="mr-1.5" />
                                  {buttonText}
                                </Button>
                             )}
                          </div>
                        </div>
                      </div>
                    ); // End of return for map callback
                  })}
                </div>
              ) : (
                <div className="h-60 flex flex-col items-center justify-center text-center text-gray-500">
                    <Calendar className="h-10 w-10 text-gray-400 mb-3" />
                    <p>No classes scheduled for today.</p>
                 </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Subject Progress */}
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-semibold text-gray-700">Subject Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.keys(subjectProgress).length > 0 ? (
                Object.entries(subjectProgress).map(([subject, progress]) => (
                  <div key={subject}>
                    <div className="flex justify-between items-baseline mb-1">
                      <span className="text-sm font-medium text-gray-800">{subject}</span>
                      <span className="text-sm font-semibold text-gray-600">{progress}%</span>
                    </div>
                    <Progress value={progress} className="h-1.5" />
                  </div>
                ))
              ) : (
                 <p className="text-sm text-center text-gray-500 py-4">No progress data available.</p>
              )}
                 <Button variant="link" size="sm" className="p-0 h-auto text-blue-600 text-xs" onClick={() => onTabChange('reports')}>View Detailed Reports</Button>
            </CardContent>
          </Card>

          {/* Upcoming Assignments */}
          <Card className="border border-gray-200 shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-base font-semibold text-gray-700">Upcoming Assignments</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {upcomingAssignments.length > 0 ? (
                upcomingAssignments.slice(0, 3).map((assignment) => ( // Show top 3
                  <div key={assignment.id} className="flex items-center justify-between gap-2 p-2 rounded bg-gray-50/50 border border-gray-100">
                      <BookOpen size={16} className="text-purple-500 flex-shrink-0" />
                      <div className="flex-grow min-w-0">
                          <p className="text-sm font-medium text-gray-800 truncate" title={assignment.title}>{assignment.title} ({assignment.subject})</p>
                          <p className="text-xs text-gray-500">Due: {assignment.dueDate}</p>
                      </div>
                      {/* Use 'secondary' or 'default' variant for Badge */}
                      <Badge variant="secondary" className={`text-xs ${getPriorityBadgeClass(assignment.priority)}`}>
                          {assignment.priority}
                      </Badge>
                  </div>
                ))
              ) : (
                 <p className="text-sm text-center text-gray-500 py-4">No upcoming assignments.</p>
              )}
                {upcomingAssignments.length > 3 && (
                     <Button variant="link" size="sm" className="p-0 h-auto text-blue-600 text-xs" onClick={() => onTabChange('homework')}>View All Assignments</Button>
                )}
            </CardContent>
          </Card>
        </div>
      </div>
      {isParentView && <ParentFAB isParent={isParentView} />} {/* Conditionally render FAB */}
    </div>
  );
}