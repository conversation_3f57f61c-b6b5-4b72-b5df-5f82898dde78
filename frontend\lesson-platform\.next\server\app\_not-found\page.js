/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(rsc)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(rsc)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ClientProviders = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ClientProviders() from the server but ClientProviders is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\app\\client-providers.tsx",
"ClientProviders",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7af86ea72fdb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3YWY4NmVhNzJmZGJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _client_providers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./client-providers */ \"(rsc)/./src/app/client-providers.tsx\");\n/* harmony import */ var _providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/ThemeProvider */ \"(rsc)/./src/app/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(rsc)/./src/components/ErrorBoundary.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'Solynta Academy',\n    description: 'Digital learning platform for Nigerian students'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_client_providers__WEBPACK_IMPORTED_MODULE_3__.ClientProviders, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 20\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFFSDtBQUM4QjtBQUNDO0FBQ0M7QUFFaEQsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7OzBCQUN0Qyw4REFBQ0M7Ozs7O1lBY007MEJBQU0sOERBQUNDOzBCQUNaLDRFQUFDVixpRUFBYUE7OEJBQ1osNEVBQUNGLDhEQUFlQTtrQ0FDZCw0RUFBQ0MsZ0VBQWFBO3NDQUNYTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0JztcclxuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJztcclxuaW1wb3J0IHsgQ2xpZW50UHJvdmlkZXJzIH0gZnJvbSAnLi9jbGllbnQtcHJvdmlkZXJzJztcclxuaW1wb3J0IFRoZW1lUHJvdmlkZXIgZnJvbSAnLi9wcm92aWRlcnMvVGhlbWVQcm92aWRlcic7XHJcbmltcG9ydCBFcnJvckJvdW5kYXJ5IGZyb20gJ0AvY29tcG9uZW50cy9FcnJvckJvdW5kYXJ5JztcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6ICdTb2x5bnRhIEFjYWRlbXknLFxyXG4gIGRlc2NyaXB0aW9uOiAnRGlnaXRhbCBsZWFybmluZyBwbGF0Zm9ybSBmb3IgTmlnZXJpYW4gc3R1ZGVudHMnLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz4gXHJcbiAgICAgIDxoZWFkPlxyXG4gICAgICAgIHsvKiBNZXRhIHRhZ3MgdGVtcG9yYXJpbHkgcmVtb3ZlZCAqL31cclxuICAgICAgICB7LyogPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz4gKi99XHJcbiAgICAgICAgey8qIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJ3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MVwiIC8+ICovfVxyXG4gICAgICAgIHsvKiA8bWV0YVxyXG4gICAgICAgICAgaHR0cEVxdWl2PVwiQ29udGVudC1TZWN1cml0eS1Qb2xpY3lcIlxyXG4gICAgICAgICAgY29udGVudD1cImRlZmF1bHQtc3JjICdzZWxmJyBodHRwczovL2FwaXMuZ29vZ2xlLmNvbSBodHRwczovLyouZ29vZ2xlYXBpcy5jb20gaHR0cHM6Ly8qLmdzdGF0aWMuY29tO1xyXG4gICAgICAgICAgICAgICAgICAgY29ubmVjdC1zcmMgJ3NlbGYnIGh0dHBzOi8vYXBpcy5nb29nbGUuY29tIGh0dHBzOi8vKi5nb29nbGVhcGlzLmNvbSBodHRwczovLyouZmlyZWJhc2Vpby5jb20gaHR0cHM6Ly8qLmNsb3VkZnVuY3Rpb25zLm5ldCBodHRwczovLyouZmlyZWJhc2UuZ29vZ2xlYXBpcy5jb207XHJcbiAgICAgICAgICAgICAgICAgICBzY3JpcHQtc3JjICdzZWxmJyAndW5zYWZlLWlubGluZScgJ3Vuc2FmZS1ldmFsJyBodHRwczovL2FwaXMuZ29vZ2xlLmNvbSBodHRwczovLyouZ29vZ2xlYXBpcy5jb20gaHR0cHM6Ly8qLmZpcmViYXNlaW8uY29tIGh0dHBzOi8vKi5jbG91ZGZ1bmN0aW9ucy5uZXQgaHR0cHM6Ly8qLmdzdGF0aWMuY29tO1xyXG4gICAgICAgICAgICAgICAgICAgc3R5bGUtc3JjICdzZWxmJyAndW5zYWZlLWlubGluZSc7XHJcbiAgICAgICAgICAgICAgICAgICBpbWctc3JjICdzZWxmJyBkYXRhOiBodHRwczovLyouZ29vZ2xlYXBpcy5jb207XHJcbiAgICAgICAgICAgICAgICAgICBmb250LXNyYyAnc2VsZicgZGF0YTo7XHJcbiAgICAgICAgICAgICAgICAgICBmcmFtZS1zcmMgJ3NlbGYnIGh0dHBzOi8vYXBpcy5nb29nbGUuY29tIGh0dHBzOi8vKi5nb29nbGUuY29tIGh0dHBzOi8vKi5maXJlYmFzZWFwcC5jb20gaWRlbnRpdHl0b29sa2l0Lmdvb2dsZWFwaXMuY29tO1wiXHJcbiAgICAgICAgLz4gKi99XHJcbiAgICAgIDwvaGVhZD4gICAgICA8Ym9keT5cclxuICAgICAgICA8RXJyb3JCb3VuZGFyeT5cclxuICAgICAgICAgIDxDbGllbnRQcm92aWRlcnM+XHJcbiAgICAgICAgICAgIDxUaGVtZVByb3ZpZGVyPlxyXG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxyXG4gICAgICAgICAgPC9DbGllbnRQcm92aWRlcnM+XHJcbiAgICAgICAgPC9FcnJvckJvdW5kYXJ5PlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDbGllbnRQcm92aWRlcnMiLCJUaGVtZVByb3ZpZGVyIiwiRXJyb3JCb3VuZGFyeSIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIiwiaGVhZCIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call the default export of \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\OneDrive\\\\\\\\Desktop\\\\\\\\Desktop\\\\\\\\Solynta_Website\\\\\\\\frontend\\\\\\\\lesson-platform\\\\\\\\src\\\\\\\\app\\\\\\\\providers\\\\\\\\ThemeProvider.tsx\\\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ThemeProvider.tsx\",\n\"default\",\n));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\Desktop\\Solynta_Website\\frontend\\lesson-platform\\src\\components\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(ssr)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(ssr)/./src/app/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(ssr)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(ssr)/./src/app/providers.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \n\n // Back to original providers\nfunction ClientProviders({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientProviders.useEffect\": ()=>{\n            const handleRejection = {\n                \"ClientProviders.useEffect.handleRejection\": (event)=>{\n                    console.error(\"!!!! GLOBAL UNHANDLED REJECTION !!!!\", event.reason);\n                    // Handle ChunkLoadError specifically\n                    if (event.reason?.name === 'ChunkLoadError' || event.reason?.message?.includes('Loading chunk') || event.reason?.message?.includes('Loading CSS chunk')) {\n                        console.log('ChunkLoadError detected, reloading page...');\n                        event.preventDefault(); // Prevent the default unhandled rejection behavior\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleRejection\"];\n            const handleError = {\n                \"ClientProviders.useEffect.handleError\": (event)=>{\n                    console.error(\"!!!! GLOBAL ERROR !!!!\", event.error);\n                    // Handle ChunkLoadError from regular errors too\n                    if (event.error?.name === 'ChunkLoadError' || event.error?.message?.includes('Loading chunk') || event.error?.message?.includes('Loading CSS chunk')) {\n                        console.log('ChunkLoadError detected via error event, reloading page...');\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleError\"];\n            window.addEventListener('unhandledrejection', handleRejection);\n            window.addEventListener('error', handleError);\n            return ({\n                \"ClientProviders.useEffect\": ()=>{\n                    window.removeEventListener('unhandledrejection', handleRejection);\n                    window.removeEventListener('error', handleError);\n                }\n            })[\"ClientProviders.useEffect\"];\n        }\n    }[\"ClientProviders.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"Loading application components...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/client-providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=NextUIProvider!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers/AuthProvider */ \"(ssr)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(ssr)/./src/app/providers/SessionProvider.tsx\");\n/* harmony import */ var _providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/ClientToastWrapper */ \"(ssr)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(ssr)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n// Import your AuthProvider etc.\n\n // NextAuth wrapper\n\n\n // Import the real auth function\n// Simplified SessionProvider that provides the interface expected by useSession\nfunction SimpleSessionProvider({ children }) {\n    const [backendSessionId, setBackendSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const value = {\n        backendSessionId,\n        user,\n        setUserSession: setUser,\n        setBackendSessionId,\n        clearSession: ()=>{\n            setUser(null);\n            setBackendSessionId(null);\n        },\n        isReady,\n        isLoading,\n        getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_7__.getAuthHeaders)(backendSessionId),\n        userRole: user?.role || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__.SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSessionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.NextUIProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__.ClientToastWrapper, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/app/providers/AuthProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isAuthenticated: false,\n    studentSession: null,\n    userRole: null,\n    manualSyncWithLocalStorage: ()=>{},\n    user: null,\n    userData: null,\n    childrenData: null,\n    loading: true,\n    error: null,\n    refreshUserData: async ()=>{},\n    refreshChildrenData: async ()=>{},\n    handleLoginSuccess: async ()=>{},\n    logout: async ()=>{}\n});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childrenData, setChildrenData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentSession, setStudentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)(); // session will be of type Session | null\n    console.log('AuthProvider: Initial state', {\n        loading,\n        isAuthenticated,\n        userRole,\n        session\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const manualSyncWithLocalStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[manualSyncWithLocalStorage]\": ()=>{\n            const storedUserData = localStorage.getItem('user_data');\n            if (storedUserData) {\n                setUserData(JSON.parse(storedUserData));\n            }\n            const storedChildrenData = localStorage.getItem('children_data');\n            if (storedChildrenData) {\n                setChildrenData(JSON.parse(storedChildrenData));\n            }\n        }\n    }[\"AuthProvider.useCallback[manualSyncWithLocalStorage]\"], []);\n    const refreshUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUserData]\": async ()=>{\n            if (!user?.uid) {\n                console.log(\"AuthProvider: refreshUserData - No user.uid, exiting.\");\n                setUserData(null); // Ensure data is cleared if no user\n                setUserRole(null);\n                return;\n            }\n            console.log(`AuthProvider: Refreshing user data for UID: ${user.uid}`);\n            setError(null); // Clear previous errors\n            // Determine the most likely role based on login method and localStorage hints\n            // This is an initial guess before confirming with fetched data\n            let likelyRole = localStorage.getItem('user_role') || null;\n            const loginMethodHint = user.providerData?.[0]?.providerId; // 'password', 'google.com', 'custom'\n            console.log(`AuthProvider: refreshUserData - Login method hint: ${loginMethodHint}, localStorage role hint: ${likelyRole}`);\n            if (!likelyRole) {\n                if (loginMethodHint === 'custom') {\n                    likelyRole = 'student'; // Custom tokens are used for student logins in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'student' based on custom token login.\");\n                } else if (loginMethodHint === 'password' || loginMethodHint === 'google.com') {\n                    likelyRole = 'parent'; // Email/Google logins are parents in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'parent' based on email/Google login.\");\n                } else {\n                    console.log(\"AuthProvider: refreshUserData - Could not reliably guess initial role.\");\n                    // Proceed cautiously, maybe try student first as it seems more common? Or try both carefully.\n                    // Let's try student first for safety in this scenario.\n                    likelyRole = 'student';\n                }\n            }\n            try {\n                // --- Logic based on likely role ---\n                let roleConfirmed = false;\n                // Try fetching data based on the likely role first\n                if (likelyRole === 'parent') {\n                    console.log(\"AuthProvider: refreshUserData - Prioritizing parent fetch.\");\n                    // 1. Try Parent Dashboard Endpoint\n                    try {\n                        const dashboardResponse = await fetch(`/api/parent/dashboard?parentId=${user.uid}`);\n                        if (dashboardResponse.ok) {\n                            const dashboardData = await dashboardResponse.json();\n                            console.log(\"AuthProvider: Parent dashboard response:\", dashboardData);\n                            if (dashboardData.success && dashboardData.data?.parent) {\n                                console.log(\"AuthProvider: Parent data found via dashboard.\");\n                                const parentData = {\n                                    ...dashboardData.data.parent,\n                                    uid: user.uid,\n                                    role: 'parent'\n                                };\n                                setUserData(parentData);\n                                localStorage.setItem('user_data', JSON.stringify(parentData));\n                                setUserRole('parent'); // Confirm role\n                                console.log(\"AuthProvider: Set user data/role to parent (dashboard).\");\n                                roleConfirmed = true;\n                                return; // Success\n                            } else {\n                                console.log(\"AuthProvider: Parent dashboard endpoint didn't provide valid parent data. Will try token verification.\");\n                            }\n                        } else {\n                            console.warn(`AuthProvider: Parent dashboard fetch failed (${dashboardResponse.status}). Will try token verification.`);\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error with parent dashboard fetch:\", e);\n                    }\n                    // 2. Fallback: Verify Firebase Token (for parents)\n                    if (!roleConfirmed) {\n                        try {\n                            console.log(\"AuthProvider: Attempting parent fallback: verify-firebase-token\");\n                            const token = await user.getIdToken(true);\n                            const verifyResponse = await fetch('/api/auth/verify-firebase-token', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    token\n                                })\n                            });\n                            if (verifyResponse.ok) {\n                                const verifyData = await verifyResponse.json();\n                                console.log(\"AuthProvider: verify-firebase-token response:\", verifyData);\n                                if (verifyData.success && verifyData.user) {\n                                    console.log(\"AuthProvider: Parent data found via token verification.\");\n                                    const parentData = {\n                                        ...verifyData.user,\n                                        uid: user.uid,\n                                        role: 'parent'\n                                    };\n                                    setUserData(parentData);\n                                    localStorage.setItem('user_data', JSON.stringify(parentData));\n                                    setUserRole('parent'); // Confirm role\n                                    console.log(\"AuthProvider: Set user data/role to parent (token verify).\");\n                                    roleConfirmed = true;\n                                    return; // Success\n                                } else {\n                                    console.log(\"AuthProvider: Token verification failed to provide parent data.\");\n                                }\n                            } else {\n                                console.warn(`AuthProvider: verify-firebase-token fetch failed (${verifyResponse.status}).`);\n                            }\n                        } catch (e) {\n                            console.error(\"AuthProvider: Error with token verification:\", e);\n                        }\n                    }\n                }\n                // If parent fetches failed or role is student, try student fetch\n                if (!roleConfirmed) {\n                    console.log(\"AuthProvider: refreshUserData - Trying student fetch.\");\n                    try {\n                        const studentIdForFetch = localStorage.getItem('student_id') || user.uid; // Use consistent variable\n                        console.log(`AuthProvider: Fetching student data with studentId: ${studentIdForFetch}`);\n                        const studentResponse = await fetch(`/api/student-data?studentId=${studentIdForFetch}`);\n                        // --- ADD ROBUST RESPONSE HANDLING ---\n                        if (!studentResponse.ok) {\n                            const errorText = await studentResponse.text(); // Get raw error text\n                            console.error(`AuthProvider: API call to /student-data failed with status ${studentResponse.status}. Response: ${errorText}`);\n                            // Try to parse as JSON only if content-type suggests it, otherwise use text\n                            let backendError = `Failed to fetch student data (status: ${studentResponse.status})`;\n                            try {\n                                if (studentResponse.headers.get(\"content-type\")?.includes(\"application/json\")) {\n                                    const errorJson = JSON.parse(errorText); // This might fail if errorText is HTML\n                                    backendError = errorJson.error || errorJson.message || backendError;\n                                } else if (errorText.length > 0 && errorText.length < 300) {\n                                    backendError = errorText;\n                                }\n                            } catch (parseError) {\n                                console.warn(\"AuthProvider: Could not parse error response from /student-data as JSON. Raw text was:\", errorText);\n                            }\n                            throw new Error(backendError);\n                        }\n                        // --- END ROBUST RESPONSE HANDLING ---\n                        // If response.ok is true, then try to parse JSON\n                        const studentData = await studentResponse.json(); // This is where the SyntaxError was happening\n                        console.log(\"AuthProvider: Student data API response received:\", studentData);\n                        if (studentData.success && studentData.student) {\n                            const studentInfo = studentData.student;\n                            console.log(\"AuthProvider: Student data found and valid.\");\n                            // ... (rest of your logic for successful student data) ...\n                            // Add code to set claims if not already set\n                            try {\n                                const idTokenResult = await user.getIdTokenResult();\n                                const hasCorrectClaims = idTokenResult.claims.role === 'student' && idTokenResult.claims.student_id === studentInfo.id;\n                                if (!hasCorrectClaims) {\n                                    console.log(\"AuthProvider: Student claims missing or incorrect, setting them now\");\n                                    const idToken = await user.getIdToken();\n                                    const claimResponse = await fetch('/api/auth/set-student-claims', {\n                                        method: 'POST',\n                                        headers: {\n                                            'Content-Type': 'application/json',\n                                            'Authorization': `Bearer ${idToken}`\n                                        },\n                                        body: JSON.stringify({\n                                            studentId: studentInfo.id\n                                        })\n                                    });\n                                    if (claimResponse.ok) {\n                                        console.log(\"AuthProvider: Student claims set successfully\");\n                                        // Force token refresh to get the new claims\n                                        await user.getIdToken(true);\n                                    } else {\n                                        console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                                    }\n                                }\n                            } catch (e) {\n                                console.error(\"AuthProvider: Error checking/setting student claims:\", e);\n                            }\n                            const studentUserData = {\n                                ...studentInfo,\n                                role: 'student',\n                                enrollments: studentData.enrollments || [],\n                                timetable: studentData.timetable || null\n                            };\n                            const studentId = studentInfo.id;\n                            setUserData(studentUserData);\n                            localStorage.setItem('user_data', JSON.stringify(studentUserData));\n                            setUserRole('student');\n                            setStudentSession(studentId);\n                            localStorage.setItem('student_id', studentId);\n                            if (localStorage.getItem('current_session') === studentId) {\n                                localStorage.removeItem('current_session');\n                            }\n                            localStorage.setItem('user_role', 'student');\n                            console.log(`AuthProvider: Set user data/role to student. Student ID: ${studentId}`);\n                            roleConfirmed = true; // Make sure to set this on success\n                            return; // Exit refreshUserData successfully\n                        } else {\n                            console.warn(`AuthProvider: Student data fetch was 'ok' but API reported not success or missing data. Message: ${studentData?.error || studentData?.message}`);\n                            throw new Error(studentData?.error || studentData?.message || 'Student data fetch failed (API level).');\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error block for student data fetch:\", e);\n                        // If we are here, it means student fetch failed.\n                        // The specific error (e.g., SyntaxError, or the error thrown from !response.ok) will be 'e'.\n                        // You might want to set a specific error or attempt recovery.\n                        // The outer catch block will handle setting the general error state.\n                        throw e; // Re-throw to be handled by the main catch in refreshUserData\n                    }\n                }\n                // If we get here without returning, no role was confirmed\n                if (!roleConfirmed) {\n                    throw new Error('Could not determine user role. Please try logging in again.');\n                }\n            } catch (err) {\n                console.error('AuthProvider: Error during refreshUserData:', err);\n                // Provide a specific error message based on the type of error if possible\n                let specificErrorMessage = 'Failed to refresh user data. Please try logging in again.';\n                if (err instanceof Error) {\n                    specificErrorMessage = `Failed to refresh user data: ${err.message}`;\n                    if (err.message.includes('Failed to fetch valid student data')) {\n                        // Attempt recovery logic as you have\n                        const studentIdHint = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                        if (studentIdHint) {\n                            // your recovery logic ...\n                            console.log(`AuthProvider: Recovering with minimal student data using ID: ${studentIdHint}`);\n                            const minimalStudentData = {\n                                id: studentIdHint,\n                                uid: user.uid,\n                                role: 'student',\n                                name: localStorage.getItem('student_name') || 'Student',\n                                recoveryMode: true\n                            };\n                            setUserData(minimalStudentData);\n                            setUserRole('student');\n                            setStudentSession(studentIdHint);\n                            localStorage.setItem('user_data', JSON.stringify(minimalStudentData));\n                            // If recovery is successful, set state and RETURN here to avoid setting generic error.\n                            console.log(\"AuthProvider: Recovered with minimal student data.\");\n                            setError(null); // Clear any previous errors if recovery was successful\n                            return;\n                        } else {\n                            specificErrorMessage = \"Could not retrieve critical student information. Please re-login.\";\n                        }\n                    } else if (err.message.includes('Could not determine user role')) {\n                        specificErrorMessage = \"Could not confirm your user role. Please try logging in again.\";\n                    }\n                }\n                setError(specificErrorMessage); // Set a user-friendly error message\n                // Clear sensitive/stale data on critical refresh failure\n                setUserData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                localStorage.removeItem('user_data');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('current_session'); // and other relevant keys\n                setIsAuthenticated(false); // Consider if a refresh failure means unauthenticated\n            // setLoading(false); // Ensure loading is false if refresh fails - This is handled by onAuthStateChanged\n            // Consider a more drastic action like redirecting to login if refresh fails consistently\n            // router.push('/login?error=session_refresh_failed');\n            }\n        // setLoading(false); // Ensure loading is set to false at the end of the function\n        // This is now handled by onAuthStateChanged's setLoading(false)\n        }\n    }[\"AuthProvider.useCallback[refreshUserData]\"], [\n        user\n    ]);\n    const refreshChildrenData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshChildrenData]\": async ()=>{\n            if (!user?.uid) return;\n            try {\n                // First try the parent dashboard endpoint which includes all child data\n                const dashboardResponse = await fetch(`/api/parent/dashboard?parentId=${user.uid}`);\n                const dashboardData = await dashboardResponse.json();\n                if (dashboardData.success) {\n                    const children = dashboardData.data.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                    return;\n                }\n                // Fallback to get-children endpoint if dashboard fails\n                const childrenResponse = await fetch(`/api/parent/get-children?parentId=${user.uid}`);\n                const childrenData = await childrenResponse.json();\n                if (childrenData.success) {\n                    const children = childrenData.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                } else {\n                    setError(childrenData.error || 'Failed to fetch children data');\n                }\n            } catch (err) {\n                console.error('Error fetching children data:', err);\n                setError('Failed to fetch children data');\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshChildrenData]\"], [\n        user\n    ]);\n    const handleLoginSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLoginSuccess]\": async (customToken, studentIdHint)=>{\n            // *** REMOVE ALERT ***\n            // alert(\"AuthProvider: handleLoginSuccess CALLED!\"); \n            // *** END REMOVE ALERT ***\n            console.log(\"AuthProvider: handleLoginSuccess called.\"); // Log start\n            setLoading(true);\n            setError(null);\n            try {\n                // Validate token format\n                if (!customToken || typeof customToken !== 'string') {\n                    throw new Error('Invalid token format: Token is missing or not a string.');\n                }\n                if (!customToken.includes('.')) {\n                    throw new Error('Invalid token format: Token does not appear to be a valid JWT.');\n                }\n                if (customToken.length < 50) {\n                    throw new Error('Invalid token format: Token is too short to be valid.');\n                } // Sign in using the custom token with timeout\n                console.log(\"AuthProvider: Calling signInWithCustomToken...\"); // Log before call\n                const signInPromise = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithCustomToken)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, customToken);\n                const timeoutPromise = new Promise({\n                    \"AuthProvider.useCallback[handleLoginSuccess]\": (_, reject)=>setTimeout({\n                            \"AuthProvider.useCallback[handleLoginSuccess]\": ()=>reject(new Error('Sign-in timeout: The operation took too long to complete'))\n                        }[\"AuthProvider.useCallback[handleLoginSuccess]\"], 30000)\n                }[\"AuthProvider.useCallback[handleLoginSuccess]\"]);\n                const userCredential = await Promise.race([\n                    signInPromise,\n                    timeoutPromise\n                ]);\n                const loggedInUser = userCredential.user;\n                console.log(\"AuthProvider: signInWithCustomToken successful. User:\", loggedInUser.uid);\n                // *** ADD LOGGING: Check auth.currentUser immediately after sign-in ***\n                console.log(`AuthProvider: auth.currentUser?.uid immediately after signInWithCustomToken: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Force token refresh to get custom claims\n                console.log(\"AuthProvider: Forcing token refresh (getIdToken(true))...\"); // Log before refresh\n                await loggedInUser.getIdToken(true);\n                console.log(\"AuthProvider: Token refresh complete.\"); // Log after refresh\n                // *** ADD LOGGING: Check auth.currentUser after token refresh ***\n                console.log(`AuthProvider: auth.currentUser?.uid after token refresh: ${_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser?.uid}`);\n                // Use provided studentId or look in localStorage - BUT DO NOT SET AS SESSION ID\n                const studentId = studentIdHint || localStorage.getItem('student_id');\n                if (studentId) {\n                    // Only store the student ID, not as a session ID\n                    localStorage.setItem('student_id', studentId);\n                    // Remove any existing incorrect session ID that might be the student ID\n                    if (localStorage.getItem('current_session') === studentId) {\n                        console.log(\"AuthProvider: Removing incorrect session ID (was set to student ID)\");\n                        localStorage.removeItem('current_session');\n                    }\n                    // Set claims via API immediately after login\n                    try {\n                        const idToken = await loggedInUser.getIdToken();\n                        const claimResponse = await fetch('/api/auth/set-student-claims', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': `Bearer ${idToken}`\n                            },\n                            body: JSON.stringify({\n                                studentId: studentId\n                            })\n                        });\n                        if (claimResponse.ok) {\n                            console.log(\"AuthProvider: Student claims set successfully\");\n                            // Force another token refresh to get the new claims\n                            await loggedInUser.getIdToken(true);\n                        } else {\n                            console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error setting student claims:\", e);\n                    }\n                }\n                // Clear any parent-related flags first\n                localStorage.removeItem('parent_id');\n                localStorage.removeItem('parent_name');\n                localStorage.removeItem('parent_role');\n                localStorage.removeItem('viewing_as_child');\n                localStorage.removeItem('is_parent');\n                // Set user role and auth state\n                localStorage.setItem('user_role', 'student');\n                setUserRole('student');\n                setIsAuthenticated(true);\n                // Clear the progress flag after successful sign-in\n                localStorage.removeItem('login_in_progress');\n                console.log(\"AuthProvider: Login successful, cleared login_in_progress flag.\");\n            } catch (err) {\n                console.error(\"AuthProvider: Error signing in with custom token:\", err);\n                let errorMessage = \"Failed to sign in with custom token.\";\n                if (err instanceof Error) {\n                    if (err.message.includes('auth/quota-exceeded')) {\n                        errorMessage = \"Authentication quota exceeded. Please try again later.\";\n                    } else if (err.message.includes('auth/invalid-custom-token')) {\n                        errorMessage = \"Invalid authentication token. Please try logging in again.\";\n                    } else if (err.message.includes('auth/custom-token-mismatch')) {\n                        errorMessage = \"Authentication token mismatch. Please try logging in again.\";\n                    } else if (err.message.includes('auth/network-request-failed')) {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear flags/storage on error\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('current_session');\n                localStorage.removeItem('login_in_progress');\n                // Re-throw the error so it can be caught by the caller\n                throw err;\n            } finally{\n                // setLoading(false); // Loading should be set to false by the onAuthStateChanged listener handling\n                console.log(\"AuthProvider: handleLoginSuccess finally block.\"); // Log finally\n            }\n        }\n    }[\"AuthProvider.useCallback[handleLoginSuccess]\"], []); // Removed auth dependency as it's globally available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log(\"AuthProvider: Setting up onAuthStateChanged listener.\"); // Log listener setup\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    // *** ADD DETAILED LOGGING for onAuthStateChanged ***\n                    const timestamp = new Date().toISOString();\n                    console.log(`AuthProvider: onAuthStateChanged fired at ${timestamp}. User object:`, user ? {\n                        uid: user.uid,\n                        email: user.email\n                    } : null);\n                    setUser(user); // Update the user state\n                    if (user) {\n                        console.log(`AuthProvider: onAuthStateChanged - User is present (UID: ${user.uid}).`);\n                        // Try to refresh token to get latest claims\n                        try {\n                            console.log(\"AuthProvider: onAuthStateChanged - Refreshing token...\");\n                            await user.getIdToken(true);\n                            console.log(\"AuthProvider: onAuthStateChanged - Token refreshed.\");\n                            // ... check claims ...\n                            const idTokenResult = await user.getIdTokenResult();\n                            console.log(\"AuthProvider: onAuthStateChanged - Token claims:\", idTokenResult.claims);\n                        // ... set role/session from claims ...\n                        } catch (e) {\n                            console.error(\"AuthProvider: onAuthStateChanged - Error refreshing token:\", e);\n                        }\n                        // Try to load from localStorage first for faster initial render\n                        const storedUserData = localStorage.getItem('user_data');\n                        if (storedUserData) {\n                            setUserData(JSON.parse(storedUserData));\n                        }\n                        const storedChildrenData = localStorage.getItem('children_data');\n                        if (storedChildrenData) {\n                            setChildrenData(JSON.parse(storedChildrenData));\n                        }\n                        // Then refresh from server\n                        console.log(\"AuthProvider: onAuthStateChanged - Calling refreshUserData...\");\n                        await refreshUserData();\n                        console.log(\"AuthProvider: onAuthStateChanged - refreshUserData complete.\");\n                        setIsAuthenticated(true);\n                        console.log(\"AuthProvider: onAuthStateChanged - Set isAuthenticated = true.\");\n                        // Only refresh children data if user is a parent\n                        if (userData?.role === 'parent' || JSON.parse(storedUserData || '{}')?.role === 'parent') {\n                            await refreshChildrenData();\n                        }\n                    } else {\n                        console.log(\"AuthProvider: onAuthStateChanged - User is null.\");\n                        // ... clear user state and localStorage ...\n                        setUserData(null);\n                        setChildrenData(null);\n                        setUserRole(null);\n                        setStudentSession(null);\n                        setIsAuthenticated(false);\n                        localStorage.removeItem('user_data');\n                        localStorage.removeItem('children_data');\n                        localStorage.removeItem('CURRENT_SESSION_KEY'); // Ensure correct key if used elsewhere\n                        localStorage.removeItem('user_role');\n                        localStorage.removeItem('student_id');\n                        console.log(\"AuthProvider: onAuthStateChanged - Cleared state and localStorage.\");\n                    }\n                    console.log(`AuthProvider: onAuthStateChanged - Setting loading = false at ${new Date().toISOString()}.`);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            // Cleanup function\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log(\"AuthProvider: Cleaning up onAuthStateChanged listener.\"); // Log cleanup\n                    unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshUserData,\n        refreshChildrenData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // Handle user authentication state\n            if (user) {\n                // If user is authenticated but no role is set, attempt to determine role\n                if (!userRole) {\n                    console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                    // Check for role in localStorage first\n                    const storedRole = localStorage.getItem('user_role');\n                    if (storedRole) {\n                        console.log(`AuthProvider: Found role in localStorage: ${storedRole}`);\n                        setUserRole(storedRole);\n                        // If this is a student, check for student ID\n                        if (storedRole === 'student') {\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID in localStorage: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            }\n                        }\n                    } else {\n                        // Check for parent indicators\n                        const isParent = localStorage.getItem('parent_id') || localStorage.getItem('parent_name');\n                        // Check for temporary student session (parent viewing student dashboard)\n                        const tempStudentSession = localStorage.getItem('temp_student_session');\n                        if (isParent) {\n                            console.log(\"AuthProvider: User appears to be a parent based on localStorage\");\n                            setUserRole('parent');\n                            // If parent is viewing a student dashboard, set the student session\n                            if (tempStudentSession) {\n                                console.log(`AuthProvider: Parent viewing student dashboard for: ${tempStudentSession}`);\n                                setStudentSession(tempStudentSession);\n                            }\n                        } else {\n                            // Default to student role if no other indicators\n                            console.log(\"AuthProvider: No role indicators found, defaulting to student\");\n                            setUserRole('student');\n                            // Try to find student ID\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(`AuthProvider: Found student ID: ${storedStudentId}`);\n                                setStudentSession(storedStudentId);\n                            } else {\n                                console.log(`AuthProvider: No student ID found, using UID: ${user.uid}`);\n                                setStudentSession(user.uid);\n                                localStorage.setItem('student_id', user.uid);\n                                localStorage.setItem('current_session', user.uid);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading auth state OR NextAuth session is loading\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Waiting for loading state or session status...\");\n                return;\n            }\n            // Determine authentication status based on Firebase OR NextAuth\n            const authenticated = !!user && !!user.uid || status === 'authenticated';\n            // Determine effective role and session for this effect run, prioritizing session if state is lagging\n            let effectiveRole = userRole;\n            let effectiveStudentSession = studentSession;\n            // Use SessionUser type for session check\n            const sessionUser = session?.user;\n            if (authenticated && !effectiveRole && status === 'authenticated' && sessionUser?.role) {\n                console.log(\"AuthProvider: Nav Effect - Using role directly from NextAuth session as state is not yet updated.\");\n                effectiveRole = sessionUser.role;\n            }\n            if (effectiveRole === 'student' && !effectiveStudentSession && status === 'authenticated' && sessionUser?.id) {\n                console.log(\"AuthProvider: Nav Effect - Using student ID directly from NextAuth session as state is not yet updated.\");\n                effectiveStudentSession = sessionUser.id;\n            }\n            console.log(\"AuthProvider: Nav Effect - Running Checks\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                authenticated,\n                user: user ? {\n                    uid: user.uid,\n                    email: user.email,\n                    provider: user.providerData?.[0]?.providerId\n                } : null,\n                sessionUser,\n                // Log component state\n                stateUserRole: userRole,\n                stateStudentSession: studentSession,\n                // Log effective values used for logic\n                effectiveRole,\n                effectiveStudentSession,\n                userDataExists: !!userData,\n                currentPath:  false ? 0 : ''\n            });\n            try {\n                // Get current path and query parameters safely\n                const currentPath =  false ? 0 : '';\n                const searchParams =  false ? 0 : new URLSearchParams();\n                const returnTo = searchParams.get('returnTo');\n                const resetParam = searchParams.get('reset') === 'true';\n                // --- DETAILED LOGGING ---\n                // console.log(\"AuthProvider: Nav Effect - Running Checks\", { // Moved up and enhanced\n                //   timestamp: new Date().toISOString(),\n                //   loading,\n                //   status,\n                //   authenticated,\n                //   user: user ? { uid: user.uid, email: user.email, provider: user.providerData?.[0]?.providerId } : null,\n                //   sessionUser,\n                //   stateUserRole: userRole, // Log state value\n                //   stateStudentSession: studentSession, // Log state value\n                //   effectiveRole, // Log derived value\n                //   effectiveStudentSession, // Log derived value\n                //   userDataExists: !!userData,\n                //   currentPath,\n                //   returnTo,\n                //   resetParam\n                // });\n                // --- END LOGGING ---\n                // Handle reset parameter first\n                if (resetParam && currentPath !== '/login') {\n                    console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                    // Clear local state immediately\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    // Clear storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Use replace to avoid adding reset=true to history\n                    router.replace('/login?reset=true');\n                    return;\n                }\n                // Use the 'authenticated' variable derived above\n                if (authenticated) {\n                    // User is authenticated (either Firebase or NextAuth)\n                    // If role or necessary session info is missing EVEN after checking session, log and wait\n                    // Use the effectiveRole and effectiveStudentSession derived above\n                    if (!effectiveRole) {\n                        console.log(\"AuthProvider: Nav Effect - User authenticated but effective role could not be determined. Waiting.\");\n                        // Potentially trigger refresh again if stuck? Or rely on initial refresh.\n                        // refreshUserData(); // Be cautious adding this here - could cause loops\n                        return; // Don't navigate yet\n                    }\n                    if (effectiveRole === 'student' && !effectiveStudentSession) {\n                        console.log(\"AuthProvider: Nav Effect - User is student but effective student session could not be determined. Waiting.\");\n                        // Potentially trigger refresh again?\n                        // refreshUserData();\n                        return; // Don't navigate yet\n                    }\n                    // --- REDIRECTION LOGIC ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Log the state being used for decision making\n                            console.log(`AuthProvider: getDestination - Effective Role: ${effectiveRole}, Effective Session: ${effectiveStudentSession}`);\n                            switch(effectiveRole){\n                                case 'parent':\n                                    return '/dashboard'; // Parent main dashboard\n                                case 'student':\n                                    // Ensure effectiveStudentSession has a value before constructing the path\n                                    if (!effectiveStudentSession) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no effective studentSession! Falling back to /login.\");\n                                        return '/login'; // Fallback to login if session missing unexpectedly\n                                    }\n                                    return `/student-dashboard/${effectiveStudentSession}`; // Use effectiveStudentSession\n                                // Add cases for teacher/admin if needed\n                                default:\n                                    console.warn(`AuthProvider: Nav Effect - Unknown or null effective role (${effectiveRole}), defaulting to /dashboard`);\n                                    return '/dashboard';\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    // Determine destination *before* checking path\n                    const destination = getDestination();\n                    // If destination calculation failed (e.g., student without session), handle it\n                    if (destination === '/login') {\n                        console.error(\"AuthProvider: Nav Effect - Calculated destination is /login even though user should be authenticated. State:\", {\n                            effectiveRole,\n                            effectiveStudentSession\n                        });\n                        // Avoid redirecting to login if authenticated but destination calculation failed\n                        // Maybe logout or show an error page?\n                        // For now, just return to prevent redirect loop\n                        return;\n                    }\n                    // 1. If user is on the login page, redirect them away\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(`AuthProvider: Nav Effect - User on login page. Redirecting to: ${redirectTarget}`);\n                        // Use replace to avoid adding login page to history after successful login\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // 2. If user is authenticated but on the WRONG page for their role, redirect them\n                    const isCorrectPath = {\n                        \"AuthProvider.useEffect.isCorrectPath\": ()=>{\n                            if (!effectiveRole) return false; // Cannot determine correctness without an effective role\n                            if (effectiveRole === 'parent') {\n                                // Parents can be on /dashboard or /student-dashboard/*\n                                return currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/');\n                            }\n                            if (effectiveRole === 'student') {\n                                // Students should be on their specific dashboard or related pages\n                                // Ensure effectiveStudentSession is checked\n                                if (!effectiveStudentSession) return false; // Cannot be correct path if session is missing\n                                return currentPath.startsWith(`/student-dashboard/${effectiveStudentSession}`) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'); // Allow base /subjects and /subjects/*\n                            }\n                            // Add logic for other roles if necessary\n                            return false; // Default to false if role is unknown\n                        }\n                    }[\"AuthProvider.useEffect.isCorrectPath\"];\n                    if (!isCorrectPath()) {\n                        console.log(`AuthProvider: Nav Effect - User on wrong page (${currentPath}) for effective role (${effectiveRole}). Redirecting to: ${destination}`);\n                        router.push(destination); // Use push here, maybe they navigated manually\n                        return;\n                    }\n                    console.log(`AuthProvider: Nav Effect - User is authenticated and on an appropriate page (${currentPath}) for effective role (${effectiveRole}).`);\n                } else {\n                    // User is not authenticated\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    // const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''; // Already defined above\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated and not on public page (${currentPath}). Redirecting to /login.`);\n                        router.push('/login');\n                        return;\n                    } else {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated, already on public page (${currentPath}). No redirect needed.`);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // If we have a user but no role, try to determine the role\n            if (user && !userRole) {\n                console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                // Check for parent indicators first\n                const isParent = localStorage.getItem('parent_id') || localStorage.getItem('is_parent') === 'true' || localStorage.getItem('user_role') === 'parent';\n                if (isParent) {\n                    console.log(\"AuthProvider: Parent indicators found in localStorage. Setting role to parent.\");\n                    setUserRole('parent');\n                    localStorage.setItem('user_role', 'parent');\n                    return;\n                }\n                // Check localStorage for role\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    console.log(`AuthProvider: Found role in localStorage: ${storedRole}`);\n                    setUserRole(storedRole); // Assuming storedRole is 'parent' or 'student'\n                    return;\n                }\n                // Check if we have a student ID in localStorage\n                const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                if (storedStudentId) {\n                    console.log(`AuthProvider: Found student ID in localStorage: ${storedStudentId}. Setting role to student.`);\n                    setUserRole('student');\n                    setStudentSession(storedStudentId); // Assuming storedStudentId is a string\n                    localStorage.setItem('user_role', 'student');\n                    return;\n                }\n                // Check if user has custom claims\n                user.getIdTokenResult(true).then({\n                    \"AuthProvider.useEffect\": (idTokenResult)=>{\n                        const claims = idTokenResult.claims;\n                        console.log(\"AuthProvider: Checking user claims:\", claims);\n                        // Ensure claims.role is treated as a string or null\n                        const claimRole = claims.role;\n                        const claimStudentId = claims.student_id;\n                        const claimParentId = claims.parent_id;\n                        if (claimRole === 'parent') {\n                            console.log(`AuthProvider: Found parent role in claims. Setting role to parent.`);\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('is_parent', 'true');\n                        } else if (claimRole) {\n                            console.log(`AuthProvider: Found role in claims: ${claimRole}`);\n                            setUserRole(claimRole);\n                            localStorage.setItem('user_role', claimRole);\n                        } else if (claimStudentId) {\n                            console.log(`AuthProvider: Found student_id in claims: ${claimStudentId}. Setting role to student.`);\n                            setUserRole('student');\n                            setStudentSession(claimStudentId);\n                            localStorage.setItem('user_role', 'student');\n                            localStorage.setItem('student_id', claimStudentId);\n                        // Avoid setting current_session directly from claims unless absolutely necessary\n                        // localStorage.setItem('current_session', claimStudentId);\n                        } else if (claimParentId) {\n                            console.log(`AuthProvider: Found parent_id in claims: ${claimParentId}. Setting role to parent.`);\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('parent_id', claimParentId);\n                            localStorage.setItem('is_parent', 'true');\n                        } else {\n                            // Check email domain for role hints\n                            const email = user.email || '';\n                            if (email.includes('parent') || email.includes('guardian')) {\n                                console.log(\"AuthProvider: Email suggests parent role. Setting role to parent.\");\n                                setUserRole('parent');\n                                localStorage.setItem('user_role', 'parent');\n                                localStorage.setItem('is_parent', 'true');\n                            } else {\n                                // Default to student role if we can't determine\n                                console.log(\"AuthProvider: No role information found. Defaulting to student role.\");\n                                setUserRole('student');\n                                localStorage.setItem('user_role', 'student');\n                            }\n                        }\n                    }\n                }[\"AuthProvider.useEffect\"]).catch({\n                    \"AuthProvider.useEffect\": (err)=>{\n                        console.error(\"AuthProvider: Error getting token claims:\", err);\n                        // Default to student role on error\n                        setUserRole('student');\n                        localStorage.setItem('user_role', 'student');\n                    }\n                }[\"AuthProvider.useEffect\"]);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    // *** NEW EFFECT: Update state based on NextAuth session ***\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Update role and session based on NextAuth session changes\n            if (status === 'authenticated' && session?.user) {\n                console.log(\"AuthProvider: NextAuth session authenticated. Updating role/session state from session:\", session.user);\n                // Use the defined SessionUser interface for type safety\n                const sessionUser = session.user;\n                const roleFromSession = sessionUser?.role;\n                const idFromSession = sessionUser?.id;\n                console.log(`AuthProvider: Extracted from session - Role: ${roleFromSession}, ID: ${idFromSession}`);\n                if (roleFromSession && roleFromSession !== userRole) {\n                    setUserRole(roleFromSession);\n                    localStorage.setItem('user_role', roleFromSession);\n                    console.log(`AuthProvider: Set userRole state to '${roleFromSession}' from session.`);\n                }\n                if (roleFromSession === 'student' && idFromSession && idFromSession !== studentSession) {\n                    setStudentSession(idFromSession);\n                    localStorage.setItem('student_id', idFromSession);\n                    if (localStorage.getItem('current_session') === idFromSession) {\n                        localStorage.removeItem('current_session');\n                    }\n                    console.log(`AuthProvider: Set studentSession state to '${idFromSession}' from session.`);\n                }\n            } else if (status === 'unauthenticated') {\n                console.log(\"AuthProvider: NextAuth session status is not 'authenticated'. Current status:\", status);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        status,\n        session,\n        userRole,\n        studentSession,\n        setUserRole,\n        setStudentSession\n    ]);\n    // Navigation Effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Add detailed logging at the start of the effect\n            console.log(\"AuthProvider: Nav Effect - START\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                user: user ? {\n                    uid: user.uid\n                } : null,\n                session: session ? {\n                    user: session.user,\n                    expires: session.expires\n                } : null,\n                stateUserRole: userRole,\n                stateStudentSession: studentSession // Log current state value\n            });\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Still loading Firebase auth or NextAuth session.\");\n                return;\n            }\n            const firebaseUserPresent = !!user && !!user.uid;\n            const nextAuthSessionAuthenticated = status === 'authenticated' && !!session?.user; // Ensure session.user exists\n            const overallAuthenticated = firebaseUserPresent || nextAuthSessionAuthenticated;\n            let derivedRole = null;\n            let derivedStudentId = null;\n            // Use SessionUser type for session check\n            const sessionUser = session?.user;\n            if (nextAuthSessionAuthenticated && sessionUser) {\n                derivedRole = sessionUser?.role || null;\n                if (derivedRole === 'student') {\n                    derivedStudentId = sessionUser?.id || null;\n                }\n                console.log(`AuthProvider: Nav Effect - Derived from NextAuth: Role='${derivedRole}', StudentID='${derivedStudentId}'`);\n            }\n            // Fallback to component state if NextAuth session didn't provide info or isn't primary\n            if (!derivedRole && userRole) {\n                derivedRole = userRole;\n                console.log(`AuthProvider: Nav Effect - Using Role from state: '${derivedRole}'`);\n            }\n            if (derivedRole === 'student' && !derivedStudentId && studentSession) {\n                derivedStudentId = studentSession;\n                console.log(`AuthProvider: Nav Effect - Using StudentID from state: '${derivedStudentId}'`);\n            }\n            // Fallback to localStorage as a last resort for role if still unknown (use with caution)\n            if (!derivedRole) {\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    derivedRole = storedRole;\n                    console.log(`AuthProvider: Nav Effect - Using Role from localStorage: '${derivedRole}'`);\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        const storedStudentId = localStorage.getItem('student_id');\n                        if (storedStudentId) {\n                            derivedStudentId = storedStudentId;\n                            console.log(`AuthProvider: Nav Effect - Using StudentID from localStorage: '${derivedStudentId}'`);\n                        }\n                    }\n                }\n            }\n            console.log(\"AuthProvider: Nav Effect - Final derived values:\", {\n                overallAuthenticated,\n                derivedRole,\n                derivedStudentId\n            });\n            // Get current path and query parameters safely\n            const currentPath =  false ? 0 : '';\n            const searchParams =  false ? 0 : new URLSearchParams();\n            const returnTo = searchParams.get('returnTo');\n            const resetParam = searchParams.get('reset') === 'true';\n            // Handle reset parameter first\n            if (resetParam && currentPath !== '/login') {\n                console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                // Clear local state immediately\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear storage\n                localStorage.clear();\n                sessionStorage.clear();\n                // Use replace to avoid adding reset=true to history\n                router.replace('/login?reset=true');\n                return;\n            }\n            try {\n                if (overallAuthenticated) {\n                    if (!derivedRole) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated, but final derived role is null. Waiting for role resolution. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session?.user\n                        });\n                        // Potentially call refreshUserData if it seems stuck, but be careful of loops.\n                        // refreshUserData(); // Might be too aggressive.\n                        return; // Wait for role to be set.\n                    }\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated as student, but final derived student ID is null. Waiting. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session?.user\n                        });\n                        return; // Wait for student ID.\n                    }\n                    // --- REDIRECTION LOGIC (using derivedRole, derivedStudentId) ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Uses derivedRole and derivedStudentId ...\n                            switch(derivedRole){\n                                case 'parent':\n                                    return '/dashboard';\n                                case 'student':\n                                    if (!derivedStudentId) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no derivedStudentId!\");\n                                        return '/login';\n                                    }\n                                    return `/student-dashboard/${derivedStudentId}`;\n                                default:\n                                    console.warn(`AuthProvider: Nav Effect - Unknown or null derived role (${derivedRole}), defaulting to /dashboard`);\n                                    return '/dashboard'; // Default destination\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    const destination = getDestination();\n                    // Ensure this logic is robust\n                    if (destination === '/login' && overallAuthenticated) {\n                        console.warn(\"AuthProvider: Nav Effect - Authenticated user is being directed to /login. This shouldn't happen. Destination calc issue or state inconsistency.\");\n                        return; // Prevent redirect to login if authenticated.\n                    }\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(`AuthProvider: Nav Effect - User on login. Redirecting to: ${redirectTarget}`);\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // Simplified isCorrectPath\n                    let onCorrectPath = false;\n                    if (derivedRole === 'parent' && (currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole === 'student' && derivedStudentId && (currentPath.startsWith(`/student-dashboard/${derivedStudentId}`) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole && ![\n                        'parent',\n                        'student'\n                    ].includes(derivedRole) && currentPath === '/dashboard') {\n                        onCorrectPath = true;\n                    }\n                    if (!onCorrectPath && destination !== currentPath) {\n                        console.log(`AuthProvider: Nav Effect - User on wrong page (${currentPath}) for role (${derivedRole}). Redirecting to: ${destination}`);\n                        router.push(destination);\n                        return;\n                    }\n                    console.log(`AuthProvider: Nav Effect - Authenticated and on appropriate page or no redirect needed.`);\n                } else {\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated overall.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(`AuthProvider: Nav Effect - Not authenticated and not on public page (${currentPath}). Redirecting to /login.`);\n                        router.push('/login');\n                    } else {\n                        console.log(`AuthProvider: Nav Effect - User not authenticated, already on public page (${currentPath}). No redirect needed.`);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Unhandled Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            userData,\n            childrenData,\n            loading,\n            error,\n            isAuthenticated: !!user && !!user.uid || status === 'authenticated',\n            // Use SessionUser type for fallbacks\n            studentSession: studentSession || (session?.user?.role === 'student' ? session?.user?.id : null),\n            userRole: userData?.role || userRole || session?.user?.role || null,\n            manualSyncWithLocalStorage,\n            refreshUserData,\n            refreshChildrenData,\n            handleLoginSuccess,\n            logout: async ()=>{\n                console.log(\"AuthProvider: Logging out...\");\n                try {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n                    // Clear backend performance caches\n                    try {\n                        const cacheResponse = await fetch('/api/clear-performance-cache', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (cacheResponse.ok) {\n                            console.log(\"AuthProvider: Backend caches cleared successfully\");\n                        } else {\n                            console.warn(\"AuthProvider: Failed to clear backend caches, but continuing with logout\");\n                        }\n                    } catch (cacheError) {\n                        console.warn(\"AuthProvider: Error clearing backend caches:\", cacheError);\n                    // Don't fail logout due to cache clearing issues\n                    }\n                    // Clear local state and storage\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setIsAuthenticated(false);\n                    // Clear all browser storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Clear IndexedDB (if any data is stored there)\n                    try {\n                        if ('indexedDB' in window) {\n                            const databases = await indexedDB.databases();\n                            databases.forEach(async (db)=>{\n                                if (db.name) {\n                                    indexedDB.deleteDatabase(db.name);\n                                }\n                            });\n                        }\n                    } catch (idbError) {\n                        console.warn(\"AuthProvider: Error clearing IndexedDB:\", idbError);\n                    }\n                    // Clear service worker caches\n                    try {\n                        if ('caches' in window) {\n                            const cacheNames = await caches.keys();\n                            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n                            console.log(\"AuthProvider: Service worker caches cleared\");\n                        }\n                    } catch (swError) {\n                        console.warn(\"AuthProvider: Error clearing service worker caches:\", swError);\n                    }\n                    console.log(\"AuthProvider: Logout successful. All caches cleared. Redirecting to login.\");\n                    router.push('/login');\n                } catch (err) {\n                    console.error(\"AuthProvider: Logout failed:\", err);\n                    setError(\"Logout failed. Please try again.\");\n                    // Still attempt to clear state/storage even if signOut fails\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setIsAuthenticated(false);\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    router.push('/login'); // Redirect even on error\n                }\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 1157,\n        columnNumber: 5\n    }, this);\n}\nconst useAuth = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper,useToast auto */ \n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nfunction ClientToastWrapper({ children }) {\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const toast = (props)=>{\n        const id = props.id || Math.random().toString(36).substr(2, 9);\n        // Add to toasts array\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    props\n                }\n            ]);\n        // Auto-dismiss after duration\n        const duration = props.duration || 5000;\n        setTimeout(()=>{\n            dismiss(id);\n        }, duration);\n        return {\n            id,\n            dismiss: ()=>dismiss(id)\n        };\n    };\n    const dismiss = (toastId)=>{\n        if (toastId) {\n            setToasts((prev)=>prev.filter((toast)=>toast.id !== toastId));\n        } else {\n            setToasts([]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map(({ id, props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n              bg-white border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] relative\n              ${props.variant === 'destructive' ? 'border-red-200 bg-red-50' : ''}\n              ${props.variant === 'success' ? 'border-green-200 bg-green-50' : ''}\n              ${props.variant === 'warning' ? 'border-yellow-200 bg-yellow-50' : ''}\n            `,\n                        children: [\n                            props.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold mb-1\",\n                                children: props.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 29\n                            }, this),\n                            props.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: props.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 35\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>dismiss(id),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n// Export the hook that components expect\nconst useToast = ()=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_1___default().useContext(ToastContext);\n    if (!context) {\n        console.error(\"useToast hook called outside of a ClientToastWrapper. Ensure ClientToastWrapper is placed correctly in your component tree (e.g., in client-providers.tsx).\");\n        throw new Error('useToast must be used within a ClientToastWrapper');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ClientToastWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/SessionProvider.tsx":
/*!***********************************************!*\
  !*** ./src/app/providers/SessionProvider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    // Always wrap children in NextAuthSessionProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU2RTtBQUU5RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUFpQztJQUNqRixrREFBa0Q7SUFDbEQscUJBQ0UsOERBQUNELDREQUF1QkE7a0JBQ3JCQzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXHNyY1xcYXBwXFxwcm92aWRlcnNcXFNlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgLy8gQWx3YXlzIHdyYXAgY2hpbGRyZW4gaW4gTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXJcclxuICByZXR1cm4gKFxyXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Theme provider with hydration-safe implementation\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light'); // Default theme\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Effect to run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setHasMounted(true); // Mark as mounted\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Effect to apply theme once mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (hasMounted) {\n                const savedTheme = localStorage.getItem('theme') || 'light';\n                setTheme(savedTheme); // Update state for potential context consumers\n                // Apply attributes directly to <html> tag\n                document.documentElement.className = savedTheme; // Set class for CSS targeting\n                document.documentElement.setAttribute('color-pick-mode', savedTheme);\n                document.documentElement.style.setProperty('color-scheme', savedTheme); // Set color-scheme style\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        hasMounted\n    ]); // Rerun if hasMounted changes (which is only once)\n    // Avoid rendering children until mounted to prevent mismatch\n    if (!hasMounted) {\n        // Render nothing or a placeholder/loader on the server and initial client render\n        // Returning children directly here could still cause mismatches if they depend on the theme\n        return null;\n    // Alternatively, render children but without theme-specific wrapper/context:\n    // return <>{children}</>;\n    }\n    // Once mounted, render children. The theme is applied via useEffect directly to documentElement.\n    // No need for a wrapper div with data-theme here.\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props), this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        // Check if it's a ChunkLoadError\n        if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {\n            console.log('ChunkLoadError detected, attempting to reload...');\n            // Reload the page to recover from chunk load errors\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1000);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 45,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, retry }) {\n    const isChunkError = error?.name === 'ChunkLoadError' || error?.message.includes('Loading chunk');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 mx-auto\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.734-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: isChunkError ? 'Loading Error' : 'Something went wrong'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: isChunkError ? 'The application is updating. Please wait while we reload...' : 'An unexpected error occurred. Please try again.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                isChunkError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: retry,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this),\n                error && !isChunkError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500\",\n                            children: \"Error Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-gray-600 overflow-auto\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSessionSimple.tsx":
/*!****************************************!*\
  !*** ./src/hooks/useSessionSimple.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authService */ \"(ssr)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionContext auto */ \n // Import real auth service\n// Simplified session context for the frontend diagnostic fix\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useSession() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SessionContext);\n    if (context === undefined) {\n        // Instead of throwing an error immediately, return a fallback object\n        console.warn('useSession called outside of SessionProvider, returning fallback values');\n        return {\n            backendSessionId: null,\n            user: null,\n            setUserSession: ()=>{},\n            setBackendSessionId: ()=>{},\n            clearSession: ()=>{},\n            isReady: false,\n            isLoading: true,\n            getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_1__.getAuthHeaders)(null),\n            userRole: null\n        };\n    }\n    return context;\n}\n// Export the context for providers to use\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSessionSimple.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? `User ${user.uid}` : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser?.getIdToken(true, additionalClaims);\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || userDetails?.name || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails?.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = currentUser.stsTokenManager?.accessToken || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || storedSession?.uid;\n        if (effectiveUid) {\n            console.warn(`Using UID (${effectiveUid}) as Session-ID header fallback. Backend session ID not found in context or localStorage ('${CURRENT_SESSION_KEY}').`);\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || storedSession?.token;\n    if (effectiveToken) {\n        headers['Authorization'] = `Bearer ${effectiveToken}`;\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = `Bearer ${fallbackToken}`;\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = storedSession?.role || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    // CRITICAL FIX: Add testing mode header when no valid authentication is available\n    // This allows backend to generate console logs for lesson interactions during development\n    if (!effectiveToken || effectiveToken === 'undefined' || effectiveToken === 'null') {\n        console.warn(\"No valid authentication token found - enabling testing mode for backend logging\");\n        headers['X-Testing-Mode'] = 'true';\n    }\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = `Bearer ${token}`;\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(`Using UID (${currentUser.uid}) as Session-ID header fallback during fresh token request. Backend session ID not found.`);\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = storedSession?.role || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers with testing mode\n        const fallbackHeaders = getAuthHeaders(backendSessionIdFromContext);\n        fallbackHeaders['X-Testing-Mode'] = 'true';\n        console.warn(\"No current user found - enabling testing mode for backend logging\");\n        return fallbackHeaders;\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        const newToken = await currentUser.getIdToken(true);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || userDetails?.name || null,\n            token: token,\n            role: userDetails?.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (false) {} else {\n    // Provide non-functional placeholders for SSR/server environments\n    // This prevents errors during import but these services won't work server-side\n    // Assigning {} as Type might cause issues if methods are called server-side.\n    // A more robust approach might involve providing mock implementations or\n    // ensuring these exports are only used in client components.\n    auth = {};\n    db = {};\n    storage = {};\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/@firebase","vendor-chunks/protobufjs","vendor-chunks/@babel","vendor-chunks/@protobufjs","vendor-chunks/@react-aria","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/framer-motion","vendor-chunks/firebase","vendor-chunks/@nextui-org","vendor-chunks/idb","vendor-chunks/@grpc","vendor-chunks/tslib","vendor-chunks/next-themes","vendor-chunks/motion-utils","vendor-chunks/long","vendor-chunks/lodash.camelcase"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();