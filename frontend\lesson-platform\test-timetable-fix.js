const https = require('https');
const http = require('http');

async function testTimetableFix() {
  console.log('🧪 Testing Timetable Generation Fix...\n');
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/timetable?studentId=andrea_ugono_33305&week=1',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  console.log('📡 Making request to:', `http://${options.hostname}:${options.port}${options.path}`);
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (response.success) {
            console.log('✅ API Response successful');
            console.log('📊 Total lessons:', response.data.totalLessons);
            console.log('📚 Enrollments found:', response.data.enrollments.length);
            
            console.log('\n📋 Enrollment Details:');
            let totalExpected = 0;
            response.data.enrollments.forEach(enrollment => {
              console.log(`  - ${enrollment.subjectName}: ${enrollment.lessonsPerWeek} lessons/week`);
              totalExpected += enrollment.lessonsPerWeek;
            });
            
            console.log('\n📈 Expected total lessons per week:', totalExpected);
            console.log('📉 Actual lessons generated:', response.data.totalLessons);
            console.log('🎯 Target (35 slots): 35');
            
            console.log('\n🔍 Sample lesson references:');
            response.data.schedule.slice(0, 10).forEach(lesson => {
              console.log(`  - ${lesson.lessonReference} (${lesson.subject})`);
            });
            
            // Check lesson reference format
            console.log('\n🔍 Lesson Reference Format Check:');
            const sampleLesson = response.data.schedule[0];
            if (sampleLesson.lessonReference.startsWith('P5-')) {
              console.log('✅ Lesson reference format is correct (P5-XXX-001)');
            } else {
              console.log('❌ Lesson reference format is incorrect:', sampleLesson.lessonReference);
            }

            // Check Project-Based Excellence subject code mapping
            console.log('\n🔍 Subject Code Mapping Check:');
            const pbeLesson = response.data.schedule.find(l => l.subject === 'Project-Based Excellence');
            if (pbeLesson) {
              if (pbeLesson.subjectCode === 'PBE') {
                console.log('✅ Project-Based Excellence correctly maps to PBE');
              } else {
                console.log('❌ Project-Based Excellence incorrectly maps to:', pbeLesson.subjectCode);
              }
            } else {
              console.log('⚠️  Project-Based Excellence not found in schedule');
            }

            // Check Mathematics lesson numbering sequence
            console.log('\n🔍 Mathematics Lesson Numbering Check:');
            const mathLessons = response.data.schedule
              .filter(l => l.subject === 'Mathematics')
              .sort((a, b) => {
                const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
                const dayA = dayOrder.indexOf(a.day);
                const dayB = dayOrder.indexOf(b.day);
                if (dayA !== dayB) return dayA - dayB;
                return a.time.localeCompare(b.time);
              });

            console.log('Mathematics lessons in chronological order:');
            mathLessons.forEach((lesson, index) => {
              const expectedNumber = index + 1;
              const actualNumber = parseInt(lesson.lessonReference.split('-')[2]);
              const status = actualNumber === expectedNumber ? '✅' : '❌';
              console.log(`  ${status} ${lesson.day} ${lesson.time}: ${lesson.lessonReference} (expected lesson ${expectedNumber})`);
            });

            // Check free period reference format
            console.log('\n🔍 Free Period Reference Check:');
            const freePeriod = response.data.schedule.find(l => l.isFreePeriod);
            if (freePeriod) {
              if (freePeriod.lessonReference.includes('-FREE-')) {
                console.log('❌ Free period still contains subject code:', freePeriod.lessonReference);
              } else {
                console.log('✅ Free period reference simplified:', freePeriod.lessonReference);
              }
            } else {
              console.log('ℹ️  No free periods found in schedule');
            }

            // Check lesson status calculation
            console.log('\n🔍 Lesson Status Check:');
            const statusCounts = {};
            response.data.schedule.forEach(lesson => {
              if (lesson.status) {
                statusCounts[lesson.status] = (statusCounts[lesson.status] || 0) + 1;
              }
            });

            console.log('Status distribution:');
            Object.entries(statusCounts).forEach(([status, count]) => {
              console.log(`  - ${status}: ${count} lessons`);
            });

            // Check if all lessons default to "upcoming"
            const nonFreePeriodLessons = response.data.schedule.filter(l => !l.isFreePeriod);
            const upcomingLessons = nonFreePeriodLessons.filter(l => l.status === 'upcoming');
            const completedLessons = nonFreePeriodLessons.filter(l => l.status === 'completed');
            const inProgressLessons = nonFreePeriodLessons.filter(l => l.status === 'in_progress');

            console.log(`\n📊 Lesson Status Summary:`);
            console.log(`  - Upcoming: ${upcomingLessons.length}/${nonFreePeriodLessons.length} lessons`);
            console.log(`  - Completed: ${completedLessons.length}/${nonFreePeriodLessons.length} lessons`);
            console.log(`  - In Progress: ${inProgressLessons.length}/${nonFreePeriodLessons.length} lessons`);

            if (upcomingLessons.length === nonFreePeriodLessons.length) {
              console.log('✅ All lessons correctly default to "upcoming" status');
            } else if (completedLessons.length > 0 || inProgressLessons.length > 0) {
              console.log('✅ Lesson completion states found from Firestore');
              console.log('Sample completed lesson:', completedLessons[0]?.lessonReference || 'None');
            } else {
              console.log('⚠️  Some lessons have unexpected status values');
            }
            
            // Check if all 35 slots are filled
            console.log('\n📊 Slot Coverage Analysis:');
            if (response.data.totalLessons === 35) {
              console.log('🎉 SUCCESS: All 35 time slots are filled!');
            } else {
              console.log(`⚠️  ISSUE: Only ${response.data.totalLessons} out of 35 slots filled`);
            }
            
            // Count lessons by subject
            console.log('\n📈 Lessons by Subject:');
            const subjectCounts = {};
            response.data.schedule.forEach(lesson => {
              if (lesson.subjectId && lesson.subjectId !== 'free_period') {
                subjectCounts[lesson.subject] = (subjectCounts[lesson.subject] || 0) + 1;
              }
            });
            
            Object.entries(subjectCounts).forEach(([subject, count]) => {
              console.log(`  - ${subject}: ${count} lessons`);
            });
            
            const freePeriods = response.data.schedule.filter(l => l.subjectId === 'free_period').length;
            if (freePeriods > 0) {
              console.log(`  - Free Periods: ${freePeriods} slots`);
            }
            
            resolve(response);
          } else {
            console.error('❌ API Error:', response.error);
            reject(new Error(response.error));
          }
        } catch (error) {
          console.error('❌ JSON Parse Error:', error.message);
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request Error:', error.message);
      reject(error);
    });
    
    req.end();
  });
}

// Run the test
testTimetableFix()
  .then(() => {
    console.log('\n🎉 Test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  });
