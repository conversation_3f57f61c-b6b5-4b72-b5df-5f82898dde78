/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/login/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(app-pages-browser)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXFKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxwY1xcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXERlc2t0b3BcXFxcU29seW50YV9XZWJzaXRlXFxcXGZyb250ZW5kXFxcXGxlc3Nvbi1wbGF0Zm9ybVxcXFxzcmNcXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../providers/AuthProvider */ \"(app-pages-browser)/./src/app/providers/AuthProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n // Import useAuth\nfunction LoginPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentId, setStudentId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [studentPassword, setStudentPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { handleLoginSuccess } = (0,_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth)(); // Get handleLoginSuccess from context\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleParentLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            // 1. Firebase Sign in\n            const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, email, password);\n            const firebaseUser = userCredential.user;\n            if (firebaseUser) {\n                // 2. Get Firebase ID Token\n                const idToken = await firebaseUser.getIdToken();\n                // 3. Sign in to NextAuth using the token\n                const nextAuthResponse = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('credentials', {\n                    redirect: false,\n                    firebaseToken: idToken\n                });\n                if (nextAuthResponse === null || nextAuthResponse === void 0 ? void 0 : nextAuthResponse.error) {\n                    console.error(\"NextAuth sign-in error:\", nextAuthResponse.error);\n                    setError('Failed to establish application session. Please try again.');\n                    // Optional: Sign out from Firebase if NextAuth fails\n                    // await signOut(auth);\n                    setLoading(false);\n                    return; // Stop execution\n                } else {\n                    console.log(\"NextAuth sign-in successful for parent:\", nextAuthResponse);\n                    // 4. Redirect on successful NextAuth sign-in\n                    router.push('/dashboard');\n                }\n            }\n        } catch (err) {\n            setError('Invalid email or password');\n        } finally{\n        // setLoading(false); // setLoading(false) is handled within the success/error paths now\n        }\n    };\n    const handleStudentLogin = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            console.log('Attempting student login with ID:', studentId);\n            // Step 1: Call backend API to authenticate student and get custom token\n            const response = await fetch('/api/auth/student-login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    studentId,\n                    password: studentPassword\n                })\n            });\n            const data = await response.json();\n            if (!response.ok || !data.success || !data.token) {\n                throw new Error(data.message || 'Student login failed or token missing');\n            }\n            console.log('Received token from backend - length:', data.token.length);\n            console.log('Token starts with:', data.token.substring(0, 50));\n            console.log('Token ends with:', data.token.substring(data.token.length - 50));\n            // Step 2: Use NextAuth credentials provider to establish session\n            const nextAuthResponse = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.signIn)('credentials', {\n                redirect: false,\n                studentId,\n                studentPassword\n            });\n            if (nextAuthResponse === null || nextAuthResponse === void 0 ? void 0 : nextAuthResponse.error) {\n                console.error(\"NextAuth sign-in error:\", nextAuthResponse.error);\n                setError('Failed to establish application session. Please try again.');\n                setLoading(false);\n                return;\n            } // Step 3: Call AuthProvider's handleLoginSuccess with the custom token\n            // This signs in the Firebase client SDK\n            console.log(\"NextAuth sign-in successful, calling handleLoginSuccess for Firebase client...\");\n            try {\n                await handleLoginSuccess(data.token, studentId); // Pass token and studentId hint\n                console.log(\"Firebase client sign-in initiated via handleLoginSuccess. AuthProvider will redirect.\");\n            } catch (firebaseError) {\n                console.error(\"Firebase sign-in error:\", firebaseError);\n                // If Firebase sign-in fails, we still have a valid NextAuth session\n                // So we can redirect to dashboard instead of showing an error\n                console.log(\"Firebase sign-in failed, but NextAuth session is valid. Redirecting to dashboard...\");\n                router.push('/dashboard');\n                return;\n            }\n        // setLoading(false); // Keep loading until redirect happens via AuthProvider's effect\n        } catch (err) {\n            console.error(\"Student login error:\", err); // Log the actual error\n            setError(err.message || 'An error occurred during student login.');\n            setLoading(false); // Set loading false on error\n        } finally{\n        // setLoading(false); // Removed from here\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"mt-6 text-center text-3xl font-extrabold text-gray-900\",\n                    children: \"Sign in to your account\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 bg-red-50 border-l-4 border-red-500 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleParentLogin,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-center text-2xl font-extrabold text-gray-900\",\n                                    children: \"Parent Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing in...\"\n                                            ]\n                                        }, void 0, true) : 'Sign in'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full border-t border-gray-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex justify-center text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 bg-white text-gray-500\",\n                                            children: \"Or\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"mt-6 space-y-6\",\n                            onSubmit: handleStudentLogin,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-center text-2xl font-extrabold text-gray-900\",\n                                    children: \"Student Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"studentId\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Student ID\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"studentId\",\n                                                name: \"studentId\",\n                                                type: \"text\" // Or 'number' if appropriate\n                                                ,\n                                                autoComplete: \"username\" // Use 'username' to potentially leverage browser autofill for IDs\n                                                ,\n                                                required: true,\n                                                value: studentId,\n                                                onChange: (e)=>setStudentId(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"student-password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"student-password\",\n                                                name: \"student-password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: studentPassword,\n                                                onChange: (e)=>setStudentPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing in...\"\n                                            ]\n                                        }, void 0, true) : 'Sign in'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"T38nx1z3n9shQ15QFZ1El8zXIMk=\", false, function() {\n    return [\n        _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/login/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);