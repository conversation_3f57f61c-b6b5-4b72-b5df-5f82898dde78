/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["common"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false!":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false! ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {



/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/AuthProvider.tsx":
/*!********************************************!*\
  !*** ./src/app/providers/AuthProvider.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isAuthenticated: false,\n    studentSession: null,\n    userRole: null,\n    manualSyncWithLocalStorage: ()=>{},\n    user: null,\n    userData: null,\n    childrenData: null,\n    loading: true,\n    error: null,\n    refreshUserData: async ()=>{},\n    refreshChildrenData: async ()=>{},\n    handleLoginSuccess: async ()=>{},\n    logout: async ()=>{}\n});\nfunction AuthProvider(param) {\n    let { children } = param;\n    var _session_user, _session_user1, _session_user2;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childrenData, setChildrenData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [studentSession, setStudentSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession)(); // session will be of type Session | null\n    console.log('AuthProvider: Initial state', {\n        loading,\n        isAuthenticated,\n        userRole,\n        session\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const manualSyncWithLocalStorage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[manualSyncWithLocalStorage]\": ()=>{\n            const storedUserData = localStorage.getItem('user_data');\n            if (storedUserData) {\n                setUserData(JSON.parse(storedUserData));\n            }\n            const storedChildrenData = localStorage.getItem('children_data');\n            if (storedChildrenData) {\n                setChildrenData(JSON.parse(storedChildrenData));\n            }\n        }\n    }[\"AuthProvider.useCallback[manualSyncWithLocalStorage]\"], []);\n    const refreshUserData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshUserData]\": async ()=>{\n            var _user_providerData_, _user_providerData;\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) {\n                console.log(\"AuthProvider: refreshUserData - No user.uid, exiting.\");\n                setUserData(null); // Ensure data is cleared if no user\n                setUserRole(null);\n                return;\n            }\n            console.log(\"AuthProvider: Refreshing user data for UID: \".concat(user.uid));\n            setError(null); // Clear previous errors\n            // Determine the most likely role based on login method and localStorage hints\n            // This is an initial guess before confirming with fetched data\n            let likelyRole = localStorage.getItem('user_role') || null;\n            const loginMethodHint = (_user_providerData = user.providerData) === null || _user_providerData === void 0 ? void 0 : (_user_providerData_ = _user_providerData[0]) === null || _user_providerData_ === void 0 ? void 0 : _user_providerData_.providerId; // 'password', 'google.com', 'custom'\n            console.log(\"AuthProvider: refreshUserData - Login method hint: \".concat(loginMethodHint, \", localStorage role hint: \").concat(likelyRole));\n            if (!likelyRole) {\n                if (loginMethodHint === 'custom') {\n                    likelyRole = 'student'; // Custom tokens are used for student logins in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'student' based on custom token login.\");\n                } else if (loginMethodHint === 'password' || loginMethodHint === 'google.com') {\n                    likelyRole = 'parent'; // Email/Google logins are parents in your setup\n                    console.log(\"AuthProvider: refreshUserData - Guessed role 'parent' based on email/Google login.\");\n                } else {\n                    console.log(\"AuthProvider: refreshUserData - Could not reliably guess initial role.\");\n                    // Proceed cautiously, maybe try student first as it seems more common? Or try both carefully.\n                    // Let's try student first for safety in this scenario.\n                    likelyRole = 'student';\n                }\n            }\n            try {\n                // --- Logic based on likely role ---\n                let roleConfirmed = false;\n                // Try fetching data based on the likely role first\n                if (likelyRole === 'parent') {\n                    console.log(\"AuthProvider: refreshUserData - Prioritizing parent fetch.\");\n                    // 1. Try Parent Dashboard Endpoint\n                    try {\n                        const dashboardResponse = await fetch(\"/api/parent/dashboard?parentId=\".concat(user.uid));\n                        if (dashboardResponse.ok) {\n                            var _dashboardData_data;\n                            const dashboardData = await dashboardResponse.json();\n                            console.log(\"AuthProvider: Parent dashboard response:\", dashboardData);\n                            if (dashboardData.success && ((_dashboardData_data = dashboardData.data) === null || _dashboardData_data === void 0 ? void 0 : _dashboardData_data.parent)) {\n                                console.log(\"AuthProvider: Parent data found via dashboard.\");\n                                const parentData = {\n                                    ...dashboardData.data.parent,\n                                    uid: user.uid,\n                                    role: 'parent'\n                                };\n                                setUserData(parentData);\n                                localStorage.setItem('user_data', JSON.stringify(parentData));\n                                setUserRole('parent'); // Confirm role\n                                console.log(\"AuthProvider: Set user data/role to parent (dashboard).\");\n                                roleConfirmed = true;\n                                return; // Success\n                            } else {\n                                console.log(\"AuthProvider: Parent dashboard endpoint didn't provide valid parent data. Will try token verification.\");\n                            }\n                        } else {\n                            console.warn(\"AuthProvider: Parent dashboard fetch failed (\".concat(dashboardResponse.status, \"). Will try token verification.\"));\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error with parent dashboard fetch:\", e);\n                    }\n                    // 2. Fallback: Verify Firebase Token (for parents)\n                    if (!roleConfirmed) {\n                        try {\n                            console.log(\"AuthProvider: Attempting parent fallback: verify-firebase-token\");\n                            const token = await user.getIdToken(true);\n                            const verifyResponse = await fetch('/api/auth/verify-firebase-token', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    token\n                                })\n                            });\n                            if (verifyResponse.ok) {\n                                const verifyData = await verifyResponse.json();\n                                console.log(\"AuthProvider: verify-firebase-token response:\", verifyData);\n                                if (verifyData.success && verifyData.user) {\n                                    console.log(\"AuthProvider: Parent data found via token verification.\");\n                                    const parentData = {\n                                        ...verifyData.user,\n                                        uid: user.uid,\n                                        role: 'parent'\n                                    };\n                                    setUserData(parentData);\n                                    localStorage.setItem('user_data', JSON.stringify(parentData));\n                                    setUserRole('parent'); // Confirm role\n                                    console.log(\"AuthProvider: Set user data/role to parent (token verify).\");\n                                    roleConfirmed = true;\n                                    return; // Success\n                                } else {\n                                    console.log(\"AuthProvider: Token verification failed to provide parent data.\");\n                                }\n                            } else {\n                                console.warn(\"AuthProvider: verify-firebase-token fetch failed (\".concat(verifyResponse.status, \").\"));\n                            }\n                        } catch (e) {\n                            console.error(\"AuthProvider: Error with token verification:\", e);\n                        }\n                    }\n                }\n                // If parent fetches failed or role is student, try student fetch\n                if (!roleConfirmed) {\n                    console.log(\"AuthProvider: refreshUserData - Trying student fetch.\");\n                    try {\n                        const studentIdForFetch = localStorage.getItem('student_id') || user.uid; // Use consistent variable\n                        console.log(\"AuthProvider: Fetching student data with studentId: \".concat(studentIdForFetch));\n                        const studentResponse = await fetch(\"/api/student-data?studentId=\".concat(studentIdForFetch));\n                        // --- ADD ROBUST RESPONSE HANDLING ---\n                        if (!studentResponse.ok) {\n                            const errorText = await studentResponse.text(); // Get raw error text\n                            console.error(\"AuthProvider: API call to /student-data failed with status \".concat(studentResponse.status, \". Response: \").concat(errorText));\n                            // Try to parse as JSON only if content-type suggests it, otherwise use text\n                            let backendError = \"Failed to fetch student data (status: \".concat(studentResponse.status, \")\");\n                            try {\n                                var _studentResponse_headers_get;\n                                if ((_studentResponse_headers_get = studentResponse.headers.get(\"content-type\")) === null || _studentResponse_headers_get === void 0 ? void 0 : _studentResponse_headers_get.includes(\"application/json\")) {\n                                    const errorJson = JSON.parse(errorText); // This might fail if errorText is HTML\n                                    backendError = errorJson.error || errorJson.message || backendError;\n                                } else if (errorText.length > 0 && errorText.length < 300) {\n                                    backendError = errorText;\n                                }\n                            } catch (parseError) {\n                                console.warn(\"AuthProvider: Could not parse error response from /student-data as JSON. Raw text was:\", errorText);\n                            }\n                            throw new Error(backendError);\n                        }\n                        // --- END ROBUST RESPONSE HANDLING ---\n                        // If response.ok is true, then try to parse JSON\n                        const studentData = await studentResponse.json(); // This is where the SyntaxError was happening\n                        console.log(\"AuthProvider: Student data API response received:\", studentData);\n                        if (studentData.success && studentData.student) {\n                            const studentInfo = studentData.student;\n                            console.log(\"AuthProvider: Student data found and valid.\");\n                            // ... (rest of your logic for successful student data) ...\n                            // Add code to set claims if not already set\n                            try {\n                                const idTokenResult = await user.getIdTokenResult();\n                                const hasCorrectClaims = idTokenResult.claims.role === 'student' && idTokenResult.claims.student_id === studentInfo.id;\n                                if (!hasCorrectClaims) {\n                                    console.log(\"AuthProvider: Student claims missing or incorrect, setting them now\");\n                                    const idToken = await user.getIdToken();\n                                    const claimResponse = await fetch('/api/auth/set-student-claims', {\n                                        method: 'POST',\n                                        headers: {\n                                            'Content-Type': 'application/json',\n                                            'Authorization': \"Bearer \".concat(idToken)\n                                        },\n                                        body: JSON.stringify({\n                                            studentId: studentInfo.id\n                                        })\n                                    });\n                                    if (claimResponse.ok) {\n                                        console.log(\"AuthProvider: Student claims set successfully\");\n                                        // Force token refresh to get the new claims\n                                        await user.getIdToken(true);\n                                    } else {\n                                        console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                                    }\n                                }\n                            } catch (e) {\n                                console.error(\"AuthProvider: Error checking/setting student claims:\", e);\n                            }\n                            const studentUserData = {\n                                ...studentInfo,\n                                role: 'student',\n                                enrollments: studentData.enrollments || [],\n                                timetable: studentData.timetable || null\n                            };\n                            const studentId = studentInfo.id;\n                            setUserData(studentUserData);\n                            localStorage.setItem('user_data', JSON.stringify(studentUserData));\n                            setUserRole('student');\n                            setStudentSession(studentId);\n                            localStorage.setItem('student_id', studentId);\n                            if (localStorage.getItem('current_session') === studentId) {\n                                localStorage.removeItem('current_session');\n                            }\n                            localStorage.setItem('user_role', 'student');\n                            console.log(\"AuthProvider: Set user data/role to student. Student ID: \".concat(studentId));\n                            roleConfirmed = true; // Make sure to set this on success\n                            return; // Exit refreshUserData successfully\n                        } else {\n                            console.warn(\"AuthProvider: Student data fetch was 'ok' but API reported not success or missing data. Message: \".concat((studentData === null || studentData === void 0 ? void 0 : studentData.error) || (studentData === null || studentData === void 0 ? void 0 : studentData.message)));\n                            throw new Error((studentData === null || studentData === void 0 ? void 0 : studentData.error) || (studentData === null || studentData === void 0 ? void 0 : studentData.message) || 'Student data fetch failed (API level).');\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error block for student data fetch:\", e);\n                        // If we are here, it means student fetch failed.\n                        // The specific error (e.g., SyntaxError, or the error thrown from !response.ok) will be 'e'.\n                        // You might want to set a specific error or attempt recovery.\n                        // The outer catch block will handle setting the general error state.\n                        throw e; // Re-throw to be handled by the main catch in refreshUserData\n                    }\n                }\n                // If we get here without returning, no role was confirmed\n                if (!roleConfirmed) {\n                    throw new Error('Could not determine user role. Please try logging in again.');\n                }\n            } catch (err) {\n                console.error('AuthProvider: Error during refreshUserData:', err);\n                // Provide a specific error message based on the type of error if possible\n                let specificErrorMessage = 'Failed to refresh user data. Please try logging in again.';\n                if (err instanceof Error) {\n                    specificErrorMessage = \"Failed to refresh user data: \".concat(err.message);\n                    if (err.message.includes('Failed to fetch valid student data')) {\n                        // Attempt recovery logic as you have\n                        const studentIdHint = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                        if (studentIdHint) {\n                            // your recovery logic ...\n                            console.log(\"AuthProvider: Recovering with minimal student data using ID: \".concat(studentIdHint));\n                            const minimalStudentData = {\n                                id: studentIdHint,\n                                uid: user.uid,\n                                role: 'student',\n                                name: localStorage.getItem('student_name') || 'Student',\n                                recoveryMode: true\n                            };\n                            setUserData(minimalStudentData);\n                            setUserRole('student');\n                            setStudentSession(studentIdHint);\n                            localStorage.setItem('user_data', JSON.stringify(minimalStudentData));\n                            // If recovery is successful, set state and RETURN here to avoid setting generic error.\n                            console.log(\"AuthProvider: Recovered with minimal student data.\");\n                            setError(null); // Clear any previous errors if recovery was successful\n                            return;\n                        } else {\n                            specificErrorMessage = \"Could not retrieve critical student information. Please re-login.\";\n                        }\n                    } else if (err.message.includes('Could not determine user role')) {\n                        specificErrorMessage = \"Could not confirm your user role. Please try logging in again.\";\n                    }\n                }\n                setError(specificErrorMessage); // Set a user-friendly error message\n                // Clear sensitive/stale data on critical refresh failure\n                setUserData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                localStorage.removeItem('user_data');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('current_session'); // and other relevant keys\n                setIsAuthenticated(false); // Consider if a refresh failure means unauthenticated\n            // setLoading(false); // Ensure loading is false if refresh fails - This is handled by onAuthStateChanged\n            // Consider a more drastic action like redirecting to login if refresh fails consistently\n            // router.push('/login?error=session_refresh_failed');\n            }\n        // setLoading(false); // Ensure loading is set to false at the end of the function\n        // This is now handled by onAuthStateChanged's setLoading(false)\n        }\n    }[\"AuthProvider.useCallback[refreshUserData]\"], [\n        user\n    ]);\n    const refreshChildrenData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[refreshChildrenData]\": async ()=>{\n            if (!(user === null || user === void 0 ? void 0 : user.uid)) return;\n            try {\n                // First try the parent dashboard endpoint which includes all child data\n                const dashboardResponse = await fetch(\"/api/parent/dashboard?parentId=\".concat(user.uid));\n                const dashboardData = await dashboardResponse.json();\n                if (dashboardData.success) {\n                    const children = dashboardData.data.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                    return;\n                }\n                // Fallback to get-children endpoint if dashboard fails\n                const childrenResponse = await fetch(\"/api/parent/get-children?parentId=\".concat(user.uid));\n                const childrenData = await childrenResponse.json();\n                if (childrenData.success) {\n                    const children = childrenData.children || [];\n                    setChildrenData(children);\n                    localStorage.setItem('children_data', JSON.stringify(children));\n                    // Also update Firestore to ensure consistency\n                    if (children.length > 0) {\n                        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, 'users', user.uid);\n                        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_5__.updateDoc)(parentRef, {\n                            children: children.map({\n                                \"AuthProvider.useCallback[refreshChildrenData]\": (child)=>child.id\n                            }[\"AuthProvider.useCallback[refreshChildrenData]\"])\n                        });\n                    }\n                } else {\n                    setError(childrenData.error || 'Failed to fetch children data');\n                }\n            } catch (err) {\n                console.error('Error fetching children data:', err);\n                setError('Failed to fetch children data');\n            }\n        }\n    }[\"AuthProvider.useCallback[refreshChildrenData]\"], [\n        user\n    ]);\n    const handleLoginSuccess = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[handleLoginSuccess]\": async (customToken, studentIdHint)=>{\n            // *** REMOVE ALERT ***\n            // alert(\"AuthProvider: handleLoginSuccess CALLED!\"); \n            // *** END REMOVE ALERT ***\n            console.log(\"AuthProvider: handleLoginSuccess called.\"); // Log start\n            setLoading(true);\n            setError(null);\n            try {\n                var _auth_currentUser, _auth_currentUser1;\n                // Validate token format\n                if (!customToken || typeof customToken !== 'string') {\n                    throw new Error('Invalid token format: Token is missing or not a string.');\n                }\n                if (!customToken.includes('.')) {\n                    throw new Error('Invalid token format: Token does not appear to be a valid JWT.');\n                }\n                if (customToken.length < 50) {\n                    throw new Error('Invalid token format: Token is too short to be valid.');\n                } // Sign in using the custom token with timeout\n                console.log(\"AuthProvider: Calling signInWithCustomToken...\"); // Log before call\n                const signInPromise = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithCustomToken)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, customToken);\n                const timeoutPromise = new Promise({\n                    \"AuthProvider.useCallback[handleLoginSuccess]\": (_, reject)=>setTimeout({\n                            \"AuthProvider.useCallback[handleLoginSuccess]\": ()=>reject(new Error('Sign-in timeout: The operation took too long to complete'))\n                        }[\"AuthProvider.useCallback[handleLoginSuccess]\"], 30000)\n                }[\"AuthProvider.useCallback[handleLoginSuccess]\"]);\n                const userCredential = await Promise.race([\n                    signInPromise,\n                    timeoutPromise\n                ]);\n                const loggedInUser = userCredential.user;\n                console.log(\"AuthProvider: signInWithCustomToken successful. User:\", loggedInUser.uid);\n                // *** ADD LOGGING: Check auth.currentUser immediately after sign-in ***\n                console.log(\"AuthProvider: auth.currentUser?.uid immediately after signInWithCustomToken: \".concat((_auth_currentUser = _lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.uid));\n                // Force token refresh to get custom claims\n                console.log(\"AuthProvider: Forcing token refresh (getIdToken(true))...\"); // Log before refresh\n                await loggedInUser.getIdToken(true);\n                console.log(\"AuthProvider: Token refresh complete.\"); // Log after refresh\n                // *** ADD LOGGING: Check auth.currentUser after token refresh ***\n                console.log(\"AuthProvider: auth.currentUser?.uid after token refresh: \".concat((_auth_currentUser1 = _lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth.currentUser) === null || _auth_currentUser1 === void 0 ? void 0 : _auth_currentUser1.uid));\n                // Use provided studentId or look in localStorage - BUT DO NOT SET AS SESSION ID\n                const studentId = studentIdHint || localStorage.getItem('student_id');\n                if (studentId) {\n                    // Only store the student ID, not as a session ID\n                    localStorage.setItem('student_id', studentId);\n                    // Remove any existing incorrect session ID that might be the student ID\n                    if (localStorage.getItem('current_session') === studentId) {\n                        console.log(\"AuthProvider: Removing incorrect session ID (was set to student ID)\");\n                        localStorage.removeItem('current_session');\n                    }\n                    // Set claims via API immediately after login\n                    try {\n                        const idToken = await loggedInUser.getIdToken();\n                        const claimResponse = await fetch('/api/auth/set-student-claims', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'Authorization': \"Bearer \".concat(idToken)\n                            },\n                            body: JSON.stringify({\n                                studentId: studentId\n                            })\n                        });\n                        if (claimResponse.ok) {\n                            console.log(\"AuthProvider: Student claims set successfully\");\n                            // Force another token refresh to get the new claims\n                            await loggedInUser.getIdToken(true);\n                        } else {\n                            console.error(\"AuthProvider: Failed to set student claims:\", await claimResponse.json());\n                        }\n                    } catch (e) {\n                        console.error(\"AuthProvider: Error setting student claims:\", e);\n                    }\n                }\n                // Clear any parent-related flags first\n                localStorage.removeItem('parent_id');\n                localStorage.removeItem('parent_name');\n                localStorage.removeItem('parent_role');\n                localStorage.removeItem('viewing_as_child');\n                localStorage.removeItem('is_parent');\n                // Set user role and auth state\n                localStorage.setItem('user_role', 'student');\n                setUserRole('student');\n                setIsAuthenticated(true);\n                // Clear the progress flag after successful sign-in\n                localStorage.removeItem('login_in_progress');\n                console.log(\"AuthProvider: Login successful, cleared login_in_progress flag.\");\n            } catch (err) {\n                console.error(\"AuthProvider: Error signing in with custom token:\", err);\n                let errorMessage = \"Failed to sign in with custom token.\";\n                if (err instanceof Error) {\n                    if (err.message.includes('auth/quota-exceeded')) {\n                        errorMessage = \"Authentication quota exceeded. Please try again later.\";\n                    } else if (err.message.includes('auth/invalid-custom-token')) {\n                        errorMessage = \"Invalid authentication token. Please try logging in again.\";\n                    } else if (err.message.includes('auth/custom-token-mismatch')) {\n                        errorMessage = \"Authentication token mismatch. Please try logging in again.\";\n                    } else if (err.message.includes('auth/network-request-failed')) {\n                        errorMessage = \"Network error. Please check your internet connection and try again.\";\n                    } else {\n                        errorMessage = err.message;\n                    }\n                }\n                setError(errorMessage);\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear flags/storage on error\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('student_id');\n                localStorage.removeItem('user_role');\n                localStorage.removeItem('current_session');\n                localStorage.removeItem('login_in_progress');\n                // Re-throw the error so it can be caught by the caller\n                throw err;\n            } finally{\n                // setLoading(false); // Loading should be set to false by the onAuthStateChanged listener handling\n                console.log(\"AuthProvider: handleLoginSuccess finally block.\"); // Log finally\n            }\n        }\n    }[\"AuthProvider.useCallback[handleLoginSuccess]\"], []); // Removed auth dependency as it's globally available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            console.log(\"AuthProvider: Setting up onAuthStateChanged listener.\"); // Log listener setup\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": async (user)=>{\n                    // *** ADD DETAILED LOGGING for onAuthStateChanged ***\n                    const timestamp = new Date().toISOString();\n                    console.log(\"AuthProvider: onAuthStateChanged fired at \".concat(timestamp, \". User object:\"), user ? {\n                        uid: user.uid,\n                        email: user.email\n                    } : null);\n                    setUser(user); // Update the user state\n                    if (user) {\n                        var _JSON_parse;\n                        console.log(\"AuthProvider: onAuthStateChanged - User is present (UID: \".concat(user.uid, \").\"));\n                        // Try to refresh token to get latest claims\n                        try {\n                            console.log(\"AuthProvider: onAuthStateChanged - Refreshing token...\");\n                            await user.getIdToken(true);\n                            console.log(\"AuthProvider: onAuthStateChanged - Token refreshed.\");\n                            // ... check claims ...\n                            const idTokenResult = await user.getIdTokenResult();\n                            console.log(\"AuthProvider: onAuthStateChanged - Token claims:\", idTokenResult.claims);\n                        // ... set role/session from claims ...\n                        } catch (e) {\n                            console.error(\"AuthProvider: onAuthStateChanged - Error refreshing token:\", e);\n                        }\n                        // Try to load from localStorage first for faster initial render\n                        const storedUserData = localStorage.getItem('user_data');\n                        if (storedUserData) {\n                            setUserData(JSON.parse(storedUserData));\n                        }\n                        const storedChildrenData = localStorage.getItem('children_data');\n                        if (storedChildrenData) {\n                            setChildrenData(JSON.parse(storedChildrenData));\n                        }\n                        // Then refresh from server\n                        console.log(\"AuthProvider: onAuthStateChanged - Calling refreshUserData...\");\n                        await refreshUserData();\n                        console.log(\"AuthProvider: onAuthStateChanged - refreshUserData complete.\");\n                        setIsAuthenticated(true);\n                        console.log(\"AuthProvider: onAuthStateChanged - Set isAuthenticated = true.\");\n                        // Only refresh children data if user is a parent\n                        if ((userData === null || userData === void 0 ? void 0 : userData.role) === 'parent' || ((_JSON_parse = JSON.parse(storedUserData || '{}')) === null || _JSON_parse === void 0 ? void 0 : _JSON_parse.role) === 'parent') {\n                            await refreshChildrenData();\n                        }\n                    } else {\n                        console.log(\"AuthProvider: onAuthStateChanged - User is null.\");\n                        // ... clear user state and localStorage ...\n                        setUserData(null);\n                        setChildrenData(null);\n                        setUserRole(null);\n                        setStudentSession(null);\n                        setIsAuthenticated(false);\n                        localStorage.removeItem('user_data');\n                        localStorage.removeItem('children_data');\n                        localStorage.removeItem('CURRENT_SESSION_KEY'); // Ensure correct key if used elsewhere\n                        localStorage.removeItem('user_role');\n                        localStorage.removeItem('student_id');\n                        console.log(\"AuthProvider: onAuthStateChanged - Cleared state and localStorage.\");\n                    }\n                    console.log(\"AuthProvider: onAuthStateChanged - Setting loading = false at \".concat(new Date().toISOString(), \".\"));\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            // Cleanup function\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    console.log(\"AuthProvider: Cleaning up onAuthStateChanged listener.\"); // Log cleanup\n                    unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        refreshUserData,\n        refreshChildrenData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // Handle user authentication state\n            if (user) {\n                // If user is authenticated but no role is set, attempt to determine role\n                if (!userRole) {\n                    console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                    // Check for role in localStorage first\n                    const storedRole = localStorage.getItem('user_role');\n                    if (storedRole) {\n                        console.log(\"AuthProvider: Found role in localStorage: \".concat(storedRole));\n                        setUserRole(storedRole);\n                        // If this is a student, check for student ID\n                        if (storedRole === 'student') {\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(\"AuthProvider: Found student ID in localStorage: \".concat(storedStudentId));\n                                setStudentSession(storedStudentId);\n                            }\n                        }\n                    } else {\n                        // Check for parent indicators\n                        const isParent = localStorage.getItem('parent_id') || localStorage.getItem('parent_name');\n                        // Check for temporary student session (parent viewing student dashboard)\n                        const tempStudentSession = localStorage.getItem('temp_student_session');\n                        if (isParent) {\n                            console.log(\"AuthProvider: User appears to be a parent based on localStorage\");\n                            setUserRole('parent');\n                            // If parent is viewing a student dashboard, set the student session\n                            if (tempStudentSession) {\n                                console.log(\"AuthProvider: Parent viewing student dashboard for: \".concat(tempStudentSession));\n                                setStudentSession(tempStudentSession);\n                            }\n                        } else {\n                            // Default to student role if no other indicators\n                            console.log(\"AuthProvider: No role indicators found, defaulting to student\");\n                            setUserRole('student');\n                            // Try to find student ID\n                            const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                            if (storedStudentId) {\n                                console.log(\"AuthProvider: Found student ID: \".concat(storedStudentId));\n                                setStudentSession(storedStudentId);\n                            } else {\n                                console.log(\"AuthProvider: No student ID found, using UID: \".concat(user.uid));\n                                setStudentSession(user.uid);\n                                localStorage.setItem('student_id', user.uid);\n                                localStorage.setItem('current_session', user.uid);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            var _user_providerData_, _user_providerData;\n            // Skip if still loading auth state OR NextAuth session is loading\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Waiting for loading state or session status...\");\n                return;\n            }\n            // Determine authentication status based on Firebase OR NextAuth\n            const authenticated = !!user && !!user.uid || status === 'authenticated';\n            // Determine effective role and session for this effect run, prioritizing session if state is lagging\n            let effectiveRole = userRole;\n            let effectiveStudentSession = studentSession;\n            // Use SessionUser type for session check\n            const sessionUser = session === null || session === void 0 ? void 0 : session.user;\n            if (authenticated && !effectiveRole && status === 'authenticated' && (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role)) {\n                console.log(\"AuthProvider: Nav Effect - Using role directly from NextAuth session as state is not yet updated.\");\n                effectiveRole = sessionUser.role;\n            }\n            if (effectiveRole === 'student' && !effectiveStudentSession && status === 'authenticated' && (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.id)) {\n                console.log(\"AuthProvider: Nav Effect - Using student ID directly from NextAuth session as state is not yet updated.\");\n                effectiveStudentSession = sessionUser.id;\n            }\n            console.log(\"AuthProvider: Nav Effect - Running Checks\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                authenticated,\n                user: user ? {\n                    uid: user.uid,\n                    email: user.email,\n                    provider: (_user_providerData = user.providerData) === null || _user_providerData === void 0 ? void 0 : (_user_providerData_ = _user_providerData[0]) === null || _user_providerData_ === void 0 ? void 0 : _user_providerData_.providerId\n                } : null,\n                sessionUser,\n                // Log component state\n                stateUserRole: userRole,\n                stateStudentSession: studentSession,\n                // Log effective values used for logic\n                effectiveRole,\n                effectiveStudentSession,\n                userDataExists: !!userData,\n                currentPath:  true ? window.location.pathname : 0\n            });\n            try {\n                // Get current path and query parameters safely\n                const currentPath =  true ? window.location.pathname : 0;\n                const searchParams =  true ? new URLSearchParams(window.location.search) : 0;\n                const returnTo = searchParams.get('returnTo');\n                const resetParam = searchParams.get('reset') === 'true';\n                // --- DETAILED LOGGING ---\n                // console.log(\"AuthProvider: Nav Effect - Running Checks\", { // Moved up and enhanced\n                //   timestamp: new Date().toISOString(),\n                //   loading,\n                //   status,\n                //   authenticated,\n                //   user: user ? { uid: user.uid, email: user.email, provider: user.providerData?.[0]?.providerId } : null,\n                //   sessionUser,\n                //   stateUserRole: userRole, // Log state value\n                //   stateStudentSession: studentSession, // Log state value\n                //   effectiveRole, // Log derived value\n                //   effectiveStudentSession, // Log derived value\n                //   userDataExists: !!userData,\n                //   currentPath,\n                //   returnTo,\n                //   resetParam\n                // });\n                // --- END LOGGING ---\n                // Handle reset parameter first\n                if (resetParam && currentPath !== '/login') {\n                    console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                    // Clear local state immediately\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    // Clear storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Use replace to avoid adding reset=true to history\n                    router.replace('/login?reset=true');\n                    return;\n                }\n                // Use the 'authenticated' variable derived above\n                if (authenticated) {\n                    // User is authenticated (either Firebase or NextAuth)\n                    // If role or necessary session info is missing EVEN after checking session, log and wait\n                    // Use the effectiveRole and effectiveStudentSession derived above\n                    if (!effectiveRole) {\n                        console.log(\"AuthProvider: Nav Effect - User authenticated but effective role could not be determined. Waiting.\");\n                        // Potentially trigger refresh again if stuck? Or rely on initial refresh.\n                        // refreshUserData(); // Be cautious adding this here - could cause loops\n                        return; // Don't navigate yet\n                    }\n                    if (effectiveRole === 'student' && !effectiveStudentSession) {\n                        console.log(\"AuthProvider: Nav Effect - User is student but effective student session could not be determined. Waiting.\");\n                        // Potentially trigger refresh again?\n                        // refreshUserData();\n                        return; // Don't navigate yet\n                    }\n                    // --- REDIRECTION LOGIC ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Log the state being used for decision making\n                            console.log(\"AuthProvider: getDestination - Effective Role: \".concat(effectiveRole, \", Effective Session: \").concat(effectiveStudentSession));\n                            switch(effectiveRole){\n                                case 'parent':\n                                    return '/dashboard'; // Parent main dashboard\n                                case 'student':\n                                    // Ensure effectiveStudentSession has a value before constructing the path\n                                    if (!effectiveStudentSession) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no effective studentSession! Falling back to /login.\");\n                                        return '/login'; // Fallback to login if session missing unexpectedly\n                                    }\n                                    return \"/student-dashboard/\".concat(effectiveStudentSession); // Use effectiveStudentSession\n                                // Add cases for teacher/admin if needed\n                                default:\n                                    console.warn(\"AuthProvider: Nav Effect - Unknown or null effective role (\".concat(effectiveRole, \"), defaulting to /dashboard\"));\n                                    return '/dashboard';\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    // Determine destination *before* checking path\n                    const destination = getDestination();\n                    // If destination calculation failed (e.g., student without session), handle it\n                    if (destination === '/login') {\n                        console.error(\"AuthProvider: Nav Effect - Calculated destination is /login even though user should be authenticated. State:\", {\n                            effectiveRole,\n                            effectiveStudentSession\n                        });\n                        // Avoid redirecting to login if authenticated but destination calculation failed\n                        // Maybe logout or show an error page?\n                        // For now, just return to prevent redirect loop\n                        return;\n                    }\n                    // 1. If user is on the login page, redirect them away\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(\"AuthProvider: Nav Effect - User on login page. Redirecting to: \".concat(redirectTarget));\n                        // Use replace to avoid adding login page to history after successful login\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // 2. If user is authenticated but on the WRONG page for their role, redirect them\n                    const isCorrectPath = {\n                        \"AuthProvider.useEffect.isCorrectPath\": ()=>{\n                            if (!effectiveRole) return false; // Cannot determine correctness without an effective role\n                            if (effectiveRole === 'parent') {\n                                // Parents can be on /dashboard or /student-dashboard/*\n                                return currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/');\n                            }\n                            if (effectiveRole === 'student') {\n                                // Students should be on their specific dashboard or related pages\n                                // Ensure effectiveStudentSession is checked\n                                if (!effectiveStudentSession) return false; // Cannot be correct path if session is missing\n                                return currentPath.startsWith(\"/student-dashboard/\".concat(effectiveStudentSession)) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'); // Allow base /subjects and /subjects/*\n                            }\n                            // Add logic for other roles if necessary\n                            return false; // Default to false if role is unknown\n                        }\n                    }[\"AuthProvider.useEffect.isCorrectPath\"];\n                    if (!isCorrectPath()) {\n                        console.log(\"AuthProvider: Nav Effect - User on wrong page (\".concat(currentPath, \") for effective role (\").concat(effectiveRole, \"). Redirecting to: \").concat(destination));\n                        router.push(destination); // Use push here, maybe they navigated manually\n                        return;\n                    }\n                    console.log(\"AuthProvider: Nav Effect - User is authenticated and on an appropriate page (\".concat(currentPath, \") for effective role (\").concat(effectiveRole, \").\"));\n                } else {\n                    // User is not authenticated\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    // const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''; // Already defined above\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(\"AuthProvider: Nav Effect - User not authenticated and not on public page (\".concat(currentPath, \"). Redirecting to /login.\"));\n                        router.push('/login');\n                        return;\n                    } else {\n                        console.log(\"AuthProvider: Nav Effect - User not authenticated, already on public page (\".concat(currentPath, \"). No redirect needed.\"));\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Skip if still loading\n            if (loading) return;\n            // If we have a user but no role, try to determine the role\n            if (user && !userRole) {\n                console.log(\"AuthProvider: User authenticated but no role set. Attempting to determine role...\");\n                // Check for parent indicators first\n                const isParent = localStorage.getItem('parent_id') || localStorage.getItem('is_parent') === 'true' || localStorage.getItem('user_role') === 'parent';\n                if (isParent) {\n                    console.log(\"AuthProvider: Parent indicators found in localStorage. Setting role to parent.\");\n                    setUserRole('parent');\n                    localStorage.setItem('user_role', 'parent');\n                    return;\n                }\n                // Check localStorage for role\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    console.log(\"AuthProvider: Found role in localStorage: \".concat(storedRole));\n                    setUserRole(storedRole); // Assuming storedRole is 'parent' or 'student'\n                    return;\n                }\n                // Check if we have a student ID in localStorage\n                const storedStudentId = localStorage.getItem('student_id') || localStorage.getItem('current_session');\n                if (storedStudentId) {\n                    console.log(\"AuthProvider: Found student ID in localStorage: \".concat(storedStudentId, \". Setting role to student.\"));\n                    setUserRole('student');\n                    setStudentSession(storedStudentId); // Assuming storedStudentId is a string\n                    localStorage.setItem('user_role', 'student');\n                    return;\n                }\n                // Check if user has custom claims\n                user.getIdTokenResult(true).then({\n                    \"AuthProvider.useEffect\": (idTokenResult)=>{\n                        const claims = idTokenResult.claims;\n                        console.log(\"AuthProvider: Checking user claims:\", claims);\n                        // Ensure claims.role is treated as a string or null\n                        const claimRole = claims.role;\n                        const claimStudentId = claims.student_id;\n                        const claimParentId = claims.parent_id;\n                        if (claimRole === 'parent') {\n                            console.log(\"AuthProvider: Found parent role in claims. Setting role to parent.\");\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('is_parent', 'true');\n                        } else if (claimRole) {\n                            console.log(\"AuthProvider: Found role in claims: \".concat(claimRole));\n                            setUserRole(claimRole);\n                            localStorage.setItem('user_role', claimRole);\n                        } else if (claimStudentId) {\n                            console.log(\"AuthProvider: Found student_id in claims: \".concat(claimStudentId, \". Setting role to student.\"));\n                            setUserRole('student');\n                            setStudentSession(claimStudentId);\n                            localStorage.setItem('user_role', 'student');\n                            localStorage.setItem('student_id', claimStudentId);\n                        // Avoid setting current_session directly from claims unless absolutely necessary\n                        // localStorage.setItem('current_session', claimStudentId);\n                        } else if (claimParentId) {\n                            console.log(\"AuthProvider: Found parent_id in claims: \".concat(claimParentId, \". Setting role to parent.\"));\n                            setUserRole('parent');\n                            localStorage.setItem('user_role', 'parent');\n                            localStorage.setItem('parent_id', claimParentId);\n                            localStorage.setItem('is_parent', 'true');\n                        } else {\n                            // Check email domain for role hints\n                            const email = user.email || '';\n                            if (email.includes('parent') || email.includes('guardian')) {\n                                console.log(\"AuthProvider: Email suggests parent role. Setting role to parent.\");\n                                setUserRole('parent');\n                                localStorage.setItem('user_role', 'parent');\n                                localStorage.setItem('is_parent', 'true');\n                            } else {\n                                // Default to student role if we can't determine\n                                console.log(\"AuthProvider: No role information found. Defaulting to student role.\");\n                                setUserRole('student');\n                                localStorage.setItem('user_role', 'student');\n                            }\n                        }\n                    }\n                }[\"AuthProvider.useEffect\"]).catch({\n                    \"AuthProvider.useEffect\": (err)=>{\n                        console.error(\"AuthProvider: Error getting token claims:\", err);\n                        // Default to student role on error\n                        setUserRole('student');\n                        localStorage.setItem('user_role', 'student');\n                    }\n                }[\"AuthProvider.useEffect\"]);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        setUserRole,\n        setStudentSession\n    ]);\n    // *** NEW EFFECT: Update state based on NextAuth session ***\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Update role and session based on NextAuth session changes\n            if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"AuthProvider: NextAuth session authenticated. Updating role/session state from session:\", session.user);\n                // Use the defined SessionUser interface for type safety\n                const sessionUser = session.user;\n                const roleFromSession = sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role;\n                const idFromSession = sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.id;\n                console.log(\"AuthProvider: Extracted from session - Role: \".concat(roleFromSession, \", ID: \").concat(idFromSession));\n                if (roleFromSession && roleFromSession !== userRole) {\n                    setUserRole(roleFromSession);\n                    localStorage.setItem('user_role', roleFromSession);\n                    console.log(\"AuthProvider: Set userRole state to '\".concat(roleFromSession, \"' from session.\"));\n                }\n                if (roleFromSession === 'student' && idFromSession && idFromSession !== studentSession) {\n                    setStudentSession(idFromSession);\n                    localStorage.setItem('student_id', idFromSession);\n                    if (localStorage.getItem('current_session') === idFromSession) {\n                        localStorage.removeItem('current_session');\n                    }\n                    console.log(\"AuthProvider: Set studentSession state to '\".concat(idFromSession, \"' from session.\"));\n                }\n            } else if (status === 'unauthenticated') {\n                console.log(\"AuthProvider: NextAuth session status is not 'authenticated'. Current status:\", status);\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        status,\n        session,\n        userRole,\n        studentSession,\n        setUserRole,\n        setStudentSession\n    ]);\n    // Navigation Effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Add detailed logging at the start of the effect\n            console.log(\"AuthProvider: Nav Effect - START\", {\n                timestamp: new Date().toISOString(),\n                loading,\n                status,\n                user: user ? {\n                    uid: user.uid\n                } : null,\n                session: session ? {\n                    user: session.user,\n                    expires: session.expires\n                } : null,\n                stateUserRole: userRole,\n                stateStudentSession: studentSession // Log current state value\n            });\n            if (loading || status === 'loading') {\n                console.log(\"AuthProvider: Nav Effect - Still loading Firebase auth or NextAuth session.\");\n                return;\n            }\n            const firebaseUserPresent = !!user && !!user.uid;\n            const nextAuthSessionAuthenticated = status === 'authenticated' && !!(session === null || session === void 0 ? void 0 : session.user); // Ensure session.user exists\n            const overallAuthenticated = firebaseUserPresent || nextAuthSessionAuthenticated;\n            let derivedRole = null;\n            let derivedStudentId = null;\n            // Use SessionUser type for session check\n            const sessionUser = session === null || session === void 0 ? void 0 : session.user;\n            if (nextAuthSessionAuthenticated && sessionUser) {\n                derivedRole = (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.role) || null;\n                if (derivedRole === 'student') {\n                    derivedStudentId = (sessionUser === null || sessionUser === void 0 ? void 0 : sessionUser.id) || null;\n                }\n                console.log(\"AuthProvider: Nav Effect - Derived from NextAuth: Role='\".concat(derivedRole, \"', StudentID='\").concat(derivedStudentId, \"'\"));\n            }\n            // Fallback to component state if NextAuth session didn't provide info or isn't primary\n            if (!derivedRole && userRole) {\n                derivedRole = userRole;\n                console.log(\"AuthProvider: Nav Effect - Using Role from state: '\".concat(derivedRole, \"'\"));\n            }\n            if (derivedRole === 'student' && !derivedStudentId && studentSession) {\n                derivedStudentId = studentSession;\n                console.log(\"AuthProvider: Nav Effect - Using StudentID from state: '\".concat(derivedStudentId, \"'\"));\n            }\n            // Fallback to localStorage as a last resort for role if still unknown (use with caution)\n            if (!derivedRole) {\n                const storedRole = localStorage.getItem('user_role');\n                if (storedRole) {\n                    derivedRole = storedRole;\n                    console.log(\"AuthProvider: Nav Effect - Using Role from localStorage: '\".concat(derivedRole, \"'\"));\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        const storedStudentId = localStorage.getItem('student_id');\n                        if (storedStudentId) {\n                            derivedStudentId = storedStudentId;\n                            console.log(\"AuthProvider: Nav Effect - Using StudentID from localStorage: '\".concat(derivedStudentId, \"'\"));\n                        }\n                    }\n                }\n            }\n            console.log(\"AuthProvider: Nav Effect - Final derived values:\", {\n                overallAuthenticated,\n                derivedRole,\n                derivedStudentId\n            });\n            // Get current path and query parameters safely\n            const currentPath =  true ? window.location.pathname : 0;\n            const searchParams =  true ? new URLSearchParams(window.location.search) : 0;\n            const returnTo = searchParams.get('returnTo');\n            const resetParam = searchParams.get('reset') === 'true';\n            // Handle reset parameter first\n            if (resetParam && currentPath !== '/login') {\n                console.log(\"AuthProvider: Nav Effect - Reset parameter detected, redirecting to /login?reset=true\");\n                // Clear local state immediately\n                setUser(null);\n                setUserData(null);\n                setChildrenData(null);\n                setUserRole(null);\n                setStudentSession(null);\n                // Clear storage\n                localStorage.clear();\n                sessionStorage.clear();\n                // Use replace to avoid adding reset=true to history\n                router.replace('/login?reset=true');\n                return;\n            }\n            try {\n                if (overallAuthenticated) {\n                    if (!derivedRole) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated, but final derived role is null. Waiting for role resolution. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session === null || session === void 0 ? void 0 : session.user\n                        });\n                        // Potentially call refreshUserData if it seems stuck, but be careful of loops.\n                        // refreshUserData(); // Might be too aggressive.\n                        return; // Wait for role to be set.\n                    }\n                    if (derivedRole === 'student' && !derivedStudentId) {\n                        console.log(\"AuthProvider: Nav Effect - Authenticated as student, but final derived student ID is null. Waiting. Current states:\", {\n                            userRole,\n                            studentSession,\n                            sessionUser: session === null || session === void 0 ? void 0 : session.user\n                        });\n                        return; // Wait for student ID.\n                    }\n                    // --- REDIRECTION LOGIC (using derivedRole, derivedStudentId) ---\n                    const getDestination = {\n                        \"AuthProvider.useEffect.getDestination\": ()=>{\n                            // Uses derivedRole and derivedStudentId ...\n                            switch(derivedRole){\n                                case 'parent':\n                                    return '/dashboard';\n                                case 'student':\n                                    if (!derivedStudentId) {\n                                        console.error(\"AuthProvider: getDestination - Student role but no derivedStudentId!\");\n                                        return '/login';\n                                    }\n                                    return \"/student-dashboard/\".concat(derivedStudentId);\n                                default:\n                                    console.warn(\"AuthProvider: Nav Effect - Unknown or null derived role (\".concat(derivedRole, \"), defaulting to /dashboard\"));\n                                    return '/dashboard'; // Default destination\n                            }\n                        }\n                    }[\"AuthProvider.useEffect.getDestination\"];\n                    const destination = getDestination();\n                    // Ensure this logic is robust\n                    if (destination === '/login' && overallAuthenticated) {\n                        console.warn(\"AuthProvider: Nav Effect - Authenticated user is being directed to /login. This shouldn't happen. Destination calc issue or state inconsistency.\");\n                        return; // Prevent redirect to login if authenticated.\n                    }\n                    if (currentPath === '/login' || currentPath === '/login/') {\n                        const redirectTarget = returnTo || destination;\n                        console.log(\"AuthProvider: Nav Effect - User on login. Redirecting to: \".concat(redirectTarget));\n                        router.replace(redirectTarget);\n                        return;\n                    }\n                    // Simplified isCorrectPath\n                    let onCorrectPath = false;\n                    if (derivedRole === 'parent' && (currentPath === '/dashboard' || currentPath.startsWith('/student-dashboard/'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole === 'student' && derivedStudentId && (currentPath.startsWith(\"/student-dashboard/\".concat(derivedStudentId)) || currentPath.startsWith('/classroom') || currentPath.startsWith('/subjects'))) {\n                        onCorrectPath = true;\n                    } else if (derivedRole && ![\n                        'parent',\n                        'student'\n                    ].includes(derivedRole) && currentPath === '/dashboard') {\n                        onCorrectPath = true;\n                    }\n                    if (!onCorrectPath && destination !== currentPath) {\n                        console.log(\"AuthProvider: Nav Effect - User on wrong page (\".concat(currentPath, \") for role (\").concat(derivedRole, \"). Redirecting to: \").concat(destination));\n                        router.push(destination);\n                        return;\n                    }\n                    console.log(\"AuthProvider: Nav Effect - Authenticated and on appropriate page or no redirect needed.\");\n                } else {\n                    console.log(\"AuthProvider: Nav Effect - User not authenticated overall.\");\n                    const publicPaths = [\n                        '/login',\n                        '/register',\n                        '/forgot-password',\n                        '/reset'\n                    ];\n                    if (!publicPaths.includes(currentPath)) {\n                        console.log(\"AuthProvider: Nav Effect - Not authenticated and not on public page (\".concat(currentPath, \"). Redirecting to /login.\"));\n                        router.push('/login');\n                    } else {\n                        console.log(\"AuthProvider: Nav Effect - User not authenticated, already on public page (\".concat(currentPath, \"). No redirect needed.\"));\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Nav Effect - Unhandled Error:\", error);\n            // router.push('/login'); // Consider fallback\n            }\n        // Add 'status' and 'session' to the dependency array\n        }\n    }[\"AuthProvider.useEffect\"], [\n        loading,\n        user,\n        userRole,\n        studentSession,\n        router,\n        userData,\n        status,\n        session\n    ]); // Added status and session\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            user,\n            userData,\n            childrenData,\n            loading,\n            error,\n            isAuthenticated: !!user && !!user.uid || status === 'authenticated',\n            // Use SessionUser type for fallbacks\n            studentSession: studentSession || ((session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.role) === 'student' ? session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.id : null),\n            userRole: (userData === null || userData === void 0 ? void 0 : userData.role) || userRole || (session === null || session === void 0 ? void 0 : (_session_user2 = session.user) === null || _session_user2 === void 0 ? void 0 : _session_user2.role) || null,\n            manualSyncWithLocalStorage,\n            refreshUserData,\n            refreshChildrenData,\n            handleLoginSuccess,\n            logout: async ()=>{\n                console.log(\"AuthProvider: Logging out...\");\n                try {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n                    // Clear backend performance caches\n                    try {\n                        const cacheResponse = await fetch('/api/clear-performance-cache', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            }\n                        });\n                        if (cacheResponse.ok) {\n                            console.log(\"AuthProvider: Backend caches cleared successfully\");\n                        } else {\n                            console.warn(\"AuthProvider: Failed to clear backend caches, but continuing with logout\");\n                        }\n                    } catch (cacheError) {\n                        console.warn(\"AuthProvider: Error clearing backend caches:\", cacheError);\n                    // Don't fail logout due to cache clearing issues\n                    }\n                    // Clear local state and storage\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setIsAuthenticated(false);\n                    // Clear all browser storage\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    // Clear IndexedDB (if any data is stored there)\n                    try {\n                        if ('indexedDB' in window) {\n                            const databases = await indexedDB.databases();\n                            databases.forEach(async (db)=>{\n                                if (db.name) {\n                                    indexedDB.deleteDatabase(db.name);\n                                }\n                            });\n                        }\n                    } catch (idbError) {\n                        console.warn(\"AuthProvider: Error clearing IndexedDB:\", idbError);\n                    }\n                    // Clear service worker caches\n                    try {\n                        if ('caches' in window) {\n                            const cacheNames = await caches.keys();\n                            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n                            console.log(\"AuthProvider: Service worker caches cleared\");\n                        }\n                    } catch (swError) {\n                        console.warn(\"AuthProvider: Error clearing service worker caches:\", swError);\n                    }\n                    console.log(\"AuthProvider: Logout successful. All caches cleared. Redirecting to login.\");\n                    router.push('/login');\n                } catch (err) {\n                    console.error(\"AuthProvider: Logout failed:\", err);\n                    setError(\"Logout failed. Please try again.\");\n                    // Still attempt to clear state/storage even if signOut fails\n                    setUser(null);\n                    setUserData(null);\n                    setChildrenData(null);\n                    setUserRole(null);\n                    setStudentSession(null);\n                    setIsAuthenticated(false);\n                    localStorage.clear();\n                    sessionStorage.clear();\n                    router.push('/login'); // Redirect even on error\n                }\n            }\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\AuthProvider.tsx\",\n        lineNumber: 1157,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"N/bLuBSG0gwWpiCgk81uDpMs9XI=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_6__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = AuthProvider;\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/AuthProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx":
/*!**************************************************!*\
  !*** ./src/app/providers/ClientToastWrapper.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientToastWrapper: () => (/* binding */ ClientToastWrapper),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ClientToastWrapper,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ToastContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nfunction ClientToastWrapper(param) {\n    let { children } = param;\n    _s();\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_1___default().useState([]);\n    const toast = (props)=>{\n        const id = props.id || Math.random().toString(36).substr(2, 9);\n        // Add to toasts array\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    props\n                }\n            ]);\n        // Auto-dismiss after duration\n        const duration = props.duration || 5000;\n        setTimeout(()=>{\n            dismiss(id);\n        }, duration);\n        return {\n            id,\n            dismiss: ()=>dismiss(id)\n        };\n    };\n    const dismiss = (toastId)=>{\n        if (toastId) {\n            setToasts((prev)=>prev.filter((toast)=>toast.id !== toastId));\n        } else {\n            setToasts([]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            toast,\n            dismiss\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-4 right-4 z-50 space-y-2\",\n                children: toasts.map((param)=>{\n                    let { id, props } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n              bg-white border rounded-lg shadow-lg p-4 min-w-[300px] max-w-[400px] relative\\n              \".concat(props.variant === 'destructive' ? 'border-red-200 bg-red-50' : '', \"\\n              \").concat(props.variant === 'success' ? 'border-green-200 bg-green-50' : '', \"\\n              \").concat(props.variant === 'warning' ? 'border-yellow-200 bg-yellow-50' : '', \"\\n            \"),\n                        children: [\n                            props.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold mb-1\",\n                                children: props.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 29\n                            }, this),\n                            props.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: props.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 35\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>dismiss(id),\n                                className: \"absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center\",\n                                children: \"\\xd7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\ClientToastWrapper.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientToastWrapper, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\n_c = ClientToastWrapper;\n// Export the hook that components expect\nconst useToast = ()=>{\n    _s1();\n    const context = react__WEBPACK_IMPORTED_MODULE_1___default().useContext(ToastContext);\n    if (!context) {\n        console.error(\"useToast hook called outside of a ClientToastWrapper. Ensure ClientToastWrapper is placed correctly in your component tree (e.g., in client-providers.tsx).\");\n        throw new Error('useToast must be used within a ClientToastWrapper');\n    }\n    return context;\n};\n_s1(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ClientToastWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvdmlkZXJzL0NsaWVudFRvYXN0V3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQWlCMUIsTUFBTUMsNkJBQWVELDBEQUFtQixDQUEwQjtBQUUzRCxTQUFTRyxtQkFBbUIsS0FBMkM7UUFBM0MsRUFBRUMsUUFBUSxFQUFpQyxHQUEzQzs7SUFDakMsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdOLHFEQUFjLENBQTJDLEVBQUU7SUFFdkYsTUFBTVEsUUFBUSxDQUFDQztRQUNiLE1BQU1DLEtBQUtELE1BQU1DLEVBQUUsSUFBSUMsS0FBS0MsTUFBTSxHQUFHQyxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7UUFFNUQsc0JBQXNCO1FBQ3RCUixVQUFVUyxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTTtvQkFBRUw7b0JBQUlEO2dCQUFNO2FBQUU7UUFFMUMsOEJBQThCO1FBQzlCLE1BQU1PLFdBQVdQLE1BQU1PLFFBQVEsSUFBSTtRQUNuQ0MsV0FBVztZQUNUQyxRQUFRUjtRQUNWLEdBQUdNO1FBRUgsT0FBTztZQUNMTjtZQUNBUSxTQUFTLElBQU1BLFFBQVFSO1FBQ3pCO0lBQ0Y7SUFFQSxNQUFNUSxVQUFVLENBQUNDO1FBQ2YsSUFBSUEsU0FBUztZQUNYYixVQUFVUyxDQUFBQSxPQUFRQSxLQUFLSyxNQUFNLENBQUNaLENBQUFBLFFBQVNBLE1BQU1FLEVBQUUsS0FBS1M7UUFDdEQsT0FBTztZQUNMYixVQUFVLEVBQUU7UUFDZDtJQUNGO0lBRUEscUJBQ0UsOERBQUNMLGFBQWFvQixRQUFRO1FBQUNDLE9BQU87WUFBRWQ7WUFBT1U7UUFBUTs7WUFDNUNkOzBCQUVELDhEQUFDbUI7Z0JBQUlDLFdBQVU7MEJBQ1puQixPQUFPb0IsR0FBRyxDQUFDO3dCQUFDLEVBQUVmLEVBQUUsRUFBRUQsS0FBSyxFQUFFO3lDQUN4Qiw4REFBQ2M7d0JBRUNDLFdBQVcsZ0hBR1BmLE9BREFBLE1BQU1pQixPQUFPLEtBQUssZ0JBQWdCLDZCQUE2QixJQUFHLG9CQUVsRWpCLE9BREFBLE1BQU1pQixPQUFPLEtBQUssWUFBWSxpQ0FBaUMsSUFBRyxvQkFDRSxPQUFwRWpCLE1BQU1pQixPQUFPLEtBQUssWUFBWSxtQ0FBbUMsSUFBRzs7NEJBR3ZFakIsTUFBTWtCLEtBQUssa0JBQUksOERBQUNKO2dDQUFJQyxXQUFVOzBDQUFzQmYsTUFBTWtCLEtBQUs7Ozs7Ozs0QkFDL0RsQixNQUFNbUIsV0FBVyxrQkFBSSw4REFBQ0w7Z0NBQUlDLFdBQVU7MENBQXlCZixNQUFNbUIsV0FBVzs7Ozs7OzBDQUMvRSw4REFBQ0M7Z0NBQ0NDLFNBQVMsSUFBTVosUUFBUVI7Z0NBQ3ZCYyxXQUFVOzBDQUNYOzs7Ozs7O3VCQWJJZDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFxQmpCO0dBekRnQlA7S0FBQUE7QUEyRGhCLHlDQUF5QztBQUNsQyxNQUFNNEIsV0FBVzs7SUFDdEIsTUFBTUMsVUFBVWhDLHVEQUFnQixDQUFDQztJQUNqQyxJQUFJLENBQUMrQixTQUFTO1FBQ1pFLFFBQVFDLEtBQUssQ0FBQztRQUNkLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9KO0FBQ1QsRUFBRTtJQVBXRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXHByb3ZpZGVyc1xcQ2xpZW50VG9hc3RXcmFwcGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5cclxuLy8gU2ltcGxlIHRvYXN0IGNvbnRleHQgaW1wbGVtZW50YXRpb25cclxuaW50ZXJmYWNlIFRvYXN0UHJvcHMge1xyXG4gIGlkPzogc3RyaW5nO1xyXG4gIHRpdGxlPzogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIGRlc2NyaXB0aW9uPzogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIHZhcmlhbnQ/OiAnZGVmYXVsdCcgfCAnZGVzdHJ1Y3RpdmUnIHwgJ3N1Y2Nlc3MnIHwgJ3dhcm5pbmcnIHwgJ2luZm8nO1xyXG4gIGFjdGlvbj86IFJlYWN0LlJlYWN0Tm9kZTtcclxuICBkdXJhdGlvbj86IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIFRvYXN0Q29udGV4dFR5cGUge1xyXG4gIHRvYXN0OiAocHJvcHM6IFRvYXN0UHJvcHMpID0+IHsgaWQ6IHN0cmluZzsgZGlzbWlzczogKCkgPT4gdm9pZCB9O1xyXG4gIGRpc21pc3M6ICh0b2FzdElkPzogc3RyaW5nKSA9PiB2b2lkO1xyXG59XHJcblxyXG5jb25zdCBUb2FzdENvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0PFRvYXN0Q29udGV4dFR5cGUgfCBudWxsPihudWxsKTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBDbGllbnRUb2FzdFdyYXBwZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIGNvbnN0IFt0b2FzdHMsIHNldFRvYXN0c10gPSBSZWFjdC51c2VTdGF0ZTxBcnJheTx7IGlkOiBzdHJpbmc7IHByb3BzOiBUb2FzdFByb3BzIH0+PihbXSk7XHJcblxyXG4gIGNvbnN0IHRvYXN0ID0gKHByb3BzOiBUb2FzdFByb3BzKSA9PiB7XHJcbiAgICBjb25zdCBpZCA9IHByb3BzLmlkIHx8IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KTtcclxuICAgIFxyXG4gICAgLy8gQWRkIHRvIHRvYXN0cyBhcnJheVxyXG4gICAgc2V0VG9hc3RzKHByZXYgPT4gWy4uLnByZXYsIHsgaWQsIHByb3BzIH1dKTtcclxuICAgIFxyXG4gICAgLy8gQXV0by1kaXNtaXNzIGFmdGVyIGR1cmF0aW9uXHJcbiAgICBjb25zdCBkdXJhdGlvbiA9IHByb3BzLmR1cmF0aW9uIHx8IDUwMDA7XHJcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgZGlzbWlzcyhpZCk7XHJcbiAgICB9LCBkdXJhdGlvbik7XHJcbiAgICBcclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlkLFxyXG4gICAgICBkaXNtaXNzOiAoKSA9PiBkaXNtaXNzKGlkKVxyXG4gICAgfTtcclxuICB9O1xyXG5cclxuICBjb25zdCBkaXNtaXNzID0gKHRvYXN0SWQ/OiBzdHJpbmcpID0+IHtcclxuICAgIGlmICh0b2FzdElkKSB7XHJcbiAgICAgIHNldFRvYXN0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHRvYXN0ID0+IHRvYXN0LmlkICE9PSB0b2FzdElkKSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRUb2FzdHMoW10pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8VG9hc3RDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt7IHRvYXN0LCBkaXNtaXNzIH19PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICAgIHsvKiBTaW1wbGUgdG9hc3QgZGlzcGxheSAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XHJcbiAgICAgICAge3RvYXN0cy5tYXAoKHsgaWQsIHByb3BzIH0pID0+IChcclxuICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAga2V5PXtpZH1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICAgYmctd2hpdGUgYm9yZGVyIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNCBtaW4tdy1bMzAwcHhdIG1heC13LVs0MDBweF0gcmVsYXRpdmVcclxuICAgICAgICAgICAgICAke3Byb3BzLnZhcmlhbnQgPT09ICdkZXN0cnVjdGl2ZScgPyAnYm9yZGVyLXJlZC0yMDAgYmctcmVkLTUwJyA6ICcnfVxyXG4gICAgICAgICAgICAgICR7cHJvcHMudmFyaWFudCA9PT0gJ3N1Y2Nlc3MnID8gJ2JvcmRlci1ncmVlbi0yMDAgYmctZ3JlZW4tNTAnIDogJyd9XHJcbiAgICAgICAgICAgICAgJHtwcm9wcy52YXJpYW50ID09PSAnd2FybmluZycgPyAnYm9yZGVyLXllbGxvdy0yMDAgYmcteWVsbG93LTUwJyA6ICcnfVxyXG4gICAgICAgICAgICBgfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7cHJvcHMudGl0bGUgJiYgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIG1iLTFcIj57cHJvcHMudGl0bGV9PC9kaXY+fVxyXG4gICAgICAgICAgICB7cHJvcHMuZGVzY3JpcHRpb24gJiYgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj57cHJvcHMuZGVzY3JpcHRpb259PC9kaXY+fVxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZGlzbWlzcyhpZCl9XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgdy02IGgtNiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICDDl1xyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICkpfVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuXHJcbi8vIEV4cG9ydCB0aGUgaG9vayB0aGF0IGNvbXBvbmVudHMgZXhwZWN0XHJcbmV4cG9ydCBjb25zdCB1c2VUb2FzdCA9ICgpOiBUb2FzdENvbnRleHRUeXBlID0+IHtcclxuICBjb25zdCBjb250ZXh0ID0gUmVhY3QudXNlQ29udGV4dChUb2FzdENvbnRleHQpO1xyXG4gIGlmICghY29udGV4dCkge1xyXG4gICAgY29uc29sZS5lcnJvcihcInVzZVRvYXN0IGhvb2sgY2FsbGVkIG91dHNpZGUgb2YgYSBDbGllbnRUb2FzdFdyYXBwZXIuIEVuc3VyZSBDbGllbnRUb2FzdFdyYXBwZXIgaXMgcGxhY2VkIGNvcnJlY3RseSBpbiB5b3VyIGNvbXBvbmVudCB0cmVlIChlLmcuLCBpbiBjbGllbnQtcHJvdmlkZXJzLnRzeCkuXCIpO1xyXG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUb2FzdCBtdXN0IGJlIHVzZWQgd2l0aGluIGEgQ2xpZW50VG9hc3RXcmFwcGVyJyk7XHJcbiAgfVxyXG4gIHJldHVybiBjb250ZXh0O1xyXG59O1xyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUb2FzdENvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwiQ2xpZW50VG9hc3RXcmFwcGVyIiwiY2hpbGRyZW4iLCJ0b2FzdHMiLCJzZXRUb2FzdHMiLCJ1c2VTdGF0ZSIsInRvYXN0IiwicHJvcHMiLCJpZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInByZXYiLCJkdXJhdGlvbiIsInNldFRpbWVvdXQiLCJkaXNtaXNzIiwidG9hc3RJZCIsImZpbHRlciIsIlByb3ZpZGVyIiwidmFsdWUiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJ2YXJpYW50IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ1c2VUb2FzdCIsImNvbnRleHQiLCJ1c2VDb250ZXh0IiwiY29uc29sZSIsImVycm9yIiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/authService.ts":
/*!********************************!*\
  !*** ./src/lib/authService.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENT_SESSION_KEY: () => (/* binding */ CURRENT_SESSION_KEY),\n/* harmony export */   clearAuthData: () => (/* binding */ clearAuthData),\n/* harmony export */   findUserByUserId: () => (/* binding */ findUserByUserId),\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   getFreshAuthHeaders: () => (/* binding */ getFreshAuthHeaders),\n/* harmony export */   getUserRole: () => (/* binding */ getUserRole),\n/* harmony export */   getUserSession: () => (/* binding */ getUserSession),\n/* harmony export */   refreshAuthToken: () => (/* binding */ refreshAuthToken),\n/* harmony export */   saveUserSession: () => (/* binding */ saveUserSession),\n/* harmony export */   setupAuthListener: () => (/* binding */ setupAuthListener),\n/* harmony export */   setupAuthStateListener: () => (/* binding */ setupAuthStateListener),\n/* harmony export */   signInWithEmailAndPassword: () => (/* binding */ signInWithEmailAndPassword),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   syncAuthState: () => (/* binding */ syncAuthState)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n// lib/authService.ts\n/* __next_internal_client_entry_do_not_use__ CURRENT_SESSION_KEY,saveUserSession,getUserSession,clearAuthData,signInWithEmailAndPassword,signOut,setupAuthStateListener,setupAuthListener,getAuthHeaders,getFreshAuthHeaders,refreshAuthToken,findUserByUserId,getUserRole,syncAuthState auto */ \n\n\n// Constants\nconst SESSION_KEY = 'user_session';\nconst CURRENT_SESSION_KEY = 'current_session'; // Export this constant\nconst TOKEN_KEY = 'token';\n/**\r\n * Save user session to localStorage with consistent keys\r\n */ const saveUserSession = (session)=>{\n    if (!session || !session.uid) return;\n    try {\n        // Add timestamp before saving\n        const sessionToSave = {\n            ...session,\n            tokenTimestamp: Date.now()\n        };\n        // Save the full session object with timestamp\n        localStorage.setItem(SESSION_KEY, JSON.stringify(sessionToSave));\n        // CURRENT_SESSION_KEY should be set explicitly elsewhere when the *backend* session ID is known.\n        // Do not automatically set it to the Firebase UID here.\n        // localStorage.setItem(CURRENT_SESSION_KEY, session.uid); // Removed this line\n        localStorage.setItem(TOKEN_KEY, session.token); // Keep saving the token\n        console.log('Session object saved for UID:', session.uid);\n    } catch (error) {\n        console.error('Error saving user session:', error);\n    }\n};\n/**\r\n * Get the current user session from localStorage\r\n */ const getUserSession = ()=>{\n    try {\n        const sessionStr = localStorage.getItem(SESSION_KEY);\n        if (!sessionStr) return null;\n        return JSON.parse(sessionStr);\n    } catch (error) {\n        console.error('Failed to parse user session:', error);\n        return null;\n    }\n};\n/**\r\n * Clear all auth-related data from localStorage\r\n */ const clearAuthData = ()=>{\n    try {\n        localStorage.removeItem(SESSION_KEY);\n        localStorage.removeItem(TOKEN_KEY);\n        localStorage.removeItem(CURRENT_SESSION_KEY);\n        localStorage.removeItem('authMethod');\n        localStorage.removeItem('viewing_as_child');\n        localStorage.removeItem('parent_id');\n        localStorage.removeItem('parent_name');\n        localStorage.removeItem('user_name');\n        localStorage.removeItem('parentEnrollmentMessage');\n    } catch (error) {\n        console.error('Error clearing auth data:', error);\n    }\n};\n/**\r\n * Sign in with email and password\r\n */ const signInWithEmailAndPassword = async (email, password)=>{\n    // Clear any previous auth state first\n    await signOut();\n    console.log(\"Attempting email/password sign-in\");\n    try {\n        var _auth_currentUser;\n        // Use Firebase's email/password auth\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        const user = userCredential.user;\n        // Get fresh token with custom claims\n        const additionalClaims = {\n            student_id: localStorage.getItem('viewing_as_child') || undefined\n        };\n        const tokenResult = await user.getIdTokenResult(true);\n        const token = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n        // Get user details from Firestore\n        const userDetails = await getUserDetailsFromFirestore(user);\n        // Create session\n        const userSession = {\n            uid: user.uid,\n            email: user.email,\n            name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        // Save session\n        saveUserSession(userSession);\n        console.log(\"Authentication successful\");\n        return userSession;\n    } catch (error) {\n        console.error(\"Authentication error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Sign out the current user\r\n */ const signOut = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        clearAuthData();\n        console.log(\"User signed out\");\n    } catch (error) {\n        console.error(\"Sign out error:\", error);\n        throw error;\n    }\n};\n/**\r\n * Set up a listener for auth state changes\r\n */ const setupAuthStateListener = (callback)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n};\n/**\r\n * Setup auth listener used by the session provider\r\n * This matches the signature expected by useSession\r\n */ const setupAuthListener = (setSession, setError, setIsLoading)=>{\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, async (user)=>{\n        console.log(\"Auth state changed:\", user ? \"User \".concat(user.uid) : \"No user\");\n        if (!user) {\n            setSession(null);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            var _auth_currentUser;\n            // Get fresh token for signed-in user\n            // Get custom claims including student_id if viewing as parent\n            const additionalClaims = {\n                student_id: localStorage.getItem('viewing_as_child') || undefined\n            };\n            const tokenResult = await user.getIdTokenResult(true);\n            const tokenString = await ((_auth_currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser) === null || _auth_currentUser === void 0 ? void 0 : _auth_currentUser.getIdToken(true, additionalClaims));\n            if (!tokenString) {\n                throw new Error('Failed to get authentication token');\n            }\n            // Get user details from Firestore\n            const userDetails = await getUserDetailsFromFirestore(user);\n            // Create session object with token string\n            const userSession = {\n                uid: user.uid,\n                email: user.email || '',\n                name: user.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || '',\n                token: tokenString,\n                tokenResult,\n                role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n            };\n            // Set session and store backend session ID if available\n            setSession(userSession);\n            // If this is a new login response with sessionId, store it\n            const responseSessionId = user.sessionId;\n            if (responseSessionId) {\n                localStorage.setItem(CURRENT_SESSION_KEY, responseSessionId);\n            }\n        } catch (error) {\n            console.error(\"Error getting auth token:\", error);\n            setError(\"Failed to authenticate session\");\n            setSession(null);\n        } finally{\n            setIsLoading(false);\n        }\n    });\n};\n/**\r\n * Get auth headers for API requests\r\n * Accepts backendSessionId from context to avoid localStorage race conditions.\r\n */ const getAuthHeaders = (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    let currentToken = null;\n    let currentUid = null;\n    if (currentUser) {\n        var _currentUser_stsTokenManager;\n        currentUid = currentUser.uid;\n        // Attempt to get token from Firebase auth state first\n        currentToken = ((_currentUser_stsTokenManager = currentUser.stsTokenManager) === null || _currentUser_stsTokenManager === void 0 ? void 0 : _currentUser_stsTokenManager.accessToken) || null;\n    }\n    const storedSession = getUserSession();\n    // Prefer the ID passed from context, fall back to localStorage only if necessary (e.g., during initial load before context is ready)\n    const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n    // Use the effective backend session ID for the Session-ID header\n    if (effectiveBackendSessionId) {\n        headers['Session-ID'] = effectiveBackendSessionId;\n    } else {\n        // Fallback to UID only if backend session ID isn't available anywhere\n        const effectiveUid = currentUid || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.uid);\n        if (effectiveUid) {\n            console.warn(\"Using UID (\".concat(effectiveUid, \") as Session-ID header fallback. Backend session ID not found in context or localStorage ('\").concat(CURRENT_SESSION_KEY, \"').\"));\n            headers['Session-ID'] = effectiveUid; // Still might be wrong, but it's the last resort\n        } else {\n            console.error(\"Cannot set Session-ID header: No backend session ID or user UID found.\");\n        }\n    }\n    // Prefer token from context's stored session if Firebase token is missing\n    const effectiveToken = currentToken || (storedSession === null || storedSession === void 0 ? void 0 : storedSession.token);\n    if (effectiveToken) {\n        headers['Authorization'] = \"Bearer \".concat(effectiveToken);\n    } else {\n        console.warn(\"Authorization token not found in Firebase state or stored session. This may cause authentication errors.\");\n        // Instead of completely failing, let's try to get token from localStorage as last resort\n        const fallbackToken = localStorage.getItem('token');\n        if (fallbackToken) {\n            console.warn(\"Using fallback token from localStorage\");\n            headers['Authorization'] = \"Bearer \".concat(fallbackToken);\n        } else {\n            console.error(\"No authentication token available from any source.\");\n        }\n    }\n    // Get role from stored session if available\n    const effectiveRole = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role if not found\n    headers['X-User-Role'] = effectiveRole;\n    // CRITICAL FIX: Add testing mode header when no valid authentication is available\n    // This allows backend to generate console logs for lesson interactions during development\n    if (!effectiveToken || effectiveToken === 'undefined' || effectiveToken === 'null') {\n        console.warn(\"No valid authentication token found - enabling testing mode for backend logging\");\n        headers['X-Testing-Mode'] = 'true';\n    }\n    return headers;\n};\n/**\r\n * Get fresh auth headers with token refresh\r\n * Accepts backendSessionId from context.\r\n */ const getFreshAuthHeaders = async (backendSessionIdFromContext)=>{\n    const headers = {\n        'Content-Type': 'application/json'\n    };\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    if (currentUser) {\n        try {\n            const token = await currentUser.getIdToken(true); // Force refresh\n            headers['Authorization'] = \"Bearer \".concat(token);\n            // Use the effective backend session ID for the Session-ID header\n            const effectiveBackendSessionId = backendSessionIdFromContext || localStorage.getItem(CURRENT_SESSION_KEY);\n            if (effectiveBackendSessionId) {\n                headers['Session-ID'] = effectiveBackendSessionId;\n            } else {\n                // Fallback to UID only if backend session ID isn't available anywhere\n                console.warn(\"Using UID (\".concat(currentUser.uid, \") as Session-ID header fallback during fresh token request. Backend session ID not found.\"));\n                headers['Session-ID'] = currentUser.uid; // Last resort\n            }\n            const storedSession = getUserSession();\n            headers['X-User-Role'] = (storedSession === null || storedSession === void 0 ? void 0 : storedSession.role) || 'student'; // Default role\n        } catch (error) {\n            console.error(\"Error getting fresh token:\", error);\n            // Fallback to non-fresh headers if refresh fails\n            return getAuthHeaders(backendSessionIdFromContext);\n        }\n    } else {\n        // If no current user, return standard (likely unauthenticated) headers with testing mode\n        const fallbackHeaders = getAuthHeaders(backendSessionIdFromContext);\n        fallbackHeaders['X-Testing-Mode'] = 'true';\n        console.warn(\"No current user found - enabling testing mode for backend logging\");\n        return fallbackHeaders;\n    }\n    return headers;\n};\n/**\r\n * Refresh the auth token\r\n */ const refreshAuthToken = async ()=>{\n    try {\n        const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n        if (!currentUser) {\n            console.error(\"No current user found for token refresh\");\n            return null;\n        }\n        // Force refresh the token\n        const newToken = await currentUser.getIdToken(true);\n        // Update the stored session with new token\n        const storedSession = getUserSession();\n        if (storedSession) {\n            const updatedSession = {\n                ...storedSession,\n                token: newToken\n            };\n            saveUserSession(updatedSession);\n        }\n        return newToken;\n    } catch (error) {\n        console.error(\"Failed to refresh authentication token:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user details from Firestore\r\n */ async function getUserDetailsFromFirestore(user) {\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', user.uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                name: data.name,\n                role: data.role,\n                children: data.children || [],\n                parents: data.parents || []\n            };\n        }\n        // Fallback to check parents collection if not found in users\n        const parentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'parents', user.uid);\n        const parentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(parentRef);\n        if (parentDoc.exists()) {\n            const data = parentDoc.data();\n            return {\n                name: data.name,\n                role: 'parent',\n                children: data.children || []\n            };\n        }\n        // Fallback to check students collection\n        const studentRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'students', user.uid);\n        const studentDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(studentRef);\n        if (studentDoc.exists()) {\n            const data = studentDoc.data();\n            return {\n                name: data.name,\n                role: 'student',\n                parents: data.parents || []\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching user details:\", error);\n        return null;\n    }\n}\n/**\r\n * Find user by userId (for child accounts)\r\n */ const findUserByUserId = async (userId)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const usersRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.collection)(db, 'users');\n        const q = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.query)(usersRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.where)('userId', '==', userId));\n        const querySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDocs)(q);\n        if (querySnapshot.empty) {\n            return null;\n        }\n        return querySnapshot.docs[0].data().email;\n    } catch (error) {\n        console.error(\"Error finding user by userId:\", error);\n        return null;\n    }\n};\n/**\r\n * Get user role from Firestore\r\n */ const getUserRole = async (uid)=>{\n    try {\n        const db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getFirestore)();\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(db, 'users', uid);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)(userRef);\n        if (userDoc.exists() && userDoc.data().role) {\n            return userDoc.data().role;\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user role:\", error);\n        return null;\n    }\n};\n/**\r\n * Sync Firebase auth state with local storage\r\n * This is crucial to fix the state mismatch issues\r\n */ const syncAuthState = async ()=>{\n    const currentUser = _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.currentUser;\n    const storedSession = getUserSession();\n    // Case 1: Firebase has user but localStorage doesn't\n    if (currentUser && (!storedSession || storedSession.uid !== currentUser.uid)) {\n        console.log(\"Syncing: Firebase has user but localStorage doesn't match\");\n        const token = await currentUser.getIdToken(true);\n        const userDetails = await getUserDetailsFromFirestore(currentUser);\n        const userSession = {\n            uid: currentUser.uid,\n            email: currentUser.email,\n            name: currentUser.displayName || (userDetails === null || userDetails === void 0 ? void 0 : userDetails.name) || null,\n            token: token,\n            role: userDetails === null || userDetails === void 0 ? void 0 : userDetails.role\n        };\n        saveUserSession(userSession);\n        return userSession;\n    }\n    // Case 2: Firebase has no user but localStorage does\n    if (!currentUser && storedSession) {\n        console.log(\"Syncing: Firebase has no user but localStorage does\");\n        clearAuthData();\n        return null;\n    }\n    // Case 3: Both have matching user, check if token needs refresh\n    if (currentUser && storedSession && currentUser.uid === storedSession.uid) {\n        console.log(\"Syncing: Both have matching user\");\n        // Token is older than 30 minutes, refresh it\n        const tokenDate = new Date(storedSession.tokenTimestamp || 0);\n        const now = new Date();\n        const diffMinutes = (now.getTime() - tokenDate.getTime()) / (1000 * 60);\n        if (diffMinutes > 30) {\n            console.log(\"Token is older than 30 minutes, refreshing\");\n            await refreshAuthToken();\n        }\n    }\n    return storedSession;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/authService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   app: () => (/* binding */ app),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// lib/firebase.ts\n/* __next_internal_client_entry_do_not_use__ app,auth,db,storage auto */ \n\n\n\n// Default Firebase configuration for development\nconst devConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\",\n    authDomain: \"solynta-academy.firebaseapp.com\",\n    projectId: \"solynta-academy\",\n    storageBucket: \"solynta-academy.firebasestorage.app\",\n    messagingSenderId: \"914922463191\",\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\",\n    measurementId: \"G-ZVC7R06Y33\"\n};\n// Firebase configuration - try environment variables first, then fallback to dev config\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDWVM8PvcWD4nAkpsI7FuDKCvpp_PEnPlU\" || 0,\n    authDomain: \"solynta-academy.firebaseapp.com\" || 0,\n    projectId: \"solynta-academy\" || 0,\n    storageBucket: \"solynta-academy.firebasestorage.app\" || 0,\n    messagingSenderId: \"914922463191\" || 0,\n    appId: \"1:914922463191:web:b6e9c737dba77a26643592\" || 0,\n    measurementId: \"G-ZVC7R06Y33\" || 0\n};\nconsole.log('Using Firebase config with project ID:', firebaseConfig.projectId);\n// Initialize Firebase app (Singleton pattern)\nconst app = !(0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)();\n// Initialize services - these will be initialized client-side\nlet auth;\nlet db;\nlet storage;\n// Check if running in a browser environment\nif (true) {\n    // Initialize Auth\n    auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n    (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.setPersistence)(auth, firebase_auth__WEBPACK_IMPORTED_MODULE_1__.browserLocalPersistence).catch((error)=>console.error(\"Auth persistence error:\", error));\n    // Initialize Firestore\n    // Note: getFirestore() could also be used if default settings are acceptable\n    db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.initializeFirestore)(app, {\n        cacheSizeBytes: firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.CACHE_SIZE_UNLIMITED,\n        experimentalForceLongPolling: true,\n        ignoreUndefinedProperties: true\n    });\n    // Initialize Storage\n    storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n    // Connect to emulators in development if configured\n    if ( true && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {\n        console.log(\"Connecting to Firebase Emulators...\");\n        // Use dynamic imports for emulator functions to potentially reduce bundle size\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectAuthEmulator } = param;\n            try {\n                connectAuthEmulator(auth, 'http://localhost:9099', {\n                    disableWarnings: true\n                });\n                console.log(\"Auth Emulator connected to http://localhost:9099\");\n            } catch (error) {\n                console.error(\"Error connecting to Auth Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectFirestoreEmulator } = param;\n            try {\n                connectFirestoreEmulator(db, 'localhost', 8080);\n                console.log(\"Firestore Emulator connected to localhost:8080\");\n            } catch (error) {\n                console.error(\"Error connecting to Firestore Emulator:\", error);\n            }\n        });\n        Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\")).then((param)=>{\n            let { connectStorageEmulator } = param;\n            try {\n                connectStorageEmulator(storage, 'localhost', 9199);\n                console.log(\"Storage Emulator connected to localhost:9199\");\n            } catch (error) {\n                console.error(\"Error connecting to Storage Emulator:\", error);\n            }\n        });\n    }\n} else {}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase.ts\n"));

/***/ })

}]);