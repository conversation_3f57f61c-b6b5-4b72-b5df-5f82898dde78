const http = require('http');

async function debugButtonIssues() {
  console.log('🔍 Debugging Button Issues in Lesson Platform...\n');
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/timetable?studentId=andrea_ugono_33305&week=1',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  console.log('📡 Testing timetable API:', `http://${options.hostname}:${options.port}${options.path}`);
  
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (response.success) {
            console.log('✅ Timetable API Response successful');
            console.log('📊 Total lessons:', response.data.totalLessons);
            
            // Debug Issue 1: SummaryTab Button State
            console.log('\n🔍 ISSUE 1: SummaryTab Button State Analysis');
            console.log('=' .repeat(50));
            
            const lessons = response.data.schedule || [];
            const nonFreePeriodLessons = lessons.filter(l => !l.isFreePeriod);
            
            console.log(`📚 Total lessons (excluding free periods): ${nonFreePeriodLessons.length}`);
            
            // Check lesson data structure for button enabling
            console.log('\n📋 Lesson Data Structure Check:');
            if (nonFreePeriodLessons.length > 0) {
              const sampleLesson = nonFreePeriodLessons[0];
              console.log('Sample lesson structure:');
              console.log(`  - lessonReference: "${sampleLesson.lessonReference}" (${typeof sampleLesson.lessonReference})`);
              console.log(`  - status: "${sampleLesson.status}" (${typeof sampleLesson.status})`);
              console.log(`  - subject: "${sampleLesson.subject}" (${typeof sampleLesson.subject})`);
              console.log(`  - grade: "${sampleLesson.grade}" (${typeof sampleLesson.grade})`);
              console.log(`  - studentId would be: "andrea_ugono_33305"`);
              
              // Check if required fields are present for button enabling
              const hasLessonRef = !!sampleLesson.lessonReference;
              const hasStatus = !!sampleLesson.status;
              const hasSubject = !!sampleLesson.subject;
              
              console.log('\n🔧 Button Enabling Requirements:');
              console.log(`  ✓ lessonRef present: ${hasLessonRef ? '✅' : '❌'}`);
              console.log(`  ✓ status present: ${hasStatus ? '✅' : '❌'}`);
              console.log(`  ✓ subject present: ${hasSubject ? '✅' : '❌'}`);
              console.log(`  ✓ isStudent detection: Depends on auth.currentUser?.uid === studentId`);
              
              // Analyze status distribution
              console.log('\n📊 Status Distribution:');
              const statusCounts = {};
              nonFreePeriodLessons.forEach(lesson => {
                const status = lesson.status || 'unknown';
                statusCounts[status] = (statusCounts[status] || 0) + 1;
              });
              
              Object.entries(statusCounts).forEach(([status, count]) => {
                console.log(`  - ${status}: ${count} lessons`);
              });
              
              // Expected button behavior based on status
              console.log('\n🎯 Expected Button Behavior:');
              console.log('  - upcoming + isStudent + lessonRef → "Start" button (enabled)');
              console.log('  - current + isStudent + lessonRef → "Join" button (enabled)');
              console.log('  - completed + isStudent + lessonRef → "Review" button (enabled)');
              console.log('  - !isStudent → "View" button (enabled for parents)');
              
            } else {
              console.log('❌ No lessons found for analysis');
            }
            
            // Debug Issue 2: TimetableTab Navigation
            console.log('\n🔍 ISSUE 2: TimetableTab Navigation Analysis');
            console.log('=' .repeat(50));
            
            // Check if lessons have all required navigation parameters
            console.log('\n📋 Navigation Parameters Check:');
            if (nonFreePeriodLessons.length > 0) {
              const sampleLesson = nonFreePeriodLessons[0];
              const requiredParams = ['lessonReference', 'subject', 'grade'];
              const optionalParams = ['level', 'curriculum', 'country'];
              
              console.log('Required parameters:');
              requiredParams.forEach(param => {
                const value = sampleLesson[param] || sampleLesson[param.replace('Reference', 'Ref')];
                console.log(`  - ${param}: "${value}" ${value ? '✅' : '❌'}`);
              });
              
              console.log('Optional parameters:');
              optionalParams.forEach(param => {
                const value = sampleLesson[param];
                console.log(`  - ${param}: "${value}" ${value ? '✅' : '⚠️'}`);
              });
              
              // Expected navigation URL
              const lessonRef = sampleLesson.lessonReference;
              const studentId = 'andrea_ugono_33305';
              const subject = sampleLesson.subject;
              const grade = sampleLesson.grade;
              
              if (lessonRef && subject && grade) {
                const expectedUrl = `/start-lesson?lessonRef=${lessonRef}&studentId=${studentId}&subject=${encodeURIComponent(subject)}&grade=${grade}`;
                console.log('\n🎯 Expected Navigation URL:');
                console.log(`  ${expectedUrl}`);
              } else {
                console.log('\n❌ Missing required parameters for navigation');
              }
            }
            
            // Summary and recommendations
            console.log('\n📝 DEBUGGING SUMMARY');
            console.log('=' .repeat(50));
            
            const upcomingCount = nonFreePeriodLessons.filter(l => l.status === 'upcoming').length;
            const hasLessonRefs = nonFreePeriodLessons.filter(l => l.lessonReference).length;
            
            console.log(`📊 Lessons with "upcoming" status: ${upcomingCount}/${nonFreePeriodLessons.length}`);
            console.log(`📊 Lessons with lessonReference: ${hasLessonRefs}/${nonFreePeriodLessons.length}`);
            
            if (upcomingCount > 0 && hasLessonRefs > 0) {
              console.log('\n✅ API DATA LOOKS GOOD - Issue likely in frontend:');
              console.log('  1. Check isStudent detection in SummaryTab');
              console.log('  2. Verify auth.currentUser?.uid === studentId logic');
              console.log('  3. Check if effectiveStatus calculation is working');
              console.log('  4. Verify TimetableTab handleJoinLesson function');
            } else {
              console.log('\n❌ API DATA ISSUES FOUND:');
              if (upcomingCount === 0) {
                console.log('  - No lessons with "upcoming" status');
              }
              if (hasLessonRefs === 0) {
                console.log('  - No lessons with lessonReference field');
              }
            }
            
            resolve(response);
          } else {
            console.error('❌ API Error:', response.error);
            reject(new Error(response.error));
          }
        } catch (error) {
          console.error('❌ JSON Parse Error:', error.message);
          console.log('Raw response (first 200 chars):', data.substring(0, 200));
          reject(error);
        }
      });
    });
    
    req.on('error', (error) => {
      console.error('❌ Request Error:', error.message);
      reject(error);
    });
    
    req.setTimeout(15000, () => {
      console.error('❌ Request timeout');
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Run the debug test
debugButtonIssues()
  .then(() => {
    console.log('\n🎉 Debug analysis completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Debug analysis failed:', error.message);
    process.exit(1);
  });
