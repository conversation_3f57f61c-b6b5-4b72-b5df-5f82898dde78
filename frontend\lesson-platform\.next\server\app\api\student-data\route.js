/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/student-data/route";
exports.ids = ["app/api/student-data/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudent-data%2Froute&page=%2Fapi%2Fstudent-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudent-data%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudent-data%2Froute&page=%2Fapi%2Fstudent-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudent-data%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_student_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/student-data/route.ts */ \"(rsc)/./src/app/api/student-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/student-data/route\",\n        pathname: \"/api/student-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/student-data/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\api\\\\student-data\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_pc_OneDrive_Desktop_Desktop_Solynta_Website_frontend_lesson_platform_src_app_api_student_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudent-data%2Froute&page=%2Fapi%2Fstudent-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudent-data%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/student-data/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/student-data/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase-admin */ \"(rsc)/./src/lib/firebase-admin.ts\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\");\n/* harmony import */ var next_auth_jwt__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__);\n// src/app/api/student-data/route.ts\n\n // Import the initialized db instance directly\n\nasync function GET(request) {\n    const studentId = request.nextUrl.searchParams.get('studentId');\n    const request_id = request.headers.get('x-request-id') || `student-data-req-${Date.now()}`;\n    console.log(`[API /student-data] REQ_ID: ${request_id} - START. StudentId from query: ${studentId}`);\n    // Optional: Server-side session validation using NextAuth token\n    try {\n        const token = await (0,next_auth_jwt__WEBPACK_IMPORTED_MODULE_2__.getToken)({\n            req: request,\n            secret: process.env.NEXTAUTH_SECRET\n        });\n        if (!token) {\n            console.warn(`[API /student-data] REQ_ID: ${request_id} - Unauthorized: No NextAuth token.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Example authorization: only the student themselves or an admin can access\n        if (token.sub !== studentId && token.role !== 'admin' && token.role !== 'parent') {\n            console.warn(`[API /student-data] REQ_ID: ${request_id} - Forbidden: Token sub (${token.sub}, role ${token.role}) cannot access data for ${studentId}.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Forbidden'\n            }, {\n                status: 403\n            });\n        }\n        console.log(`[API /student-data] REQ_ID: ${request_id} - NextAuth token processed. UID from token: ${token.sub}, Role: ${token.role}`);\n    } catch (tokenError) {\n        console.error(`[API /student-data] REQ_ID: ${request_id} - Error processing NextAuth token: ${tokenError.message}`, tokenError.stack);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Authentication token processing error',\n            details:  true ? tokenError.message : 0,\n            stack:  true ? tokenError.stack : 0\n        }, {\n            status: 500\n        });\n    }\n    if (!studentId) {\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Missing studentId parameter.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Student ID is required'\n        }, {\n            status: 400\n        });\n    }\n    if (!_lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db) {\n        console.error(`[API /student-data] REQ_ID: ${request_id} - CRITICAL: Firestore 'db' instance is NOT AVAILABLE. Check Firebase Admin initialization in @/lib/firebase-admin.ts.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"Server configuration error: Database service unavailable.\"\n        }, {\n            status: 503\n        });\n    }\n    console.log(`[API /student-data] REQ_ID: ${request_id} - Firebase Admin 'db' instance IS available.`);\n    try {\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Attempting to fetch student document for: ${studentId}`);\n        const studentDocRef = _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(studentId);\n        const studentDoc = await studentDocRef.get(); // This is a DocumentSnapshot\n        console.log(`[API /student-data] REQ_ID: ${request_id} - studentDoc.exists for ${studentId}: ${studentDoc.exists}`);\n        if (!studentDoc.exists) {\n            console.log(`[API /student-data] REQ_ID: ${request_id} - Student document NOT FOUND for studentId: ${studentId}.`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Student not found'\n            }, {\n                status: 404\n            });\n        }\n        const studentData = studentDoc.data(); // Cast to your defined type\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Found student data for ${studentId}.`);\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Attempting to fetch active enrollments for: ${studentId}`);\n        const enrollmentsSnap = await _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(studentId).collection('enrollments').where('status', '==', 'active').get();\n        const enrollments = enrollmentsSnap.docs.map((doc)=>({\n                id: doc.id,\n                ...doc.data()\n            }));\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Fetched ${enrollments.length} active enrollments for ${studentId}.`);\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Attempting to fetch timetable (doc 'active_schedule') for: ${studentId}`);\n        const timetableDoc = await _lib_firebase_admin__WEBPACK_IMPORTED_MODULE_1__.db.collection('students').doc(studentId).collection('timetables').doc('active_schedule').get(); // This is a DocumentSnapshot\n        // ============================================================\n        // CORRECTED USAGE OF .exists PROPERTY\n        // ============================================================\n        const timetable = timetableDoc.exists ? timetableDoc.data() : null;\n        // ============================================================\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Timetable document 'active_schedule' for ${studentId}: ${timetableDoc.exists ? 'Found' : 'Not found'}.`);\n        console.log(`[API /student-data] REQ_ID: ${request_id} - Successfully fetched all data for ${studentId}. Constructing response.`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Student data retrieved successfully\",\n            student: studentData,\n            enrollments: enrollments,\n            timetable: timetable\n        }, {\n            status: 200\n        });\n    } catch (error) {\n        console.error(`[API /student-data] REQ_ID: ${request_id} - UNHANDLED EXCEPTION for student ${studentId}: ${error.message}`, error.stack);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'An internal server error occurred while fetching student data.',\n            details:  true ? error.message : 0,\n            stack:  true ? error.stack : 0\n        }, {\n            status: 500\n        });\n    }\n} // Optional: Add an OPTIONS handler for CORS preflight requests if your middleware doesn't handle it globally for all API routes.\n // However, Next.js App Router usually handles basic CORS for API routes well if configured in next.config.js or middleware.\n // If you encounter CORS issues specifically with this route, you can add it.\n /*\r\nexport async function OPTIONS(request: NextRequest) {\r\n  return new NextResponse(null, {\r\n    status: 204,\r\n    headers: {\r\n      'Access-Control-Allow-Origin': '*', // Or your specific frontend origin\r\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\r\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Request-ID', // Add any custom headers you use\r\n    },\r\n  });\r\n}\r\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/student-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib sync recursive":
/*!***********************!*\
  !*** ./src/lib/ sync ***!
  \***********************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./src/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./src/lib/firebase-admin.ts":
/*!***********************************!*\
  !*** ./src/lib/firebase-admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   firebaseAdminApp: () => (/* binding */ firebaseAdminApp),\n/* harmony export */   getDb: () => (/* binding */ getDb),\n/* harmony export */   initFirebaseAdmin: () => (/* binding */ initFirebaseAdmin)\n/* harmony export */ });\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase-admin */ \"firebase-admin\");\n/* harmony import */ var firebase_admin__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(firebase_admin__WEBPACK_IMPORTED_MODULE_0__);\n// src/lib/firebase-admin.ts\n\n// Get environment variables\nconst projectId = process.env.FIREBASE_PROJECT_ID;\nconst clientEmail = process.env.FIREBASE_CLIENT_EMAIL;\n// IMPORTANT: Replace newline characters in the private key if stored in .env\nconst privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\\\n/g, '\\n');\nif (!(firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length) {\n    console.log('[Firebase Admin Lib] No existing Firebase Admin app. Attempting to initialize...'); // Log attempt\n    if (projectId && clientEmail && privateKey) {\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert({\n                projectId,\n                clientEmail,\n                privateKey\n            })\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${projectId}`);\n    } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64) {\n        const decodedKey = Buffer.from(process.env.FIREBASE_SERVICE_ACCOUNT_KEY_BASE64, 'base64').toString('utf-8');\n        const serviceAccount = JSON.parse(decodedKey);\n        firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n            credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n        });\n        console.log(`[Firebase Admin Lib] Successfully initialized. Project ID: ${serviceAccount.project_id}`);\n    } else {\n        // Fallback to service account key file\n        try {\n            const path = __webpack_require__(/*! path */ \"path\");\n            const serviceAccountPath = path.join(process.cwd(), 'service-account-key.json');\n            const serviceAccount = __webpack_require__(\"(rsc)/./src/lib sync recursive\")(serviceAccountPath);\n            firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().initializeApp({\n                credential: firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().credential.cert(serviceAccount)\n            });\n            console.log(`[Firebase Admin Lib] Successfully initialized with service account file. Project ID: ${serviceAccount.project_id}`);\n        } catch (error) {\n            console.warn('[Firebase Admin Lib] Firebase Admin SDK NOT initialized - missing configuration and service account file.');\n            console.error('Error:', error.message);\n        }\n    }\n} else {\n    // This part is fine, just confirms the module was loaded again but used existing app\n    console.log('[Firebase Admin Lib] Firebase Admin SDK already initialized. Using existing app.');\n}\nconst db = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().firestore() : null; // Make db potentially null if init fails\nconst auth = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().auth() : null; // Make auth potentially null if init fails\n// Add other Firebase services if you use them, like storage:\n// export const storage = admin.apps.length ? admin.storage() : null;\nif (db) {\n    console.log('[Firebase Admin Lib] Firestore instance obtained.');\n}\nif (auth) {\n    console.log('[Firebase Admin Lib] Auth instance obtained.');\n}\n// For backwards compatibility with existing code\nconst getDb = async ()=>db;\nconst initFirebaseAdmin = async ()=>{\n    // This function now just returns a resolved promise since initialization happens on import\n    return Promise.resolve();\n};\nconst firebaseAdminApp = (firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().apps).length ? firebase_admin__WEBPACK_IMPORTED_MODULE_0___default().app() : null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/firebase-admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "firebase-admin":
/*!*********************************!*\
  !*** external "firebase-admin" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("firebase-admin");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstudent-data%2Froute&page=%2Fapi%2Fstudent-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstudent-data%2Froute.ts&appDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpc%5COneDrive%5CDesktop%5CDesktop%5CSolynta_Website%5Cfrontend%5Clesson-platform&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();