"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria";
exports.ids = ["vendor-chunks/@react-aria"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-aria/i18n/dist/context.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@react-aria/i18n/dist/context.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nProvider: () => (/* binding */ $18f2051aff69b9bf$export$a54013f0d02a8f82),\n/* harmony export */   useLocale: () => (/* binding */ $18f2051aff69b9bf$export$43bb16f9c6d9e3f7)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@react-aria/i18n/dist/utils.mjs\");\n/* harmony import */ var _useDefaultLocale_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useDefaultLocale.mjs */ \"(ssr)/./node_modules/@react-aria/i18n/dist/useDefaultLocale.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nconst $18f2051aff69b9bf$var$I18nContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nfunction $18f2051aff69b9bf$export$a54013f0d02a8f82(props) {\n    let { locale: locale, children: children } = props;\n    let defaultLocale = (0, _useDefaultLocale_mjs__WEBPACK_IMPORTED_MODULE_1__.useDefaultLocale)();\n    let value = (0, react__WEBPACK_IMPORTED_MODULE_0__).useMemo(()=>{\n        if (!locale) return defaultLocale;\n        return {\n            locale: locale,\n            direction: (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_2__.isRTL)(locale) ? 'rtl' : 'ltr'\n        };\n    }, [\n        defaultLocale,\n        locale\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($18f2051aff69b9bf$var$I18nContext.Provider, {\n        value: value\n    }, children);\n}\nfunction $18f2051aff69b9bf$export$43bb16f9c6d9e3f7() {\n    let defaultLocale = (0, _useDefaultLocale_mjs__WEBPACK_IMPORTED_MODULE_1__.useDefaultLocale)();\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($18f2051aff69b9bf$var$I18nContext);\n    return context || defaultLocale;\n}\n\n\n\n//# sourceMappingURL=context.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/i18n/dist/context.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/i18n/dist/useDefaultLocale.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@react-aria/i18n/dist/useDefaultLocale.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaultLocale: () => (/* binding */ $1e5a04cdaf7d1af8$export$f09106e7c6677ec5),\n/* harmony export */   useDefaultLocale: () => (/* binding */ $1e5a04cdaf7d1af8$export$188ec29ebc2bdc3a)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/@react-aria/i18n/dist/utils.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n// Locale passed from server by PackageLocalizationProvider.\nconst $1e5a04cdaf7d1af8$var$localeSymbol = Symbol.for('react-aria.i18n.locale');\nfunction $1e5a04cdaf7d1af8$export$f09106e7c6677ec5() {\n    let locale = typeof window !== 'undefined' && window[$1e5a04cdaf7d1af8$var$localeSymbol] || typeof navigator !== 'undefined' && (navigator.language || navigator.userLanguage) || 'en-US';\n    try {\n        Intl.DateTimeFormat.supportedLocalesOf([\n            locale\n        ]);\n    } catch  {\n        locale = 'en-US';\n    }\n    return {\n        locale: locale,\n        direction: (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_1__.isRTL)(locale) ? 'rtl' : 'ltr'\n    };\n}\nlet $1e5a04cdaf7d1af8$var$currentLocale = $1e5a04cdaf7d1af8$export$f09106e7c6677ec5();\nlet $1e5a04cdaf7d1af8$var$listeners = new Set();\nfunction $1e5a04cdaf7d1af8$var$updateLocale() {\n    $1e5a04cdaf7d1af8$var$currentLocale = $1e5a04cdaf7d1af8$export$f09106e7c6677ec5();\n    for (let listener of $1e5a04cdaf7d1af8$var$listeners)listener($1e5a04cdaf7d1af8$var$currentLocale);\n}\nfunction $1e5a04cdaf7d1af8$export$188ec29ebc2bdc3a() {\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__.useIsSSR)();\n    let [defaultLocale, setDefaultLocale] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)($1e5a04cdaf7d1af8$var$currentLocale);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if ($1e5a04cdaf7d1af8$var$listeners.size === 0) window.addEventListener('languagechange', $1e5a04cdaf7d1af8$var$updateLocale);\n        $1e5a04cdaf7d1af8$var$listeners.add(setDefaultLocale);\n        return ()=>{\n            $1e5a04cdaf7d1af8$var$listeners.delete(setDefaultLocale);\n            if ($1e5a04cdaf7d1af8$var$listeners.size === 0) window.removeEventListener('languagechange', $1e5a04cdaf7d1af8$var$updateLocale);\n        };\n    }, []);\n    // We cannot determine the browser's language on the server, so default to\n    // en-US. This will be updated after hydration on the client to the correct value.\n    if (isSSR) return {\n        locale: 'en-US',\n        direction: 'ltr'\n    };\n    return defaultLocale;\n}\n\n\n\n//# sourceMappingURL=useDefaultLocale.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/i18n/dist/useDefaultLocale.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/i18n/dist/utils.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@react-aria/i18n/dist/utils.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isRTL: () => (/* binding */ $148a7a147e38ea7f$export$702d680b21cbd764)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // https://en.wikipedia.org/wiki/Right-to-left\nconst $148a7a147e38ea7f$var$RTL_SCRIPTS = new Set([\n    'Arab',\n    'Syrc',\n    'Samr',\n    'Mand',\n    'Thaa',\n    'Mend',\n    'Nkoo',\n    'Adlm',\n    'Rohg',\n    'Hebr'\n]);\nconst $148a7a147e38ea7f$var$RTL_LANGS = new Set([\n    'ae',\n    'ar',\n    'arc',\n    'bcc',\n    'bqi',\n    'ckb',\n    'dv',\n    'fa',\n    'glk',\n    'he',\n    'ku',\n    'mzn',\n    'nqo',\n    'pnb',\n    'ps',\n    'sd',\n    'ug',\n    'ur',\n    'yi'\n]);\nfunction $148a7a147e38ea7f$export$702d680b21cbd764(localeString) {\n    // If the Intl.Locale API is available, use it to get the locale's text direction.\n    if (Intl.Locale) {\n        let locale = new Intl.Locale(localeString).maximize();\n        // Use the text info object to get the direction if possible.\n        // @ts-ignore - this was implemented as a property by some browsers before it was standardized as a function.\n        // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/getTextInfo\n        let textInfo = typeof locale.getTextInfo === 'function' ? locale.getTextInfo() : locale.textInfo;\n        if (textInfo) return textInfo.direction === 'rtl';\n        // Fallback: guess using the script.\n        // This is more accurate than guessing by language, since languages can be written in multiple scripts.\n        if (locale.script) return $148a7a147e38ea7f$var$RTL_SCRIPTS.has(locale.script);\n    }\n    // If not, just guess by the language (first part of the locale)\n    let lang = localeString.split('-')[0];\n    return $148a7a147e38ea7f$var$RTL_LANGS.has(lang);\n}\n\n\n\n//# sourceMappingURL=utils.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/i18n/dist/utils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/overlays/dist/useModal.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@react-aria/overlays/dist/useModal.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModalProvider: () => (/* binding */ $f57aed4a881a3485$export$178405afcd8c5eb),\n/* harmony export */   OverlayContainer: () => (/* binding */ $f57aed4a881a3485$export$b47c3594eab58386),\n/* harmony export */   OverlayProvider: () => (/* binding */ $f57aed4a881a3485$export$bf688221f59024e5),\n/* harmony export */   useModal: () => (/* binding */ $f57aed4a881a3485$export$33ffd74ebf07f060),\n/* harmony export */   useModalProvider: () => (/* binding */ $f57aed4a881a3485$export$d9aaed4c3ece1bc0)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nconst $f57aed4a881a3485$var$Context = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nfunction $f57aed4a881a3485$export$178405afcd8c5eb(props) {\n    let { children: children } = props;\n    let parent = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    let [modalCount, setModalCount] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            parent: parent,\n            modalCount: modalCount,\n            addModal () {\n                setModalCount((count)=>count + 1);\n                if (parent) parent.addModal();\n            },\n            removeModal () {\n                setModalCount((count)=>count - 1);\n                if (parent) parent.removeModal();\n            }\n        }), [\n        parent,\n        modalCount\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$var$Context.Provider, {\n        value: context\n    }, children);\n}\nfunction $f57aed4a881a3485$export$d9aaed4c3ece1bc0() {\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    return {\n        modalProviderProps: {\n            'aria-hidden': context && context.modalCount > 0 ? true : undefined\n        }\n    };\n}\n/**\n * Creates a root node that will be aria-hidden if there are other modals open.\n */ function $f57aed4a881a3485$var$OverlayContainerDOM(props) {\n    let { modalProviderProps: modalProviderProps } = $f57aed4a881a3485$export$d9aaed4c3ece1bc0();\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"div\", {\n        \"data-overlay-container\": true,\n        ...props,\n        ...modalProviderProps\n    });\n}\nfunction $f57aed4a881a3485$export$bf688221f59024e5(props) {\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$export$178405afcd8c5eb, null, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$var$OverlayContainerDOM, props));\n}\nfunction $f57aed4a881a3485$export$b47c3594eab58386(props) {\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__.useIsSSR)();\n    let { portalContainer: portalContainer = isSSR ? null : document.body, ...rest } = props;\n    (0, react__WEBPACK_IMPORTED_MODULE_0__).useEffect(()=>{\n        if (portalContainer === null || portalContainer === void 0 ? void 0 : portalContainer.closest('[data-overlay-container]')) throw new Error('An OverlayContainer must not be inside another container. Please change the portalContainer prop.');\n    }, [\n        portalContainer\n    ]);\n    if (!portalContainer) return null;\n    let contents = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$export$bf688221f59024e5, rest);\n    return /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_1__).createPortal(contents, portalContainer);\n}\nfunction $f57aed4a881a3485$export$33ffd74ebf07f060(options) {\n    // Add aria-hidden to all parent providers on mount, and restore on unmount.\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    if (!context) throw new Error('Modal is not contained within a provider');\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if ((options === null || options === void 0 ? void 0 : options.isDisabled) || !context || !context.parent) return;\n        // The immediate context is from the provider containing this modal, so we only\n        // want to trigger aria-hidden on its parents not on the modal provider itself.\n        context.parent.addModal();\n        return ()=>{\n            if (context && context.parent) context.parent.removeModal();\n        };\n    }, [\n        context,\n        context.parent,\n        options === null || options === void 0 ? void 0 : options.isDisabled\n    ]);\n    return {\n        modalProps: {\n            'data-ismodal': !(options === null || options === void 0 ? void 0 : options.isDisabled)\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useModal.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/overlays/dist/useModal.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/ssr/dist/SSRProvider.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@react-aria/ssr/dist/SSRProvider.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSRProvider: () => (/* binding */ $b5e257d569688ac6$export$9f8ac96af4b1b2ae),\n/* harmony export */   useIsSSR: () => (/* binding */ $b5e257d569688ac6$export$535bd6ca7f90a273),\n/* harmony export */   useSSRSafeId: () => (/* binding */ $b5e257d569688ac6$export$619500959fc48b26)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst $b5e257d569688ac6$var$defaultContext = {\n    prefix: String(Math.round(Math.random() * 10000000000)),\n    current: 0\n};\nconst $b5e257d569688ac6$var$SSRContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext($b5e257d569688ac6$var$defaultContext);\nconst $b5e257d569688ac6$var$IsSSRContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(false);\n// This is only used in React < 18.\nfunction $b5e257d569688ac6$var$LegacySSRProvider(props) {\n    let cur = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    let counter = $b5e257d569688ac6$var$useCounter(cur === $b5e257d569688ac6$var$defaultContext);\n    let [isSSR, setIsSSR] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    let value = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            // If this is the first SSRProvider, start with an empty string prefix, otherwise\n            // append and increment the counter.\n            prefix: cur === $b5e257d569688ac6$var$defaultContext ? '' : `${cur.prefix}-${counter}`,\n            current: 0\n        }), [\n        cur,\n        counter\n    ]);\n    // If on the client, and the component was initially server rendered,\n    // then schedule a layout effect to update the component after hydration.\n    if (typeof document !== 'undefined') // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(()=>{\n        setIsSSR(false);\n    }, []);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$SSRContext.Provider, {\n        value: value\n    }, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$IsSSRContext.Provider, {\n        value: isSSR\n    }, props.children));\n}\nlet $b5e257d569688ac6$var$warnedAboutSSRProvider = false;\nfunction $b5e257d569688ac6$export$9f8ac96af4b1b2ae(props) {\n    if (typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useId'] === 'function') {\n        if ( true && !$b5e257d569688ac6$var$warnedAboutSSRProvider) {\n            console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n            $b5e257d569688ac6$var$warnedAboutSSRProvider = true;\n        }\n        return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement((0, react__WEBPACK_IMPORTED_MODULE_0__).Fragment, null, props.children);\n    }\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$LegacySSRProvider, props);\n}\nlet $b5e257d569688ac6$var$canUseDOM = Boolean(typeof window !== 'undefined' && window.document && window.document.createElement);\nlet $b5e257d569688ac6$var$componentIds = new WeakMap();\nfunction $b5e257d569688ac6$var$useCounter(isDisabled = false) {\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    let ref = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // eslint-disable-next-line rulesdir/pure-render\n    if (ref.current === null && !isDisabled) {\n        var _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner, _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n        // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n        // This means our id counter will be incremented twice instead of once. This is a problem because on the\n        // server, components are only rendered once and so ids generated on the server won't match the client.\n        // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n        // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n        // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n        // To ensure that we only increment the global counter once, we store the starting id for this component in\n        // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n        // Since React runs the second render immediately after the first, this is safe.\n        // @ts-ignore\n        let currentOwner = (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = (0, react__WEBPACK_IMPORTED_MODULE_0__).__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === void 0 ? void 0 : (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner = _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner === void 0 ? void 0 : _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner.current;\n        if (currentOwner) {\n            let prevComponentValue = $b5e257d569688ac6$var$componentIds.get(currentOwner);\n            if (prevComponentValue == null) // On the first render, and first call to useId, store the id and state in our weak map.\n            $b5e257d569688ac6$var$componentIds.set(currentOwner, {\n                id: ctx.current,\n                state: currentOwner.memoizedState\n            });\n            else if (currentOwner.memoizedState !== prevComponentValue.state) {\n                // On the second render, the memoizedState gets reset by React.\n                // Reset the counter, and remove from the weak map so we don't\n                // do this for subsequent useId calls.\n                ctx.current = prevComponentValue.id;\n                $b5e257d569688ac6$var$componentIds.delete(currentOwner);\n            }\n        }\n        // eslint-disable-next-line rulesdir/pure-render\n        ref.current = ++ctx.current;\n    }\n    // eslint-disable-next-line rulesdir/pure-render\n    return ref.current;\n}\nfunction $b5e257d569688ac6$var$useLegacySSRSafeId(defaultId) {\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n    // provide a warning to hint to the developer to add one.\n    if (ctx === $b5e257d569688ac6$var$defaultContext && !$b5e257d569688ac6$var$canUseDOM) console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n    let counter = $b5e257d569688ac6$var$useCounter(!!defaultId);\n    let prefix = ctx === $b5e257d569688ac6$var$defaultContext && \"development\" === 'test' ? 0 : `react-aria${ctx.prefix}`;\n    return defaultId || `${prefix}-${counter}`;\n}\nfunction $b5e257d569688ac6$var$useModernSSRSafeId(defaultId) {\n    let id = (0, react__WEBPACK_IMPORTED_MODULE_0__).useId();\n    let [didSSR] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)($b5e257d569688ac6$export$535bd6ca7f90a273());\n    let prefix = didSSR || \"development\" === 'test' ? 'react-aria' : `react-aria${$b5e257d569688ac6$var$defaultContext.prefix}`;\n    return defaultId || `${prefix}-${id}`;\n}\nconst $b5e257d569688ac6$export$619500959fc48b26 = typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useId'] === 'function' ? $b5e257d569688ac6$var$useModernSSRSafeId : $b5e257d569688ac6$var$useLegacySSRSafeId;\nfunction $b5e257d569688ac6$var$getSnapshot() {\n    return false;\n}\nfunction $b5e257d569688ac6$var$getServerSnapshot() {\n    return true;\n}\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction $b5e257d569688ac6$var$subscribe(onStoreChange) {\n    // noop\n    return ()=>{};\n}\nfunction $b5e257d569688ac6$export$535bd6ca7f90a273() {\n    // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n    if (typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useSyncExternalStore'] === 'function') return (0, react__WEBPACK_IMPORTED_MODULE_0__)['useSyncExternalStore']($b5e257d569688ac6$var$subscribe, $b5e257d569688ac6$var$getSnapshot, $b5e257d569688ac6$var$getServerSnapshot);\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$IsSSRContext);\n}\n\n\n\n//# sourceMappingURL=SSRProvider.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/ssr/dist/SSRProvider.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusWithoutScrolling: () => (/* binding */ $7215afc6de606d6b$export$de79e2c695e052f3)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ function $7215afc6de606d6b$export$de79e2c695e052f3(element) {\n    if ($7215afc6de606d6b$var$supportsPreventScroll()) element.focus({\n        preventScroll: true\n    });\n    else {\n        let scrollableElements = $7215afc6de606d6b$var$getScrollableElements(element);\n        element.focus();\n        $7215afc6de606d6b$var$restoreScrollPosition(scrollableElements);\n    }\n}\nlet $7215afc6de606d6b$var$supportsPreventScrollCached = null;\nfunction $7215afc6de606d6b$var$supportsPreventScroll() {\n    if ($7215afc6de606d6b$var$supportsPreventScrollCached == null) {\n        $7215afc6de606d6b$var$supportsPreventScrollCached = false;\n        try {\n            let focusElem = document.createElement('div');\n            focusElem.focus({\n                get preventScroll () {\n                    $7215afc6de606d6b$var$supportsPreventScrollCached = true;\n                    return true;\n                }\n            });\n        } catch  {\n        // Ignore\n        }\n    }\n    return $7215afc6de606d6b$var$supportsPreventScrollCached;\n}\nfunction $7215afc6de606d6b$var$getScrollableElements(element) {\n    let parent = element.parentNode;\n    let scrollableElements = [];\n    let rootScrollingElement = document.scrollingElement || document.documentElement;\n    while(parent instanceof HTMLElement && parent !== rootScrollingElement){\n        if (parent.offsetHeight < parent.scrollHeight || parent.offsetWidth < parent.scrollWidth) scrollableElements.push({\n            element: parent,\n            scrollTop: parent.scrollTop,\n            scrollLeft: parent.scrollLeft\n        });\n        parent = parent.parentNode;\n    }\n    if (rootScrollingElement instanceof HTMLElement) scrollableElements.push({\n        element: rootScrollingElement,\n        scrollTop: rootScrollingElement.scrollTop,\n        scrollLeft: rootScrollingElement.scrollLeft\n    });\n    return scrollableElements;\n}\nfunction $7215afc6de606d6b$var$restoreScrollPosition(scrollableElements) {\n    for (let { element: element, scrollTop: scrollTop, scrollLeft: scrollLeft } of scrollableElements){\n        element.scrollTop = scrollTop;\n        element.scrollLeft = scrollLeft;\n    }\n}\n\n\n\n//# sourceMappingURL=focusWithoutScrolling.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/utils/dist/openLink.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-aria/utils/dist/openLink.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RouterProvider: () => (/* binding */ $ea8dcbcb9ea1b556$export$323e4fc2fa4753fb),\n/* harmony export */   getSyntheticLinkProps: () => (/* binding */ $ea8dcbcb9ea1b556$export$51437d503373d223),\n/* harmony export */   openLink: () => (/* binding */ $ea8dcbcb9ea1b556$export$95185d699e05d4d7),\n/* harmony export */   shouldClientNavigate: () => (/* binding */ $ea8dcbcb9ea1b556$export$efa8c9099e530235),\n/* harmony export */   useLinkProps: () => (/* binding */ $ea8dcbcb9ea1b556$export$7e924b3091a3bd18),\n/* harmony export */   useRouter: () => (/* binding */ $ea8dcbcb9ea1b556$export$9a302a45f65d0572),\n/* harmony export */   useSyntheticLinkProps: () => (/* binding */ $ea8dcbcb9ea1b556$export$bdc77b0c0a3a85d6)\n/* harmony export */ });\n/* harmony import */ var _focusWithoutScrolling_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusWithoutScrolling.mjs */ \"(ssr)/./node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs\");\n/* harmony import */ var _platform_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./platform.mjs */ \"(ssr)/./node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nconst $ea8dcbcb9ea1b556$var$RouterContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    isNative: true,\n    open: $ea8dcbcb9ea1b556$var$openSyntheticLink,\n    useHref: (href)=>href\n});\nfunction $ea8dcbcb9ea1b556$export$323e4fc2fa4753fb(props) {\n    let { children: children, navigate: navigate, useHref: useHref } = props;\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            isNative: false,\n            open: (target, modifiers, href, routerOptions)=>{\n                $ea8dcbcb9ea1b556$var$getSyntheticLink(target, (link)=>{\n                    if ($ea8dcbcb9ea1b556$export$efa8c9099e530235(link, modifiers)) navigate(href, routerOptions);\n                    else $ea8dcbcb9ea1b556$export$95185d699e05d4d7(link, modifiers);\n                });\n            },\n            useHref: useHref || ((href)=>href)\n        }), [\n        navigate,\n        useHref\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($ea8dcbcb9ea1b556$var$RouterContext.Provider, {\n        value: ctx\n    }, children);\n}\nfunction $ea8dcbcb9ea1b556$export$9a302a45f65d0572() {\n    return (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($ea8dcbcb9ea1b556$var$RouterContext);\n}\nfunction $ea8dcbcb9ea1b556$export$efa8c9099e530235(link, modifiers) {\n    // Use getAttribute here instead of link.target. Firefox will default link.target to \"_parent\" when inside an iframe.\n    let target = link.getAttribute('target');\n    return (!target || target === '_self') && link.origin === location.origin && !link.hasAttribute('download') && !modifiers.metaKey && // open in new tab (mac)\n    !modifiers.ctrlKey && // open in new tab (windows)\n    !modifiers.altKey && // download\n    !modifiers.shiftKey;\n}\nfunction $ea8dcbcb9ea1b556$export$95185d699e05d4d7(target, modifiers, setOpening = true) {\n    var _window_event_type, _window_event;\n    let { metaKey: metaKey, ctrlKey: ctrlKey, altKey: altKey, shiftKey: shiftKey } = modifiers;\n    // Firefox does not recognize keyboard events as a user action by default, and the popup blocker\n    // will prevent links with target=\"_blank\" from opening. However, it does allow the event if the\n    // Command/Control key is held, which opens the link in a background tab. This seems like the best we can do.\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=257870 and https://bugzilla.mozilla.org/show_bug.cgi?id=746640.\n    if ((0, _platform_mjs__WEBPACK_IMPORTED_MODULE_1__.isFirefox)() && ((_window_event = window.event) === null || _window_event === void 0 ? void 0 : (_window_event_type = _window_event.type) === null || _window_event_type === void 0 ? void 0 : _window_event_type.startsWith('key')) && target.target === '_blank') {\n        if ((0, _platform_mjs__WEBPACK_IMPORTED_MODULE_1__.isMac)()) metaKey = true;\n        else ctrlKey = true;\n    }\n    // WebKit does not support firing click events with modifier keys, but does support keyboard events.\n    // https://github.com/WebKit/WebKit/blob/c03d0ac6e6db178f90923a0a63080b5ca210d25f/Source/WebCore/html/HTMLAnchorElement.cpp#L184\n    let event = (0, _platform_mjs__WEBPACK_IMPORTED_MODULE_1__.isWebKit)() && (0, _platform_mjs__WEBPACK_IMPORTED_MODULE_1__.isMac)() && !(0, _platform_mjs__WEBPACK_IMPORTED_MODULE_1__.isIPad)() && true ? new KeyboardEvent('keydown', {\n        keyIdentifier: 'Enter',\n        metaKey: metaKey,\n        ctrlKey: ctrlKey,\n        altKey: altKey,\n        shiftKey: shiftKey\n    }) : new MouseEvent('click', {\n        metaKey: metaKey,\n        ctrlKey: ctrlKey,\n        altKey: altKey,\n        shiftKey: shiftKey,\n        bubbles: true,\n        cancelable: true\n    });\n    $ea8dcbcb9ea1b556$export$95185d699e05d4d7.isOpening = setOpening;\n    (0, _focusWithoutScrolling_mjs__WEBPACK_IMPORTED_MODULE_2__.focusWithoutScrolling)(target);\n    target.dispatchEvent(event);\n    $ea8dcbcb9ea1b556$export$95185d699e05d4d7.isOpening = false;\n}\n// https://github.com/parcel-bundler/parcel/issues/8724\n$ea8dcbcb9ea1b556$export$95185d699e05d4d7.isOpening = false;\nfunction $ea8dcbcb9ea1b556$var$getSyntheticLink(target, open) {\n    if (target instanceof HTMLAnchorElement) open(target);\n    else if (target.hasAttribute('data-href')) {\n        let link = document.createElement('a');\n        link.href = target.getAttribute('data-href');\n        if (target.hasAttribute('data-target')) link.target = target.getAttribute('data-target');\n        if (target.hasAttribute('data-rel')) link.rel = target.getAttribute('data-rel');\n        if (target.hasAttribute('data-download')) link.download = target.getAttribute('data-download');\n        if (target.hasAttribute('data-ping')) link.ping = target.getAttribute('data-ping');\n        if (target.hasAttribute('data-referrer-policy')) link.referrerPolicy = target.getAttribute('data-referrer-policy');\n        target.appendChild(link);\n        open(link);\n        target.removeChild(link);\n    }\n}\nfunction $ea8dcbcb9ea1b556$var$openSyntheticLink(target, modifiers) {\n    $ea8dcbcb9ea1b556$var$getSyntheticLink(target, (link)=>$ea8dcbcb9ea1b556$export$95185d699e05d4d7(link, modifiers));\n}\nfunction $ea8dcbcb9ea1b556$export$bdc77b0c0a3a85d6(props) {\n    let router = $ea8dcbcb9ea1b556$export$9a302a45f65d0572();\n    var _props_href;\n    const href = router.useHref((_props_href = props.href) !== null && _props_href !== void 0 ? _props_href : '');\n    return {\n        'data-href': props.href ? href : undefined,\n        'data-target': props.target,\n        'data-rel': props.rel,\n        'data-download': props.download,\n        'data-ping': props.ping,\n        'data-referrer-policy': props.referrerPolicy\n    };\n}\nfunction $ea8dcbcb9ea1b556$export$51437d503373d223(props) {\n    return {\n        'data-href': props.href,\n        'data-target': props.target,\n        'data-rel': props.rel,\n        'data-download': props.download,\n        'data-ping': props.ping,\n        'data-referrer-policy': props.referrerPolicy\n    };\n}\nfunction $ea8dcbcb9ea1b556$export$7e924b3091a3bd18(props) {\n    let router = $ea8dcbcb9ea1b556$export$9a302a45f65d0572();\n    var _props_href;\n    const href = router.useHref((_props_href = props === null || props === void 0 ? void 0 : props.href) !== null && _props_href !== void 0 ? _props_href : '');\n    return {\n        href: (props === null || props === void 0 ? void 0 : props.href) ? href : undefined,\n        target: props === null || props === void 0 ? void 0 : props.target,\n        rel: props === null || props === void 0 ? void 0 : props.rel,\n        download: props === null || props === void 0 ? void 0 : props.download,\n        ping: props === null || props === void 0 ? void 0 : props.ping,\n        referrerPolicy: props === null || props === void 0 ? void 0 : props.referrerPolicy\n    };\n}\n\n\n\n//# sourceMappingURL=openLink.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/utils/dist/openLink.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-aria/utils/dist/platform.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-aria/utils/dist/platform.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isAndroid: () => (/* binding */ $c87311424ea30a05$export$a11b0059900ceec8),\n/* harmony export */   isAppleDevice: () => (/* binding */ $c87311424ea30a05$export$e1865c3bedcd822b),\n/* harmony export */   isChrome: () => (/* binding */ $c87311424ea30a05$export$6446a186d09e379e),\n/* harmony export */   isFirefox: () => (/* binding */ $c87311424ea30a05$export$b7d78993b74f766d),\n/* harmony export */   isIOS: () => (/* binding */ $c87311424ea30a05$export$fedb369cb70207f1),\n/* harmony export */   isIPad: () => (/* binding */ $c87311424ea30a05$export$7bef049ce92e4224),\n/* harmony export */   isIPhone: () => (/* binding */ $c87311424ea30a05$export$186c6964ca17d99),\n/* harmony export */   isMac: () => (/* binding */ $c87311424ea30a05$export$9ac100e40613ea10),\n/* harmony export */   isWebKit: () => (/* binding */ $c87311424ea30a05$export$78551043582a6a98)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ function $c87311424ea30a05$var$testUserAgent(re) {\n    var _window_navigator_userAgentData;\n    if (typeof window === 'undefined' || window.navigator == null) return false;\n    return ((_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.brands.some((brand)=>re.test(brand.brand))) || re.test(window.navigator.userAgent);\n}\nfunction $c87311424ea30a05$var$testPlatform(re) {\n    var _window_navigator_userAgentData;\n    return typeof window !== 'undefined' && window.navigator != null ? re.test(((_window_navigator_userAgentData = window.navigator['userAgentData']) === null || _window_navigator_userAgentData === void 0 ? void 0 : _window_navigator_userAgentData.platform) || window.navigator.platform) : false;\n}\nfunction $c87311424ea30a05$var$cached(fn) {\n    let res = null;\n    return ()=>{\n        if (res == null) res = fn();\n        return res;\n    };\n}\nconst $c87311424ea30a05$export$9ac100e40613ea10 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^Mac/i);\n});\nconst $c87311424ea30a05$export$186c6964ca17d99 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^iPhone/i);\n});\nconst $c87311424ea30a05$export$7bef049ce92e4224 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testPlatform(/^iPad/i) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    $c87311424ea30a05$export$9ac100e40613ea10() && navigator.maxTouchPoints > 1;\n});\nconst $c87311424ea30a05$export$fedb369cb70207f1 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$export$186c6964ca17d99() || $c87311424ea30a05$export$7bef049ce92e4224();\n});\nconst $c87311424ea30a05$export$e1865c3bedcd822b = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$export$9ac100e40613ea10() || $c87311424ea30a05$export$fedb369cb70207f1();\n});\nconst $c87311424ea30a05$export$78551043582a6a98 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/AppleWebKit/i) && !$c87311424ea30a05$export$6446a186d09e379e();\n});\nconst $c87311424ea30a05$export$6446a186d09e379e = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Chrome/i);\n});\nconst $c87311424ea30a05$export$a11b0059900ceec8 = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Android/i);\n});\nconst $c87311424ea30a05$export$b7d78993b74f766d = $c87311424ea30a05$var$cached(function() {\n    return $c87311424ea30a05$var$testUserAgent(/Firefox/i);\n});\n\n\n\n//# sourceMappingURL=platform.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-aria/utils/dist/platform.mjs\n");

/***/ })

};
;