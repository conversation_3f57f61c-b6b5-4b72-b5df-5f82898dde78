"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student-dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/components/dashboard/TimetableTab.tsx":
/*!*******************************************************!*\
  !*** ./src/app/components/dashboard/TimetableTab.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TimetableTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/calendar-days.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/view.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CalendarDays,ChevronRight,Clock,Cpu,Download,Grid,List,RefreshCw,View!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_getDay_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=getDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/getDay.js\");\n/* harmony import */ var _components_LoadingState__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/LoadingState */ \"(app-pages-browser)/./src/components/LoadingState.tsx\");\n/* harmony import */ var _components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ErrorDisplay */ \"(app-pages-browser)/./src/components/ui/ErrorDisplay.tsx\");\n/* harmony import */ var _lib_subject_utils_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/subject-utils-client */ \"(app-pages-browser)/./src/lib/subject-utils-client.ts\");\n/* harmony import */ var _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/LessonProgressService */ \"(app-pages-browser)/./src/services/LessonProgressService.ts\");\n// src/components/student/TimetableTab.tsx\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DAYS_OF_WEEK = [\n    'Monday',\n    'Tuesday',\n    'Wednesday',\n    'Thursday',\n    'Friday'\n];\nconst MAX_LESSONS_PER_WEEK = 35;\n// --- Main Component ---\nfunction TimetableTab(param) {\n    let { studentId: studentIdProp, isParentView = false } = param;\n    _s();\n    // New state management for week-based timetable\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [schedule, setSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [timeSlots, setTimeSlots] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Function to fetch week-specific timetable\n    const fetchWeeklyTimetable = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimetableTab.useCallback[fetchWeeklyTimetable]\": async function(week) {\n            let forceRefresh = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            if (!studentIdProp) return;\n            setIsLoading(!forceRefresh);\n            setIsRefreshing(forceRefresh);\n            setError(null);\n            try {\n                var _result_data;\n                const today = new Date().toISOString().split('T')[0];\n                const response = await fetch(\"/api/timetable?studentId=\".concat(studentIdProp, \"&week=\").concat(week, \"&date=\").concat(today), {\n                    headers: {\n                        'Cache-Control': forceRefresh ? 'no-cache' : 'default'\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch timetable: \".concat(response.statusText));\n                }\n                const result = await response.json();\n                if (result.success && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.schedule)) {\n                    setWeeklySchedule(result.data.schedule);\n                    console.log(\"[TimetableTab] Raw schedule data:\", result.data.schedule);\n                    // Convert the weekly schedule to the expected format\n                    const newSchedule = {};\n                    const newTimeSlots = new Set();\n                    result.data.schedule.forEach({\n                        \"TimetableTab.useCallback[fetchWeeklyTimetable]\": (lesson, index)=>{\n                            const day = lesson.day;\n                            const timeSlot = lesson.time;\n                            console.log(\"[TimetableTab] Processing lesson \".concat(index + 1, \":\"), {\n                                subject: lesson.subject,\n                                day: day,\n                                time: timeSlot,\n                                lessonRef: lesson.lessonReference\n                            });\n                            if (!newSchedule[day]) {\n                                newSchedule[day] = {};\n                                console.log(\"[TimetableTab] Created new day schedule for: \".concat(day));\n                            }\n                            newSchedule[day][timeSlot] = {\n                                subject_id: lesson.subjectId,\n                                subject: lesson.subject,\n                                startTime: timeSlot,\n                                endTime: timeSlot,\n                                lessonReference: lesson.lessonReference,\n                                academicWeek: lesson.academicWeek,\n                                absoluteLessonNumber: lesson.absoluteLessonNumber,\n                                description: lesson.description,\n                                status: lesson.status\n                            };\n                            newTimeSlots.add(timeSlot);\n                        }\n                    }[\"TimetableTab.useCallback[fetchWeeklyTimetable]\"]);\n                    console.log(\"[TimetableTab] Final schedule object:\", newSchedule);\n                    console.log(\"[TimetableTab] Time slots:\", Array.from(newTimeSlots));\n                    setSchedule(newSchedule);\n                    setTimeSlots(Array.from(newTimeSlots).sort());\n                    console.log(\"[TimetableTab] Loaded \".concat(result.data.schedule.length, \" lessons for week \").concat(week));\n                } else {\n                    throw new Error(result.error || 'No schedule data received');\n                }\n            } catch (err) {\n                const errorMessage = err instanceof Error ? err.message : 'Failed to fetch timetable';\n                setError(errorMessage);\n                console.error('[TimetableTab] Error fetching weekly timetable:', err);\n            } finally{\n                setIsLoading(false);\n                setIsRefreshing(false);\n                setIsGenerating(false);\n            }\n        }\n    }[\"TimetableTab.useCallback[fetchWeeklyTimetable]\"], [\n        studentIdProp\n    ]);\n    // UI State - Managed within the component, not the data hook\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [displayMode, setDisplayMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('week');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [academicWeek, setAcademicWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1); // Add week selector state\n    const currentDayIndex = Math.max(0, Math.min(4, (0,_barrel_optimize_names_getDay_date_fns__WEBPACK_IMPORTED_MODULE_9__.getDay)(new Date()) - 1));\n    const [selectedDay, setSelectedDay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DAYS_OF_WEEK[currentDayIndex]);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Add new state for progress data\n    const [lessonProgress, setLessonProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Add function to fetch progress data\n    const fetchProgressData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimetableTab.useCallback[fetchProgressData]\": async ()=>{\n            if (!studentIdProp) return;\n            try {\n                const progressData = await _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__.LessonProgressService.getAllLessonProgress(studentIdProp);\n                setLessonProgress(progressData);\n                console.log('[TimetableTab] Fetched lesson progress data:', progressData);\n            } catch (error) {\n                console.error('[TimetableTab] Error fetching lesson progress:', error);\n            }\n        }\n    }[\"TimetableTab.useCallback[fetchProgressData]\"], [\n        studentIdProp\n    ]);\n    // Effect to fetch timetable when week changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimetableTab.useEffect\": ()=>{\n            if (studentIdProp && academicWeek) {\n                fetchWeeklyTimetable(academicWeek);\n            }\n        }\n    }[\"TimetableTab.useEffect\"], [\n        studentIdProp,\n        academicWeek,\n        fetchWeeklyTimetable\n    ]);\n    // --- Refresh Handler ---\n    const handleRefresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TimetableTab.useCallback[handleRefresh]\": ()=>{\n            if (academicWeek) {\n                fetchWeeklyTimetable(academicWeek, true);\n            }\n        }\n    }[\"TimetableTab.useCallback[handleRefresh]\"], [\n        academicWeek,\n        fetchWeeklyTimetable\n    ]);\n    const getSubjectClass = function() {\n        let subject = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '';\n        const subjectLower = subject.toLowerCase();\n        if (subjectLower.includes('math')) return 'bg-blue-100 border-blue-400 text-blue-800';\n        if (subjectLower.includes('english')) return 'bg-green-100 border-green-400 text-green-800';\n        if (subjectLower.includes('science') || subjectLower.includes('physics') || subjectLower.includes('chemistry') || subjectLower.includes('biology') || subjectLower.includes('basic science')) return 'bg-purple-100 border-purple-400 text-purple-800';\n        if (subjectLower.includes('social') || subjectLower.includes('history') || subjectLower.includes('geography') || subjectLower.includes('national values') || subjectLower.includes('civic')) return 'bg-amber-100 border-amber-400 text-amber-800';\n        if (subjectLower.includes('computer') || subjectLower.includes('computing') || subjectLower.includes('ict') || subjectLower.includes('artificial intelligence')) return 'bg-cyan-100 border-cyan-400 text-cyan-800';\n        if (subjectLower.includes('art') || subjectLower.includes('creative')) return 'bg-pink-100 border-pink-400 text-pink-800';\n        if (subjectLower.includes('entrepreneurship') || subjectLower.includes('business')) return 'bg-indigo-100 border-indigo-400 text-indigo-800';\n        if (subjectLower.includes('financial') || subjectLower.includes('literacy') || subjectLower.includes('economics')) return 'bg-yellow-100 border-yellow-400 text-yellow-800';\n        if (subjectLower.includes('physical') || subjectLower.includes('health')) return 'bg-lime-100 border-lime-400 text-lime-800';\n        if (subjectLower.includes('project') || subjectLower.includes('excellence')) return 'bg-teal-100 border-teal-400 text-teal-800';\n        if (subjectLower.includes('language') && !subjectLower.includes('english')) return 'bg-orange-100 border-orange-400 text-orange-800';\n        return 'bg-gray-100 border-gray-400 text-gray-800';\n    };\n    const getStatusBadgeClass = (status)=>{\n        switch(status){\n            case 'completed':\n                return 'bg-green-100 text-green-800';\n            case 'in_progress':\n                return 'bg-blue-100 text-blue-800';\n            case 'upcoming':\n                return 'bg-gray-100 text-gray-800';\n            case 'past':\n                return 'bg-red-100 text-red-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // --- Handle Join Lesson ---\n    const handleJoinLesson = async (lesson)=>{\n        try {\n            if (!lesson.lessonRef && !lesson.lessonReference) {\n                throw new Error('Missing lesson reference');\n            }\n            const lessonRef = lesson.lessonRef || lesson.lessonReference;\n            console.log('[TimetableTab] Joining lesson with reference:', lessonRef);\n            // Navigate to start-lesson page with proper parameters\n            const params = {\n                lessonRef: lessonRef,\n                studentId: studentIdProp,\n                subject: lesson.subject || '',\n                grade: lesson.grade || '',\n                level: lesson.level || '',\n                curriculum: lesson.curriculum || '',\n                country: lesson.country || ''\n            };\n            // Validate critical parameters\n            const missingCriticalParams = [\n                'lessonRef',\n                'studentId'\n            ].filter((key)=>!params[key]);\n            if (missingCriticalParams.length > 0) {\n                throw new Error(\"Cannot start lesson: Missing critical information (\".concat(missingCriticalParams.join(', '), \")\"));\n            }\n            const queryParams = new URLSearchParams(params).toString();\n            console.log('[TimetableTab] Navigating to start-lesson with params:', params);\n            router.push(\"/start-lesson?\".concat(queryParams));\n        } catch (error) {\n            console.error('Failed to join lesson:', error);\n            // Use toast notification if available\n            if ( true && window.alert) {\n                window.alert(\"Failed to join lesson: \".concat(error.message));\n            }\n        }\n    };\n    // Effect to fetch progress data when component loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TimetableTab.useEffect\": ()=>{\n            fetchProgressData();\n        }\n    }[\"TimetableTab.useEffect\"], [\n        fetchProgressData\n    ]);\n    // --- Weekly Summary Calculation ---\n    const weeklySummary = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"TimetableTab.useMemo[weeklySummary]\": ()=>{\n            if (Object.keys(schedule).length === 0) return {\n                sortedSubjects: [],\n                totalLessons: 0\n            };\n            const subjectCounts = {};\n            let totalLessons = 0;\n            DAYS_OF_WEEK.forEach({\n                \"TimetableTab.useMemo[weeklySummary]\": (day)=>{\n                    const daySchedule = schedule[day] || {};\n                    Object.values(daySchedule).forEach({\n                        \"TimetableTab.useMemo[weeklySummary]\": (entry)=>{\n                            // Check if it's a valid lesson object AND NOT a free/study period\n                            if (typeof entry === 'object' && // Is it an object?\n                            entry !== null && // Is it not null?\n                            entry.subject && // Does it have a subject name?\n                            entry.subject_id !== 'free_period' && // Explicitly exclude free periods by ID\n                            entry.subject !== 'Free Period' // Also check subject name\n                            ) {\n                                // It's a valid lesson to count\n                                subjectCounts[entry.subject] = (subjectCounts[entry.subject] || 0) + 1;\n                                totalLessons++;\n                            }\n                        }\n                    }[\"TimetableTab.useMemo[weeklySummary]\"]);\n                }\n            }[\"TimetableTab.useMemo[weeklySummary]\"]);\n            const sortedSubjects = Object.entries(subjectCounts).map({\n                \"TimetableTab.useMemo[weeklySummary].sortedSubjects\": (param)=>{\n                    let [name, count] = param;\n                    return {\n                        name,\n                        count\n                    };\n                }\n            }[\"TimetableTab.useMemo[weeklySummary].sortedSubjects\"]).sort({\n                \"TimetableTab.useMemo[weeklySummary].sortedSubjects\": (a, b)=>b.count - a.count\n            }[\"TimetableTab.useMemo[weeklySummary].sortedSubjects\"]);\n            return {\n                sortedSubjects,\n                totalLessons\n            };\n        }\n    }[\"TimetableTab.useMemo[weeklySummary]\"], [\n        schedule\n    ]);\n    // --- Loading and Error States Rendering ---\n    // Use states returned from the hook\n    if (isGenerating) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        message: \"Generating timetable, please wait...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n        lineNumber: 307,\n        columnNumber: 28\n    }, this);\n    if (isRefreshing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        message: \"Refreshing timetable...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n        lineNumber: 308,\n        columnNumber: 28\n    }, this);\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        message: \"Loading timetable...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n        lineNumber: 309,\n        columnNumber: 25\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        title: \"Timetable Error\",\n        message: error,\n        onAction: handleRefresh,\n        actionText: \"Retry / Regenerate Timetable\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n        lineNumber: 310,\n        columnNumber: 21\n    }, this);\n    const isScheduleEffectivelyEmpty = Object.keys(schedule).length === 0 || DAYS_OF_WEEK.every((day)=>Object.values(schedule[day] || {}).every((entry)=>typeof entry !== 'object'));\n    if (isScheduleEffectivelyEmpty && !isLoading && !isGenerating && !isRefreshing) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ErrorDisplay__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            title: \"Timetable Unavailable\",\n            message: \"No timetable data could be loaded or generated.\",\n            onAction: handleRefresh,\n            actionText: \"Refresh Timetable\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n            lineNumber: 314,\n            columnNumber: 14\n        }, this);\n    }\n    // --- RENDER LOGIC ---\n    // Parent View\n    if (isParentView) {\n        // Simplified rendering for parent view (Grid only)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold tracking-tight\",\n                                    children: \"Child's Timetable\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 24\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        \"Week \",\n                                        academicWeek,\n                                        \" of 30 - Viewing your child's weekly class schedule\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 24\n                                }, this),\n                                weeklySchedule.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mt-1\",\n                                    children: [\n                                        weeklySchedule.length,\n                                        \" lessons scheduled this week\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 26\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 20\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"parent-week-select\",\n                                    className: \"text-sm font-medium text-muted-foreground\",\n                                    children: \"Week:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 22\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"parent-week-select\",\n                                    value: academicWeek,\n                                    onChange: (e)=>setAcademicWeek(parseInt(e.target.value)),\n                                    disabled: isLoading || isRefreshing || isGenerating,\n                                    className: \"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium min-w-[80px] bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                    children: Array.from({\n                                        length: 30\n                                    }, (_, i)=>i + 1).map((week)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: week,\n                                            children: [\n                                                \"Week \",\n                                                week\n                                            ]\n                                        }, week, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 26\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 22\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 20\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 18\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full border-collapse min-w-[800px]\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"bg-muted\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"p-3 border-r text-left font-medium text-muted-foreground sticky left-0 bg-muted z-10\",\n                                                children: \"Time\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 30\n                                            }, this),\n                                            DAYS_OF_WEEK.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 text-center font-medium\",\n                                                    children: day\n                                                }, day, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 56\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 26\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 24\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: timeSlots.length > 0 ? timeSlots.map((timeSlotKey, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"p-3 border-r text-sm text-muted-foreground font-medium whitespace-nowrap sticky left-0 bg-inherit z-10\",\n                                                    children: timeSlotKey\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 32\n                                                }, this),\n                                                DAYS_OF_WEEK.map((day)=>{\n                                                    const daySchedule = schedule[day] || {};\n                                                    const entry = daySchedule[timeSlotKey];\n                                                    const isLesson = typeof entry === 'object';\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-2 text-center border-l h-24 align-top\",\n                                                        children: isLesson ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded border \".concat(getSubjectClass(entry.subject), \" h-full flex flex-col justify-between text-xs sm:text-sm\"),\n                                                            children: [\n                                                                \"                                           \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold\",\n                                                                            children: entry.subject\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 46\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-gray-600 mt-1\",\n                                                                            children: entry.teacher || \"AI Instructor\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 380,\n                                                                            columnNumber: 46\n                                                                        }, this),\n                                                                        entry.lessonReference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: entry.lessonReference\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                    lineNumber: 384,\n                                                                                    columnNumber: 50\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-1 mt-1\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"inline-block px-1 py-0.5 rounded-sm text-xs \".concat(getStatusBadgeClass(entry.status || 'upcoming')),\n                                                                                            children: entry.status === 'completed' ? 'Done' : entry.status === 'in_progress' ? 'Active' : entry.status === 'past' ? 'Past' : 'Upcoming'\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                            lineNumber: 386,\n                                                                                            columnNumber: 52\n                                                                                        }, this),\n                                                                                        (entry.cognitiveLoad || entry.cognitive_load) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800\",\n                                                                                            children: [\n                                                                                                \"CL: \",\n                                                                                                entry.cognitiveLoad || entry.cognitive_load\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                            lineNumber: 393,\n                                                                                            columnNumber: 54\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                    lineNumber: 385,\n                                                                                    columnNumber: 50\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 48\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 211\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 40\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-400 h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Free\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 40\n                                                        }, this)\n                                                    }, \"\".concat(day, \"-\").concat(timeSlotKey), false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 36\n                                                    }, this);\n                                                })\n                                            ]\n                                        }, timeSlotKey, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 30\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            colSpan: 6,\n                                            className: \"p-6 text-center text-muted-foreground\",\n                                            children: \"Timetable data is loading or unavailable.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 36\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 32\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 24\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 19\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"Weekly Lesson Summary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 419,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3 font-medium \".concat(weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK ? 'text-red-600' : 'text-gray-700'),\n                                    children: [\n                                        \"Total Lessons Scheduled: \",\n                                        weeklySummary.totalLessons,\n                                        \" / \",\n                                        MAX_LESSONS_PER_WEEK\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 23\n                                }, this),\n                                weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                                    variant: \"destructive\",\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 74\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AlertDescription, {\n                                            children: \"Lesson limit exceeded.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 109\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 28\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: weeklySummary.sortedSubjects.map((sub)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: sub.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 30\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"secondary\",\n                                                    children: [\n                                                        sub.count,\n                                                        \" lesson\",\n                                                        sub.count > 1 ? 's' : ''\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 30\n                                                }, this)\n                                            ]\n                                        }, sub.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 28\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 23\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                    lineNumber: 418,\n                    columnNumber: 18\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n            lineNumber: 324,\n            columnNumber: 13\n        }, this);\n    }\n    // --- STUDENT VIEW ---\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold tracking-tight\",\n                                children: \"Weekly Timetable\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 16\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Week \",\n                                    academicWeek,\n                                    \" of 30 - Your class schedule based on your enrollments\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 16\n                            }, this),\n                            weeklySchedule.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600 mt-1\",\n                                children: [\n                                    weeklySchedule.length,\n                                    \" lessons scheduled this week\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 12\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"week-select\",\n                                        className: \"text-sm font-medium text-muted-foreground\",\n                                        children: \"Week:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"week-select\",\n                                        value: academicWeek,\n                                        onChange: (e)=>setAcademicWeek(parseInt(e.target.value)),\n                                        disabled: isLoading || isRefreshing || isGenerating,\n                                        className: \"px-3 py-1 border border-gray-300 rounded-md text-sm font-medium min-w-[80px] bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: Array.from({\n                                            length: 30\n                                        }, (_, i)=>i + 1).map((week)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: week,\n                                                children: [\n                                                    \"Week \",\n                                                    week\n                                                ]\n                                            }, week, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: handleRefresh,\n                                disabled: isRefreshing || isLoading || isGenerating,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isRefreshing || isGenerating ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 19\n                                    }, this),\n                                    isGenerating ? 'Generating...' : isRefreshing ? 'Refreshing...' : 'Refresh'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 480,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center border rounded-md p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        \"aria-label\": \"View Weekly Timetable\",\n                                        variant: displayMode === 'week' ? 'secondary' : 'ghost',\n                                        size: \"sm\",\n                                        className: \"px-2 py-1 h-auto text-xs\",\n                                        onClick: ()=>setDisplayMode('week'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 24\n                                            }, this),\n                                            \" Week\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 22\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        \"aria-label\": \"View Daily Timetable\",\n                                        variant: displayMode === 'day' ? 'secondary' : 'ghost',\n                                        size: \"sm\",\n                                        className: \"px-2 py-1 h-auto text-xs\",\n                                        onClick: ()=>setDisplayMode('day'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-3 w-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 24\n                                            }, this),\n                                            \" Day\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setViewMode(viewMode === 'grid' ? 'list' : 'grid'),\n                                children: [\n                                    viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 43\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 79\n                                    }, this),\n                                    viewMode === 'grid' ? 'List View' : 'Grid View'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>{},\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Download\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 12\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                lineNumber: 446,\n                columnNumber: 8\n            }, this),\n            displayMode === 'day' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap justify-center gap-2 mb-4\",\n                children: DAYS_OF_WEEK.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: selectedDay === day ? 'default' : 'outline',\n                        size: \"sm\",\n                        onClick: ()=>setSelectedDay(day),\n                        className: \"flex-grow sm:flex-grow-0\",\n                        children: day\n                    }, day, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 15\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                lineNumber: 507,\n                columnNumber: 11\n            }, this),\n            displayMode === 'week' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-0 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                className: \"w-full border-collapse min-w-[800px]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"bg-muted\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    className: \"p-3 border-r text-left font-medium text-muted-foreground sticky left-0 bg-muted z-10\",\n                                                    children: \"Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 54\n                                                }, this),\n                                                DAYS_OF_WEEK.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"p-3 text-center font-medium\",\n                                                        children: day\n                                                    }, day, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 190\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 22\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: timeSlots.length > 0 ? timeSlots.map((timeSlotKey, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: index % 2 === 0 ? 'bg-white' : 'bg-gray-50',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        className: \"p-3 border-r text-sm text-muted-foreground font-medium whitespace-nowrap sticky left-0 bg-inherit z-10\",\n                                                        children: timeSlotKey\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 171\n                                                    }, this),\n                                                    DAYS_OF_WEEK.map((day)=>{\n                                                        const dS = schedule[day] || {};\n                                                        const e = dS[timeSlotKey];\n                                                        const iL = typeof e === 'object';\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"p-2 text-center border-l h-20 align-top\",\n                                                            children: iL ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded border \".concat(getSubjectClass(e.subject), \" h-full flex flex-col justify-between text-xs sm:text-sm\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: e.subject\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: e.teacher || \"AI Instructor\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                lineNumber: 531,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            e.lessonReference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: e.lessonReference\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                        lineNumber: 535,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex flex-wrap gap-1 mt-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block px-1 py-0.5 rounded-sm text-xs \".concat(getStatusBadgeClass(e.status || 'upcoming')),\n                                                                                                children: e.status === 'completed' ? 'Completed' : e.status === 'in_progress' ? 'In Progress' : e.status === 'past' ? 'Past' : 'Upcoming'\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                                lineNumber: 537,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            (e.cognitiveLoad || e.cognitive_load) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800\",\n                                                                                                children: [\n                                                                                                    \"CL: \",\n                                                                                                    e.cognitiveLoad || e.cognitive_load\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                                lineNumber: 544,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                        lineNumber: 536,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    e.lessonReference && e.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        className: \"mt-2 text-xs h-6 px-2\",\n                                                                        onClick: (event)=>{\n                                                                            event.stopPropagation();\n                                                                            handleJoinLesson(e);\n                                                                        },\n                                                                        children: \"Join Lesson\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400 h-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Free Period\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, \"\".concat(day, \"-\").concat(timeSlotKey), false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 431\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, timeSlotKey, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 93\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                colSpan: 6,\n                                                className: \"p-6 text-center\",\n                                                children: \"Loading...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 49\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 45\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 524,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 15\n                    }, this),\n                    viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: DAYS_OF_WEEK.map((day)=>{\n                            const dayScheduleMap = schedule[day] || {};\n                            const lessons = Object.entries(dayScheduleMap).sort((param, param1)=>{\n                                let [keyA] = param, [keyB] = param1;\n                                return keyA.localeCompare(keyB);\n                            }).filter((param)=>{\n                                let [, value] = param;\n                                return typeof value === 'object';\n                            }).map((param)=>{\n                                let [, value] = param;\n                                return value;\n                            });\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"pb-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: day\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 590,\n                                            columnNumber: 26\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 24\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: lessons.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: lessons.map((lesson, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded border \".concat(getSubjectClass(lesson.subject), \" flex justify-between items-center\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: lesson.subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 36\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs mt-1\",\n                                                                    children: lesson.teacher || \"AI Instructor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 599,\n                                                                    columnNumber: 36\n                                                                }, this),\n                                                                lesson.lessonReference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: lesson.lessonReference\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 40\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-wrap gap-1 mt-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-block px-1 py-0.5 rounded-sm text-xs \".concat(getStatusBadgeClass(lesson.status || 'upcoming')),\n                                                                                    children: lesson.status === 'completed' ? 'Completed' : lesson.status === 'in_progress' ? 'In Progress' : lesson.status === 'past' ? 'Past' : 'Upcoming'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                    lineNumber: 604,\n                                                                                    columnNumber: 42\n                                                                                }, this),\n                                                                                (lesson.cognitiveLoad || lesson.cognitive_load) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800\",\n                                                                                    children: [\n                                                                                        \"CL: \",\n                                                                                        lesson.cognitiveLoad || lesson.cognitive_load\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 44\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 603,\n                                                                            columnNumber: 40\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 38\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 34\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 38\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: lesson.startTime\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 622,\n                                                                            columnNumber: 38\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 36\n                                                                }, this),\n                                                                lesson.lessonReference && lesson.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleJoinLesson(lesson),\n                                                                    children: \"Join Lesson\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 38\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4 cursor-pointer\",\n                                                                    onClick: ()=>{\n                                                                        const subjectId = lesson.subject_id || (0,_lib_subject_utils_client__WEBPACK_IMPORTED_MODULE_7__.normalizeSubjectName)(lesson.subject).toLowerCase().replace(/\\s+/g, '_');\n                                                                        router.push(\"/subjects/\".concat(subjectId, \"/lessons?studentId=\").concat(studentIdProp));\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 36\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 34\n                                                        }, this)\n                                                    ]\n                                                }, \"\".concat(day, \"-l-\").concat(idx), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 32\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 28\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4\",\n                                            children: \"No classes scheduled\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 28\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, day, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 22\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 16\n                    }, this)\n                ]\n            }, void 0, true),\n            displayMode === 'day' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            children: [\n                                selectedDay,\n                                \"'s Schedule - Week \",\n                                academicWeek\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 28\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 16\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: (()=>{\n                            const dayScheduleMap = schedule[selectedDay] || {};\n                            const lessons = Object.entries(dayScheduleMap).filter((param)=>{\n                                let [, value] = param;\n                                return typeof value === 'object';\n                            }).sort((param, param1)=>{\n                                let [keyA] = param, [keyB] = param1;\n                                return keyA.localeCompare(keyB);\n                            }).map((param)=>{\n                                let [, value] = param;\n                                return value;\n                            });\n                            if (lessons.length > 0) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: lessons.map((lesson, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 rounded border \".concat(getSubjectClass(lesson.subject), \" flex justify-between items-center\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: lesson.subject\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 36\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs mt-1\",\n                                                            children: lesson.teacher || \"AI Instructor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 36\n                                                        }, this),\n                                                        lesson.lessonReference && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: lesson.lessonReference\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 676,\n                                                                    columnNumber: 40\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-block px-1 py-0.5 rounded-sm text-xs \".concat(getStatusBadgeClass(lesson.status || 'upcoming')),\n                                                                            children: lesson.status === 'completed' ? 'Completed' : lesson.status === 'in_progress' ? 'In Progress' : lesson.status === 'past' ? 'Past' : 'Upcoming'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 678,\n                                                                            columnNumber: 42\n                                                                        }, this),\n                                                                        (lesson.cognitiveLoad || lesson.cognitive_load) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-block px-1 py-0.5 rounded-sm text-xs bg-indigo-100 text-indigo-800\",\n                                                                            children: [\n                                                                                \"CL: \",\n                                                                                lesson.cognitiveLoad || lesson.cognitive_load\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 44\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 677,\n                                                                    columnNumber: 40\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 675,\n                                                            columnNumber: 38\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 34\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 38\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: lesson.startTime\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                                    lineNumber: 696,\n                                                                    columnNumber: 38\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 694,\n                                                            columnNumber: 36\n                                                        }, this),\n                                                        lesson.lessonReference && lesson.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleJoinLesson(lesson),\n                                                            children: \"Join Lesson\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 38\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 cursor-pointer\",\n                                                            onClick: ()=>{\n                                                                const subjectId = lesson.subject_id || (0,_lib_subject_utils_client__WEBPACK_IMPORTED_MODULE_7__.normalizeSubjectName)(lesson.subject).toLowerCase().replace(/\\s+/g, '_');\n                                                                router.push(\"/subjects/\".concat(subjectId, \"/lessons?studentId=\").concat(studentIdProp));\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 36\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, \"\".concat(selectedDay, \"-d-\").concat(idx), true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 32\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 28\n                                }, this);\n                            } else {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-6\",\n                                    children: [\n                                        \"No classes scheduled for \",\n                                        selectedDay,\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 33\n                                }, this);\n                            }\n                        })()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 658,\n                        columnNumber: 16\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                lineNumber: 656,\n                columnNumber: 12\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Week \",\n                                    academicWeek,\n                                    \" Lesson Summary\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 16\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: [\n                                    \"Total lessons scheduled per subject for week \",\n                                    academicWeek,\n                                    \" of 30.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 16\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 12\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Total Weekly Lessons:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 737,\n                                                columnNumber: 24\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-lg \".concat(weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK ? 'text-red-600' : 'text-gray-800'),\n                                                children: [\n                                                    weeklySummary.totalLessons,\n                                                    \" / \",\n                                                    MAX_LESSONS_PER_WEEK\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 24\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 20\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                        value: weeklySummary.totalLessons / MAX_LESSONS_PER_WEEK * 100,\n                                        className: \"h-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 20\n                                    }, this),\n                                    weeklySummary.totalLessons > MAX_LESSONS_PER_WEEK && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-red-600 mt-1\",\n                                        children: \"Warning: Maximum recommended lessons exceeded.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 24\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 16\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: weeklySummary.sortedSubjects.length > 0 ? weeklySummary.sortedSubjects.map((sub)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center text-sm p-2 rounded bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-gray-700\",\n                                                children: sub.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: [\n                                                    sub.count,\n                                                    \" lesson\",\n                                                    sub.count !== 1 ? 's' : ''\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 32\n                                            }, this)\n                                        ]\n                                    }, sub.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 28\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-center text-gray-500 py-4\",\n                                    children: \"No lessons scheduled to summarize.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 24\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                lineNumber: 747,\n                                columnNumber: 16\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 12\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                lineNumber: 727,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-4 flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CalendarDays_ChevronRight_Clock_Cpu_Download_Grid_List_RefreshCw_View_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"h-10 w-10 text-blue-500 mr-4 flex-shrink-0\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 14\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-blue-700\",\n                                    children: \"AI Timetable Optimization\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 16\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600 mt-1\",\n                                    children: [\n                                        \"This timetable was generated by AI, balancing subject distribution and cognitive load for optimal learning across \",\n                                        academicWeek === 1 ? 'the first week' : \"week \".concat(academicWeek),\n                                        \" of 30 weeks.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                                    lineNumber: 770,\n                                    columnNumber: 16\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                            lineNumber: 768,\n                            columnNumber: 14\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                    lineNumber: 766,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n                lineNumber: 765,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\TimetableTab.tsx\",\n        lineNumber: 444,\n        columnNumber: 5\n    }, this);\n}\n_s(TimetableTab, \"9pD5pIn1RNZQ9/uPgE59OnA7eIQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TimetableTab;\nvar _c;\n$RefreshReg$(_c, \"TimetableTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/dashboard/TimetableTab.tsx\n"));

/***/ })

});