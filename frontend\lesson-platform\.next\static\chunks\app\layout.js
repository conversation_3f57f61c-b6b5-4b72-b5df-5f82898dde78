/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/client-providers.tsx */ \"(app-pages-browser)/./src/app/client-providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers/ThemeProvider.tsx */ \"(app-pages-browser)/./src/app/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ErrorBoundary.tsx */ \"(app-pages-browser)/./src/components/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGMlNUMlNUNPbmVEcml2ZSU1QyU1Q0Rlc2t0b3AlNUMlNUNEZXNrdG9wJTVDJTVDU29seW50YV9XZWJzaXRlJTVDJTVDZnJvbnRlbmQlNUMlNUNsZXNzb24tcGxhdGZvcm0lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjbGllbnQtcHJvdmlkZXJzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkNsaWVudFByb3ZpZGVycyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwYyU1QyU1Q09uZURyaXZlJTVDJTVDRGVza3RvcCU1QyU1Q0Rlc2t0b3AlNUMlNUNTb2x5bnRhX1dlYnNpdGUlNUMlNUNmcm9udGVuZCU1QyU1Q2xlc3Nvbi1wbGF0Zm9ybSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcHJvdmlkZXJzJTVDJTVDVGhlbWVQcm92aWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3BjJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDRGVza3RvcCU1QyU1Q1NvbHludGFfV2Vic2l0ZSU1QyU1Q2Zyb250ZW5kJTVDJTVDbGVzc29uLXBsYXRmb3JtJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q0Vycm9yQm91bmRhcnkudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUErTDtBQUMvTDtBQUNBLG9LQUFpSjtBQUNqSjtBQUNBLG9NQUErTDtBQUMvTDtBQUNBLDhMQUEyTCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ2xpZW50UHJvdmlkZXJzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxjbGllbnQtcHJvdmlkZXJzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGFwcFxcXFxnbG9iYWxzLmNzc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHBjXFxcXE9uZURyaXZlXFxcXERlc2t0b3BcXFxcRGVza3RvcFxcXFxTb2x5bnRhX1dlYnNpdGVcXFxcZnJvbnRlbmRcXFxcbGVzc29uLXBsYXRmb3JtXFxcXHNyY1xcXFxhcHBcXFxccHJvdmlkZXJzXFxcXFRoZW1lUHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxccGNcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxEZXNrdG9wXFxcXFNvbHludGFfV2Vic2l0ZVxcXFxmcm9udGVuZFxcXFxsZXNzb24tcGxhdGZvcm1cXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcRXJyb3JCb3VuZGFyeS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/client-providers.tsx":
/*!**************************************!*\
  !*** ./src/app/client-providers.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientProviders: () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* __next_internal_client_entry_do_not_use__ ClientProviders auto */ \nvar _s = $RefreshSig$();\n\n // Back to original providers\nfunction ClientProviders(param) {\n    let { children } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientProviders.useEffect\": ()=>{\n            const handleRejection = {\n                \"ClientProviders.useEffect.handleRejection\": (event)=>{\n                    var _event_reason, _event_reason_message, _event_reason1, _event_reason_message1, _event_reason2;\n                    console.error(\"!!!! GLOBAL UNHANDLED REJECTION !!!!\", event.reason);\n                    // Handle ChunkLoadError specifically\n                    if (((_event_reason = event.reason) === null || _event_reason === void 0 ? void 0 : _event_reason.name) === 'ChunkLoadError' || ((_event_reason1 = event.reason) === null || _event_reason1 === void 0 ? void 0 : (_event_reason_message = _event_reason1.message) === null || _event_reason_message === void 0 ? void 0 : _event_reason_message.includes('Loading chunk')) || ((_event_reason2 = event.reason) === null || _event_reason2 === void 0 ? void 0 : (_event_reason_message1 = _event_reason2.message) === null || _event_reason_message1 === void 0 ? void 0 : _event_reason_message1.includes('Loading CSS chunk'))) {\n                        console.log('ChunkLoadError detected, reloading page...');\n                        event.preventDefault(); // Prevent the default unhandled rejection behavior\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleRejection\"];\n            const handleError = {\n                \"ClientProviders.useEffect.handleError\": (event)=>{\n                    var _event_error, _event_error_message, _event_error1, _event_error_message1, _event_error2;\n                    console.error(\"!!!! GLOBAL ERROR !!!!\", event.error);\n                    // Handle ChunkLoadError from regular errors too\n                    if (((_event_error = event.error) === null || _event_error === void 0 ? void 0 : _event_error.name) === 'ChunkLoadError' || ((_event_error1 = event.error) === null || _event_error1 === void 0 ? void 0 : (_event_error_message = _event_error1.message) === null || _event_error_message === void 0 ? void 0 : _event_error_message.includes('Loading chunk')) || ((_event_error2 = event.error) === null || _event_error2 === void 0 ? void 0 : (_event_error_message1 = _event_error2.message) === null || _event_error_message1 === void 0 ? void 0 : _event_error_message1.includes('Loading CSS chunk'))) {\n                        console.log('ChunkLoadError detected via error event, reloading page...');\n                        window.location.reload();\n                        return;\n                    }\n                }\n            }[\"ClientProviders.useEffect.handleError\"];\n            window.addEventListener('unhandledrejection', handleRejection);\n            window.addEventListener('error', handleError);\n            return ({\n                \"ClientProviders.useEffect\": ()=>{\n                    window.removeEventListener('unhandledrejection', handleRejection);\n                    window.removeEventListener('error', handleError);\n                }\n            })[\"ClientProviders.useEffect\"];\n        }\n    }[\"ClientProviders.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-screen\",\n            children: \"Loading application components...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\client-providers.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientProviders, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = ClientProviders;\nvar _c;\n$RefreshReg$(_c, \"ClientProviders\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/client-providers.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7dedb17e81c0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZGVkYjE3ZTgxYzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=NextUIProvider!=!@nextui-org/react */ \"(app-pages-browser)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./providers/AuthProvider */ \"(app-pages-browser)/./src/app/providers/AuthProvider.tsx\");\n/* harmony import */ var _providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./providers/SessionProvider */ \"(app-pages-browser)/./src/app/providers/SessionProvider.tsx\");\n/* harmony import */ var _providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useSessionSimple */ \"(app-pages-browser)/./src/hooks/useSessionSimple.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/authService */ \"(app-pages-browser)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Import your AuthProvider etc.\n\n // NextAuth wrapper\n\n\n // Import the real auth function\n// Simplified SessionProvider that provides the interface expected by useSession\nfunction SimpleSessionProvider(param) {\n    let { children } = param;\n    _s();\n    const [backendSessionId, setBackendSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const value = {\n        backendSessionId,\n        user,\n        setUserSession: setUser,\n        setBackendSessionId,\n        clearSession: ()=>{\n            setUser(null);\n            setBackendSessionId(null);\n        },\n        isReady,\n        isLoading,\n        getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_7__.getAuthHeaders)(backendSessionId),\n        userRole: (user === null || user === void 0 ? void 0 : user.role) || null\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useSessionSimple__WEBPACK_IMPORTED_MODULE_6__.SessionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleSessionProvider, \"pNIfeEG3OqB25OlRAphdtSUILwM=\");\n_c = SimpleSessionProvider;\nfunction Providers(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSessionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"light\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.NextUIProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_5__.ClientToastWrapper, {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n                lineNumber: 46,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c1 = Providers;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleSessionProvider\");\n$RefreshReg$(_c1, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ1c7QUFDUDtBQUM1QyxnQ0FBZ0M7QUFDd0I7QUFDRSxDQUFDLG1CQUFtQjtBQUNWO0FBQ1Y7QUFDUCxDQUFDLGdDQUFnQztBQUVwRixnRkFBZ0Y7QUFDaEYsU0FBU1Msc0JBQXNCLEtBQTJDO1FBQTNDLEVBQUVDLFFBQVEsRUFBaUMsR0FBM0M7O0lBQzdCLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR1gsK0NBQVFBLENBQWdCO0lBQ3hFLE1BQU0sQ0FBQ1ksTUFBTUMsUUFBUSxHQUFHYiwrQ0FBUUEsQ0FBTTtJQUN0QyxNQUFNLENBQUNjLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0IsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQUM7SUFFM0MsTUFBTWtCLFFBQVE7UUFDWlI7UUFDQUU7UUFDQU8sZ0JBQWdCTjtRQUNoQkY7UUFDQVMsY0FBYztZQUNaUCxRQUFRO1lBQ1JGLG9CQUFvQjtRQUN0QjtRQUNBRztRQUNBRTtRQUNBVCxnQkFBZ0IsSUFBTUEsZ0VBQWNBLENBQUNHO1FBQ3JDVyxVQUFVVCxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1VLElBQUksS0FBSTtJQUMxQjtJQUVBLHFCQUNFLDhEQUFDaEIsbUVBQWNBLENBQUNpQixRQUFRO1FBQUNMLE9BQU9BO2tCQUM3QlQ7Ozs7OztBQUdQO0dBMUJTRDtLQUFBQTtBQTRCRixTQUFTZ0IsVUFBVSxLQUEyQztRQUEzQyxFQUFFZixRQUFRLEVBQWlDLEdBQTNDO0lBQ3hCLHFCQUNFLDhEQUFDTCxrRUFBZUE7a0JBQ2QsNEVBQUNJO3NCQUNDLDRFQUFDTCxpRUFBWUE7MEJBQ1gsNEVBQUNELHNEQUFhQTtvQkFBQ3VCLFdBQVU7b0JBQVFDLGNBQWE7OEJBQzVDLDRFQUFDekIsa0dBQWNBO2tDQUNiLDRFQUFDSSw2RUFBa0JBO3NDQUNoQkk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRakI7TUFoQmdCZSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiICAgICd1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgTmV4dFVJUHJvdmlkZXIgfSBmcm9tICdAbmV4dHVpLW9yZy9yZWFjdCc7XHJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XHJcbi8vIEltcG9ydCB5b3VyIEF1dGhQcm92aWRlciBldGMuXHJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4vcHJvdmlkZXJzL0F1dGhQcm92aWRlcic7XHJcbmltcG9ydCBTZXNzaW9uUHJvdmlkZXIgZnJvbSAnLi9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyJzsgLy8gTmV4dEF1dGggd3JhcHBlclxyXG5pbXBvcnQgeyBDbGllbnRUb2FzdFdyYXBwZXIgfSBmcm9tICcuL3Byb3ZpZGVycy9DbGllbnRUb2FzdFdyYXBwZXInO1xyXG5pbXBvcnQgeyBTZXNzaW9uQ29udGV4dCB9IGZyb20gJ0AvaG9va3MvdXNlU2Vzc2lvblNpbXBsZSc7XHJcbmltcG9ydCB7IGdldEF1dGhIZWFkZXJzIH0gZnJvbSAnQC9saWIvYXV0aFNlcnZpY2UnOyAvLyBJbXBvcnQgdGhlIHJlYWwgYXV0aCBmdW5jdGlvblxyXG5cclxuLy8gU2ltcGxpZmllZCBTZXNzaW9uUHJvdmlkZXIgdGhhdCBwcm92aWRlcyB0aGUgaW50ZXJmYWNlIGV4cGVjdGVkIGJ5IHVzZVNlc3Npb25cclxuZnVuY3Rpb24gU2ltcGxlU2Vzc2lvblByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICBjb25zdCBbYmFja2VuZFNlc3Npb25JZCwgc2V0QmFja2VuZFNlc3Npb25JZF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xyXG4gIGNvbnN0IFtpc1JlYWR5LCBzZXRJc1JlYWR5XSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcblxyXG4gIGNvbnN0IHZhbHVlID0ge1xyXG4gICAgYmFja2VuZFNlc3Npb25JZCxcclxuICAgIHVzZXIsXHJcbiAgICBzZXRVc2VyU2Vzc2lvbjogc2V0VXNlcixcclxuICAgIHNldEJhY2tlbmRTZXNzaW9uSWQsXHJcbiAgICBjbGVhclNlc3Npb246ICgpID0+IHtcclxuICAgICAgc2V0VXNlcihudWxsKTtcclxuICAgICAgc2V0QmFja2VuZFNlc3Npb25JZChudWxsKTtcclxuICAgIH0sXHJcbiAgICBpc1JlYWR5LFxyXG4gICAgaXNMb2FkaW5nLFxyXG4gICAgZ2V0QXV0aEhlYWRlcnM6ICgpID0+IGdldEF1dGhIZWFkZXJzKGJhY2tlbmRTZXNzaW9uSWQpLCAvLyBVc2UgcmVhbCBhdXRoIHNlcnZpY2UgZnVuY3Rpb25cclxuICAgIHVzZXJSb2xlOiB1c2VyPy5yb2xlIHx8IG51bGwsXHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxTZXNzaW9uQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L1Nlc3Npb25Db250ZXh0LlByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICA8U2ltcGxlU2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XHJcbiAgICAgICAgICA8VGhlbWVQcm92aWRlciBhdHRyaWJ1dGU9XCJjbGFzc1wiIGRlZmF1bHRUaGVtZT1cImxpZ2h0XCI+XHJcbiAgICAgICAgICAgIDxOZXh0VUlQcm92aWRlcj4gXHJcbiAgICAgICAgICAgICAgPENsaWVudFRvYXN0V3JhcHBlcj5cclxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgICA8L0NsaWVudFRvYXN0V3JhcHBlcj5cclxuICAgICAgICAgICAgPC9OZXh0VUlQcm92aWRlcj5cclxuICAgICAgICAgIDwvVGhlbWVQcm92aWRlcj5cclxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cclxuICAgICAgPC9TaW1wbGVTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJOZXh0VUlQcm92aWRlciIsIlRoZW1lUHJvdmlkZXIiLCJBdXRoUHJvdmlkZXIiLCJTZXNzaW9uUHJvdmlkZXIiLCJDbGllbnRUb2FzdFdyYXBwZXIiLCJTZXNzaW9uQ29udGV4dCIsImdldEF1dGhIZWFkZXJzIiwiU2ltcGxlU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJiYWNrZW5kU2Vzc2lvbklkIiwic2V0QmFja2VuZFNlc3Npb25JZCIsInVzZXIiLCJzZXRVc2VyIiwiaXNSZWFkeSIsInNldElzUmVhZHkiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ2YWx1ZSIsInNldFVzZXJTZXNzaW9uIiwiY2xlYXJTZXNzaW9uIiwidXNlclJvbGUiLCJyb2xlIiwiUHJvdmlkZXIiLCJQcm92aWRlcnMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/SessionProvider.tsx":
/*!***********************************************!*\
  !*** ./src/app/providers/SessionProvider.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider(param) {\n    let { children } = param;\n    // Always wrap children in NextAuthSessionProvider\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = SessionProvider;\nvar _c;\n$RefreshReg$(_c, \"SessionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFNkU7QUFFOUQsU0FBU0EsZ0JBQWdCLEtBQTJDO1FBQTNDLEVBQUVFLFFBQVEsRUFBaUMsR0FBM0M7SUFDdEMsa0RBQWtEO0lBQ2xELHFCQUNFLDhEQUFDRCw0REFBdUJBO2tCQUNyQkM7Ozs7OztBQUdQO0tBUHdCRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcc3JjXFxhcHBcXHByb3ZpZGVyc1xcU2Vzc2lvblByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2Vzc2lvblByb3ZpZGVyKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICAvLyBBbHdheXMgd3JhcCBjaGlsZHJlbiBpbiBOZXh0QXV0aFNlc3Npb25Qcm92aWRlclxyXG4gIHJldHVybiAoXHJcbiAgICA8TmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJOZXh0QXV0aFNlc3Npb25Qcm92aWRlciIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/SessionProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/providers/ThemeProvider.tsx":
/*!*********************************************!*\
  !*** ./src/app/providers/ThemeProvider.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// Theme provider with hydration-safe implementation\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light'); // Default theme\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Effect to run once on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setHasMounted(true); // Mark as mounted\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Effect to apply theme once mounted\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (hasMounted) {\n                const savedTheme = localStorage.getItem('theme') || 'light';\n                setTheme(savedTheme); // Update state for potential context consumers\n                // Apply attributes directly to <html> tag\n                document.documentElement.className = savedTheme; // Set class for CSS targeting\n                document.documentElement.setAttribute('color-pick-mode', savedTheme);\n                document.documentElement.style.setProperty('color-scheme', savedTheme); // Set color-scheme style\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        hasMounted\n    ]); // Rerun if hasMounted changes (which is only once)\n    // Avoid rendering children until mounted to prevent mismatch\n    if (!hasMounted) {\n        // Render nothing or a placeholder/loader on the server and initial client render\n        // Returning children directly here could still cause mismatches if they depend on the theme\n        return null;\n    // Alternatively, render children but without theme-specific wrapper/context:\n    // return <>{children}</>;\n    }\n    // Once mounted, render children. The theme is applied via useEffect directly to documentElement.\n    // No need for a wrapper div with data-theme here.\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ThemeProvider, \"5+Yq0u/1vtTArMRxr05JuELf41o=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/providers/ThemeProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error('ErrorBoundary caught an error:', error, errorInfo);\n        // Check if it's a ChunkLoadError\n        if (error.name === 'ChunkLoadError' || error.message.includes('Loading chunk')) {\n            console.log('ChunkLoadError detected, attempting to reload...');\n            // Reload the page to recover from chunk load errors\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1000);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            const FallbackComponent = this.props.fallback || DefaultErrorFallback;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                error: this.state.error,\n                retry: this.retry\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 45,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props), this.retry = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n}\nfunction DefaultErrorFallback(param) {\n    let { error, retry } = param;\n    const isChunkError = (error === null || error === void 0 ? void 0 : error.name) === 'ChunkLoadError' || (error === null || error === void 0 ? void 0 : error.message.includes('Loading chunk'));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 mx-auto\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.734-.833-2.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-2\",\n                    children: isChunkError ? 'Loading Error' : 'Something went wrong'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-4\",\n                    children: isChunkError ? 'The application is updating. Please wait while we reload...' : 'An unexpected error occurred. Please try again.'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                isChunkError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: retry,\n                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors\",\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 11\n                }, this),\n                error && !isChunkError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mt-4 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm text-gray-500\",\n                            children: \"Error Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-gray-600 overflow-auto\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_c = DefaultErrorFallback;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\nvar _c;\n$RefreshReg$(_c, \"DefaultErrorFallback\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ErrorBoundary.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useSessionSimple.tsx":
/*!****************************************!*\
  !*** ./src/hooks/useSessionSimple.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authService */ \"(app-pages-browser)/./src/lib/authService.ts\");\n/* __next_internal_client_entry_do_not_use__ useSession,SessionContext auto */ var _s = $RefreshSig$();\n\n // Import real auth service\n// Simplified session context for the frontend diagnostic fix\nconst SessionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nfunction useSession() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SessionContext);\n    if (context === undefined) {\n        // Instead of throwing an error immediately, return a fallback object\n        console.warn('useSession called outside of SessionProvider, returning fallback values');\n        return {\n            backendSessionId: null,\n            user: null,\n            setUserSession: ()=>{},\n            setBackendSessionId: ()=>{},\n            clearSession: ()=>{},\n            isReady: false,\n            isLoading: true,\n            getAuthHeaders: ()=>(0,_lib_authService__WEBPACK_IMPORTED_MODULE_1__.getAuthHeaders)(null),\n            userRole: null\n        };\n    }\n    return context;\n}\n_s(useSession, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Export the context for providers to use\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useSessionSimple.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","common","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cclient-providers.tsx%22%2C%22ids%22%3A%5B%22ClientProviders%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Capp%5C%5Cproviders%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpc%5C%5COneDrive%5C%5CDesktop%5C%5CDesktop%5C%5CSolynta_Website%5C%5Cfrontend%5C%5Clesson-platform%5C%5Csrc%5C%5Ccomponents%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);