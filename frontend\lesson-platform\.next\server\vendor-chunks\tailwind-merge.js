"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tailwind-merge";
exports.ids = ["vendor-chunks/tailwind-merge"];
exports.modules = {

/***/ "(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': [borderColor]\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n");

/***/ })

};
;