import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { calculateLessonStatus } from '@/lib/time-utils';
import { getAuth } from 'firebase-admin/auth';

// Define the standard time slots for timetable generation
const STANDARD_TIME_SLOTS = [
  '08:00-08:45',
  '08:50-09:35', 
  '09:40-10:25',
  '10:40-11:25',
  '11:30-12:15',
  '13:15-14:00',
  '14:15-15:00'
];

const DAYS_OF_WEEK = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

// Interface for enrollment data
interface Enrollment {
  subjectId: string;
  subjectName: string;
  subjectCode: string;
  lessonsPerWeek: number;
  status: string;
  enrolledAt: any;
  cognitiveLoad?: number; // Add cognitive load rating (1-10, 10 being highest)
}

// Subject cognitive load mapping (1-10, where 10 is highest cognitive load)
const SUBJECT_COGNITIVE_LOADS: Record<string, number> = {
  'mathematics': 9,
  'english_language': 8,
  'basic_science_and_technology': 7,
  'basic_science': 7,
  'social_studies': 6,
  'computer_studies': 8,
  'computing': 8,
  'artificial_intelligence': 9,
  'french': 7,
  'christian_religious_knowledge': 5,
  'national_values_education': 4,
  'entrepreneurship_education': 5,
  'entrepreneurship': 5,
  'financial_literacy': 6,
  'creative_arts': 3,
  'cultural_and_creative_arts': 3,
  'art_and_design': 3,
  'physical_health_education': 2,
  'project_based_excellence': 6
};

// Calculate lesson reference for a specific week and subject
function generateLessonReference(
  grade: string,
  subjectCode: string,
  academicWeek: number,
  lessonNumber: number,
  lessonsPerWeek: number
): string {
  // Calculate the absolute lesson number across all weeks
  const absoluteLessonNumber = ((academicWeek - 1) * lessonsPerWeek) + lessonNumber;

  // Format lesson number with leading zeros (e.g., 001, 002, etc.)
  const formattedLessonNumber = absoluteLessonNumber.toString().padStart(3, '0');

  // Convert grade to proper abbreviated format
  const abbreviatedGrade = convertGradeToAbbreviation(grade);

  return `${abbreviatedGrade}-${subjectCode}-${formattedLessonNumber}`;
}

// Convert grade level to proper abbreviated format
function convertGradeToAbbreviation(grade: string): string {
  // Handle different grade formats
  const gradeStr = grade.toLowerCase().trim();

  // Primary grades
  if (gradeStr.includes('primary') || gradeStr.startsWith('p')) {
    const match = gradeStr.match(/(\d+)/);
    if (match) {
      return `P${match[1]}`;
    }
  }

  // Junior Secondary
  if (gradeStr.includes('junior') || gradeStr.includes('jss')) {
    const match = gradeStr.match(/(\d+)/);
    if (match) {
      return `JSS${match[1]}`;
    }
  }

  // Senior Secondary
  if (gradeStr.includes('senior') || gradeStr.includes('sss')) {
    const match = gradeStr.match(/(\d+)/);
    if (match) {
      return `SSS${match[1]}`;
    }
  }

  // If already in correct format, return as is
  if (/^(P|JSS|SSS)\d+$/i.test(grade)) {
    return grade.toUpperCase();
  }

  // Default fallback
  return grade;
}

// Fetch student enrollments from Firestore
async function fetchStudentEnrollments(studentId: string): Promise<Enrollment[]> {
  try {
    console.log(`[Timetable API] Fetching enrollments for student: ${studentId}`);
    
    // Try different student ID formats
    const studentIdFormats = [
      studentId,
      `andrea_ugono_33305`, // Direct ID
      studentId.toLowerCase(),
      studentId.replace(/\s+/g, '_').toLowerCase()
    ];
    
    let enrollments: Enrollment[] = [];
    let studentRef: any = null;
    
    for (const format of studentIdFormats) {
      console.log(`[Timetable API] Trying student ID format: ${format}`);
      
      const testStudentRef = db!.collection('students').doc(format);
      const testStudentSnap = await testStudentRef.get();
      
      if (testStudentSnap.exists) {
        console.log(`[Timetable API] Found student document with ID: ${format}`);
        studentRef = testStudentRef;
        break;
      }
    }
    
    if (!studentRef) {
      console.warn(`[Timetable API] No student document found for any ID format`);
      return [];
    }
    
    // Fetch enrollments from the enrollments subcollection
    const enrollmentsRef = studentRef.collection('enrollments');
    const enrollmentsSnap = await enrollmentsRef.where('status', '==', 'active').get();
    
    console.log(`[Timetable API] Found ${enrollmentsSnap.size} active enrollments`);
    
    enrollmentsSnap.docs.forEach(doc => {
      const data = doc.data();
      const subjectId = doc.id;
      enrollments.push({
        subjectId: subjectId,
        subjectName: data.subjectName || data.subject_name || doc.id.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        subjectCode: data.subjectCode || data.subject_code || getSubjectCodeFromId(doc.id),
        lessonsPerWeek: data.lessonsPerWeek || data.lessons_per_week || 1,
        status: data.status,
        enrolledAt: data.enrolledAt || data.enrolled_at,
        cognitiveLoad: data.cognitiveLoad || SUBJECT_COGNITIVE_LOADS[subjectId] || 5 // Default to medium cognitive load
      });
    });
    
    console.log(`[Timetable API] Processed enrollments:`, enrollments);
    return enrollments;
    
  } catch (error) {
    console.error(`[Timetable API] Error fetching enrollments:`, error);
    return [];
  }
}

// Get subject code from subject ID
function getSubjectCodeFromId(subjectId: string): string {
  // Normalize the input: convert to lowercase and replace hyphens/spaces with underscores
  const normalizedId = subjectId.toLowerCase()
    .replace(/[-\s]+/g, '_')
    .replace(/[^a-z0-9_]/g, ''); // Remove any other special characters

  const codeMap: Record<string, string> = {
    'mathematics': 'MAT',
    'english_language': 'ENG',
    'basic_science_and_technology': 'BST',
    'basic_science': 'BST',
    'social_studies': 'SST',
    'computer_studies': 'COM',
    'computing': 'COM',
    'creative_arts': 'ART',
    'cultural_and_creative_arts': 'CCA',
    'art_and_design': 'ART',
    'physical_health_education': 'PHE',
    'national_values_education': 'NVE',
    'entrepreneurship_education': 'ENT',
    'entrepreneurship': 'ENT',
    'financial_literacy': 'FIL',
    'french': 'FRE',
    'artificial_intelligence': 'AI',
    'project_based_excellence': 'PBE',
    'project_based_excellence': 'PBE', // Handle the hyphen-underscore variant from logs
    'christian_religious_knowledge': 'CRK'
  };

  // Try the normalized ID first
  if (codeMap[normalizedId]) {
    return codeMap[normalizedId];
  }

  // Try the original ID as fallback
  if (codeMap[subjectId.toLowerCase()]) {
    return codeMap[subjectId.toLowerCase()];
  }

  // Special handling for project-based excellence variants
  if (subjectId.toLowerCase().includes('project') && subjectId.toLowerCase().includes('excellence')) {
    return 'PBE';
  }

  return 'GEN';
}

// Distribute lessons across the week based on lessons per week and cognitive load
function distributeLessonsAcrossWeek(enrollments: Enrollment[], academicWeek: number, studentGrade: string): any[] {
  const weeklyLessons: any[] = [];

  console.log(`[Timetable API] Distributing lessons for ${enrollments.length} subjects across the week`);

  // Create a weekly schedule grid: [day][timeSlot]
  const weekSchedule: (any | null)[][] = DAYS_OF_WEEK.map(() => new Array(STANDARD_TIME_SLOTS.length).fill(null));

  // Calculate total lessons needed
  const totalLessonsNeeded = enrollments.reduce((sum, e) => sum + e.lessonsPerWeek, 0);
  const totalSlotsAvailable = DAYS_OF_WEEK.length * STANDARD_TIME_SLOTS.length; // 5 days × 7 slots = 35

  console.log(`[Timetable API] Total lessons needed: ${totalLessonsNeeded}, Total slots available: ${totalSlotsAvailable}`);

  // Sort enrollments by cognitive load (highest first) for optimal time slot assignment
  const sortedEnrollments = [...enrollments].sort((a, b) => (b.cognitiveLoad || 5) - (a.cognitiveLoad || 5));

  console.log(`[Timetable API] Sorted subjects by cognitive load:`,
    sortedEnrollments.map(e => `${e.subjectName} (${e.cognitiveLoad}) - ${e.lessonsPerWeek} lessons`));

  // Track how many lessons each subject has been assigned
  const subjectLessonCounts: Record<string, number> = {};

  // Initialize all subjects
  sortedEnrollments.forEach(enrollment => {
    subjectLessonCounts[enrollment.subjectId] = 0;
  });

  // Schedule all lessons using a more aggressive approach
  scheduleAllLessonsOptimally(sortedEnrollments, weekSchedule, academicWeek, studentGrade, subjectLessonCounts);

  // Fill any remaining empty slots with "Free Period"
  fillEmptySlotsWithFreePeriods(weekSchedule, academicWeek, studentGrade);

  // Convert the schedule grid back to lesson objects
  for (let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++) {
    for (let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++) {
      const lesson = weekSchedule[dayIndex][timeIndex];
      if (lesson) {
        weeklyLessons.push(lesson);
      }
    }
  }

  console.log(`[Timetable API] Generated ${weeklyLessons.length} lessons for the week (target: ${totalSlotsAvailable})`);

  // Log final subject distribution
  const finalCounts: Record<string, number> = {};
  weeklyLessons.forEach(lesson => {
    if (lesson.subjectId && lesson.subjectId !== 'free_period') {
      finalCounts[lesson.subjectId] = (finalCounts[lesson.subjectId] || 0) + 1;
    }
  });

  console.log(`[Timetable API] Final lesson distribution:`, finalCounts);

  return weeklyLessons;
}

// New optimized scheduling function that ensures all lessons are scheduled
function scheduleAllLessonsOptimally(
  enrollments: Enrollment[],
  weekSchedule: (any | null)[][],
  academicWeek: number,
  studentGrade: string,
  subjectLessonCounts: Record<string, number>
): void {
  // Create a list of all lessons that need to be scheduled (without lesson numbers initially)
  const lessonsToSchedule: { enrollment: Enrollment }[] = [];

  enrollments.forEach(enrollment => {
    for (let i = 1; i <= enrollment.lessonsPerWeek; i++) {
      lessonsToSchedule.push({ enrollment });
    }
  });

  console.log(`[Timetable API] Total lessons to schedule: ${lessonsToSchedule.length}`);

  // Sort lessons by cognitive load (highest first)
  lessonsToSchedule.sort((a, b) => {
    return (b.enrollment.cognitiveLoad || 5) - (a.enrollment.cognitiveLoad || 5);
  });

  // Track lessons per day for each subject to enforce distribution rules
  const subjectDailyCount: Record<string, number[]> = {};
  enrollments.forEach(enrollment => {
    subjectDailyCount[enrollment.subjectId] = new Array(DAYS_OF_WEEK.length).fill(0);
  });

  // Schedule each lesson (without assigning lesson numbers yet)
  for (const lessonToSchedule of lessonsToSchedule) {
    const { enrollment } = lessonToSchedule;
    let scheduled = false;

    // Get preferred time slots based on cognitive load
    const preferredTimeSlots = getPreferredTimeSlots(enrollment.cognitiveLoad || 5);

    // Try to schedule on each day, prioritizing days with fewer lessons for this subject
    const dayPriority = Array.from({ length: DAYS_OF_WEEK.length }, (_, i) => i)
      .sort((a, b) => subjectDailyCount[enrollment.subjectId][a] - subjectDailyCount[enrollment.subjectId][b]);

    for (const dayIndex of dayPriority) {
      // Special rule for Mathematics: allow up to 2 lessons on Monday, 1 on other days
      const maxLessonsPerDay = (enrollment.subjectId === 'mathematics' && dayIndex === 0) ? 2 :
                               (enrollment.lessonsPerWeek >= 6) ? 2 : 1;

      if (subjectDailyCount[enrollment.subjectId][dayIndex] >= maxLessonsPerDay) {
        continue; // Skip this day if we've reached the limit
      }

      // Try preferred time slots first
      for (const timeIndex of preferredTimeSlots) {
        if (weekSchedule[dayIndex][timeIndex] === null) {
          // Check if this would create back-to-back lessons (avoid if possible)
          const hasAdjacentLesson = (timeIndex > 0 && weekSchedule[dayIndex][timeIndex - 1]?.subjectId === enrollment.subjectId) ||
                                   (timeIndex < STANDARD_TIME_SLOTS.length - 1 && weekSchedule[dayIndex][timeIndex + 1]?.subjectId === enrollment.subjectId);

          // For subjects with multiple lessons per week, allow adjacent lessons if necessary
          if (!hasAdjacentLesson || enrollment.lessonsPerWeek >= 4) {
            subjectLessonCounts[enrollment.subjectId]++;
            subjectDailyCount[enrollment.subjectId][dayIndex]++;

            // Create lesson object with placeholder lesson number (will be fixed later)
            const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);
            weekSchedule[dayIndex][timeIndex] = lesson;
            scheduled = true;
            break;
          }
        }
      }

      if (scheduled) break;

      // If no preferred slot worked, try any available slot on this day
      for (let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++) {
        if (weekSchedule[dayIndex][timeIndex] === null) {
          subjectLessonCounts[enrollment.subjectId]++;
          subjectDailyCount[enrollment.subjectId][dayIndex]++;

          // Create lesson object with placeholder lesson number (will be fixed later)
          const lesson = createLessonObject(enrollment, 0, dayIndex, timeIndex, academicWeek, studentGrade);
          weekSchedule[dayIndex][timeIndex] = lesson;
          scheduled = true;
          break;
        }
      }

      if (scheduled) break;
    }

    if (!scheduled) {
      console.warn(`[Timetable API] Could not schedule ${enrollment.subjectName} lesson - no available slots`);
    }
  }

  // Now fix lesson numbering to be chronological
  fixLessonNumberingChronologically(weekSchedule, academicWeek, studentGrade);

  // Log scheduling results
  console.log(`[Timetable API] Scheduled lessons and fixed chronological numbering`);
}

// Fix lesson numbering to follow chronological order (day and time based)
function fixLessonNumberingChronologically(
  weekSchedule: (any | null)[][],
  academicWeek: number,
  studentGrade: string
): void {
  // Group lessons by subject
  const subjectLessons: Record<string, any[]> = {};

  // Collect all lessons for each subject
  for (let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++) {
    for (let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++) {
      const lesson = weekSchedule[dayIndex][timeIndex];
      if (lesson && lesson.subjectId && lesson.subjectId !== 'free_period') {
        if (!subjectLessons[lesson.subjectId]) {
          subjectLessons[lesson.subjectId] = [];
        }
        subjectLessons[lesson.subjectId].push({
          lesson,
          dayIndex,
          timeIndex,
          position: dayIndex * STANDARD_TIME_SLOTS.length + timeIndex // For sorting
        });
      }
    }
  }

  // For each subject, sort lessons chronologically and reassign lesson numbers
  Object.entries(subjectLessons).forEach(([subjectId, lessons]) => {
    // Sort by chronological position (day first, then time)
    lessons.sort((a, b) => a.position - b.position);

    // Reassign lesson numbers chronologically
    lessons.forEach((lessonData, index) => {
      const { lesson, dayIndex, timeIndex } = lessonData;
      const newLessonNumber = index + 1;

      // Update the lesson object with correct numbering
      const enrollment: Enrollment = {
        subjectId: lesson.subjectId,
        subjectName: lesson.subject,
        subjectCode: lesson.subjectCode,
        lessonsPerWeek: lessons.length,
        cognitiveLoad: lesson.cognitiveLoad,
        status: 'active',
        enrolledAt: lesson.enrolledAt || new Date()
      };

      const updatedLesson = createLessonObject(
        enrollment,
        newLessonNumber,
        dayIndex,
        timeIndex,
        academicWeek,
        studentGrade
      );

      // Replace the lesson in the schedule
      weekSchedule[dayIndex][timeIndex] = updatedLesson;
    });
  });

  console.log(`[Timetable API] Fixed lesson numbering for ${Object.keys(subjectLessons).length} subjects`);
}

// Get preferred time slots based on cognitive load
function getPreferredTimeSlots(cognitiveLoad: number): number[] {
  if (cognitiveLoad >= 8) {
    // High cognitive load: prefer early morning slots
    return [0, 1, 2, 3, 4, 5, 6];
  } else if (cognitiveLoad >= 6) {
    // Medium cognitive load: prefer mid-morning slots
    return [1, 2, 3, 0, 4, 5, 6];
  } else if (cognitiveLoad >= 4) {
    // Low-medium cognitive load: prefer afternoon slots
    return [3, 4, 5, 2, 6, 1, 0];
  } else {
    // Low cognitive load: prefer late slots
    return [5, 6, 4, 3, 2, 1, 0];
  }
}

// Create a lesson object
function createLessonObject(
  enrollment: Enrollment,
  lessonNumber: number,
  dayIndex: number,
  timeIndex: number,
  academicWeek: number,
  studentGrade: string
): any {
  const lessonReference = generateLessonReference(
    studentGrade,
    enrollment.subjectCode,
    academicWeek,
    lessonNumber,
    enrollment.lessonsPerWeek
  );

  // Default all lessons to "upcoming" status
  // Actual completion status will be applied later from Firestore data
  const status = 'upcoming';

  return {
    id: `${lessonReference}_week${academicWeek}`,
    lessonReference: lessonReference,
    title: `${enrollment.subjectName} - Week ${academicWeek}, Lesson ${lessonNumber}`,
    subject: enrollment.subjectName,
    subjectId: enrollment.subjectId,
    subjectCode: enrollment.subjectCode,
    time: STANDARD_TIME_SLOTS[timeIndex],
    day: DAYS_OF_WEEK[dayIndex],
    duration: 45,
    status: status,
    description: `Week ${academicWeek} lesson ${lessonNumber} for ${enrollment.subjectName}`,
    grade: studentGrade,
    academicWeek: academicWeek,
    lessonNumberInWeek: lessonNumber,
    absoluteLessonNumber: ((academicWeek - 1) * enrollment.lessonsPerWeek) + lessonNumber,
    totalWeeks: 30,
    teacher: 'AI Instructor',
    cognitiveLoad: enrollment.cognitiveLoad,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
}

// Apply lesson completion states from Firestore to the generated lessons
function applyLessonCompletionStates(
  lessons: any[],
  completionStates: Record<string, any>
): any[] {
  console.log(`[Timetable API] Applying completion states to ${lessons.length} lessons`);

  const updatedLessons = lessons.map(lesson => {
    if (!lesson.lessonReference || lesson.isFreePeriod) {
      return lesson; // Skip lessons without lesson references or free periods
    }

    // Check if this lesson has completion data from session
    const sessionData = completionStates[lesson.lessonReference];

    if (sessionData) {
      console.log(`[Timetable API] Found session data for ${lesson.lessonReference}:`, {
        status: sessionData.status,
        sessionId: sessionData.sessionId,
        currentPhase: sessionData.currentPhase,
        lastUpdated: sessionData.lastUpdated
      });

      // Update lesson with actual completion status from session
      return {
        ...lesson,
        status: sessionData.status,
        completed: sessionData.completed,
        completedAt: sessionData.completedAt,
        progress: sessionData.progress,
        // Add session-specific metadata
        sessionId: sessionData.sessionId,
        currentPhase: sessionData.currentPhase,
        workingLevel: sessionData.workingLevel,
        lastUpdated: sessionData.lastUpdated,
        sessionCreatedAt: sessionData.createdAt
      };
    }

    // No session data found, keep as "upcoming"
    return lesson;
  });

  // Calculate status summary (excluding free periods)
  const nonFreePeriodLessons = updatedLessons.filter(l => !l.isFreePeriod);
  const completedCount = nonFreePeriodLessons.filter(l => l.status === 'completed').length;
  const inProgressCount = nonFreePeriodLessons.filter(l => l.status === 'in_progress').length;
  const upcomingCount = nonFreePeriodLessons.filter(l => l.status === 'upcoming').length;

  console.log(`[Timetable API] Lesson status summary (${nonFreePeriodLessons.length} total): ${completedCount} completed, ${inProgressCount} in progress, ${upcomingCount} upcoming`);

  // Log details of completed/in-progress lessons
  const activeLessons = nonFreePeriodLessons.filter(l => l.status !== 'upcoming');
  if (activeLessons.length > 0) {
    console.log(`[Timetable API] Active lessons:`, activeLessons.map(l => ({
      lessonRef: l.lessonReference,
      status: l.status,
      phase: l.currentPhase,
      sessionId: l.sessionId
    })));
  }

  return updatedLessons;
}

// Get student grade level
async function getStudentGrade(studentId: string): Promise<string> {
  try {
    // Try different student ID formats
    const studentIdFormats = [
      studentId,
      `andrea_ugono_33305`,
      studentId.toLowerCase(),
      studentId.replace(/\s+/g, '_').toLowerCase()
    ];

    for (const format of studentIdFormats) {
      const studentRef: any = db!.collection('students').doc(format);
      const studentSnap = await studentRef.get();

      if (studentSnap.exists) {
        const studentData = studentSnap.data();
        return studentData?.gradeLevel || studentData?.grade || 'P5';
      }
    }

    return 'P5'; // Default grade
  } catch (error) {
    console.error('[Timetable API] Error fetching student grade:', error);
    return 'P5';
  }
}

// Fetch lesson completion states from Firestore using session-based data structure
async function fetchLessonCompletionStates(studentId: string): Promise<Record<string, any>> {
  try {
    console.log(`[Timetable API] Fetching lesson completion states for student: ${studentId}`);

    // Try different student ID formats for querying
    const studentIdFormats = [
      studentId,
      `andrea_ugono_33305`,
      studentId.toLowerCase(),
      studentId.replace(/\s+/g, '_').toLowerCase()
    ];

    const completionStates: Record<string, any> = {};
    let totalSessionsFound = 0;

    for (const format of studentIdFormats) {
      try {
        console.log(`[Timetable API] Querying lesson_states collection for student_id: ${format}`);

        // Query the lesson_states collection for all sessions belonging to this student
        const lessonStatesQuery = db!.collection('lesson_states')
          .where('student_id', '==', format);

        const querySnapshot = await lessonStatesQuery.get();

        if (!querySnapshot.empty) {
          console.log(`[Timetable API] Found ${querySnapshot.size} session(s) for student ID format: ${format}`);
          totalSessionsFound += querySnapshot.size;

          // Process each session document
          querySnapshot.docs.forEach(sessionDoc => {
            const sessionData = sessionDoc.data();
            const sessionId = sessionDoc.id;

            console.log(`[Timetable API] Processing session ${sessionId}:`, {
              lesson_ref: sessionData.lesson_ref,
              current_phase: sessionData.current_phase,
              last_updated: sessionData.last_updated,
              student_id: sessionData.student_id
            });

            // Extract lesson reference and completion data
            const lessonRef = sessionData.lesson_ref;
            if (lessonRef) {
              // Determine status from current_phase
              let status = 'upcoming';
              if (sessionData.current_phase === 'lesson_completion' || sessionData.current_phase === 'completed') {
                status = 'completed';
              } else if (sessionData.current_phase && sessionData.current_phase !== 'diagnostic_start_probe') {
                status = 'in_progress';
              }

              // Check if we already have data for this lesson reference
              const existingData = completionStates[lessonRef];
              const currentTimestamp = sessionData.last_updated?.toDate?.() || sessionData.last_updated || new Date(0);
              const existingTimestamp = existingData?.lastUpdated || new Date(0);

              // Use the most recent session data for this lesson reference
              if (!existingData || currentTimestamp > existingTimestamp) {
                completionStates[lessonRef] = {
                  status: status,
                  completed: status === 'completed',
                  completedAt: status === 'completed' ? currentTimestamp : null,
                  progress: status === 'completed' ? 100 : (status === 'in_progress' ? 50 : 0),
                  sessionId: sessionId,
                  currentPhase: sessionData.current_phase,
                  workingLevel: sessionData.current_session_working_level,
                  lastUpdated: currentTimestamp,
                  createdAt: sessionData.created_at?.toDate?.() || sessionData.created_at,
                  studentId: sessionData.student_id,
                  lessonRef: lessonRef
                };
              }
            } else {
              console.warn(`[Timetable API] Session ${sessionId} missing lesson_ref field`);
            }
          });

          // Found sessions, no need to try other student ID formats
          break;
        } else {
          console.log(`[Timetable API] No sessions found for student ID format: ${format}`);
        }
      } catch (formatError) {
        console.warn(`[Timetable API] Error querying lesson states for format ${format}:`, formatError);
        continue;
      }
    }

    const completionCount = Object.keys(completionStates).length;
    console.log(`[Timetable API] Processed ${totalSessionsFound} session(s), found ${completionCount} unique lesson completion records`);

    // Log summary of completion states
    if (completionCount > 0) {
      const statusSummary = Object.values(completionStates).reduce((acc: any, state: any) => {
        acc[state.status] = (acc[state.status] || 0) + 1;
        return acc;
      }, {});
      console.log(`[Timetable API] Completion status summary:`, statusSummary);
    }

    return completionStates;

  } catch (error) {
    console.error('[Timetable API] Error fetching lesson completion states:', error);
    return {};
  }
}

// Verify Firebase Auth token
async function verifyAuthToken(request: NextRequest): Promise<{ uid: string } | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return null;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await getAuth().verifyIdToken(idToken);
    return { uid: decodedToken.uid };
  } catch (error) {
    console.error('Auth verification failed:', error);
    return null;
  }
}

// Fill any remaining empty slots with "Free Period"
function fillEmptySlotsWithFreePeriods(
  weekSchedule: (any | null)[][],
  academicWeek: number,
  studentGrade: string
): void {
  const abbreviatedGrade = convertGradeToAbbreviation(studentGrade);
  let freePeriodCounter = 1;

  for (let dayIndex = 0; dayIndex < DAYS_OF_WEEK.length; dayIndex++) {
    for (let timeIndex = 0; timeIndex < STANDARD_TIME_SLOTS.length; timeIndex++) {
      if (weekSchedule[dayIndex][timeIndex] === null) {
        const freePeriod = {
          id: `free_period_week${academicWeek}_${DAYS_OF_WEEK[dayIndex]}_${timeIndex}`,
          lessonReference: `${abbreviatedGrade}-${String(academicWeek).padStart(3, '0')}-${String(freePeriodCounter).padStart(3, '0')}`,
          title: 'Free Period',
          subject: 'Free Period',
          subjectId: 'free_period',
          subjectCode: null, // Remove subject code for free periods
          time: STANDARD_TIME_SLOTS[timeIndex],
          day: DAYS_OF_WEEK[dayIndex],
          duration: 45,
          status: 'upcoming',
          description: 'Free study period',
          grade: studentGrade,
          academicWeek: academicWeek,
          lessonNumberInWeek: freePeriodCounter,
          absoluteLessonNumber: null,
          totalWeeks: 30,
          teacher: null,
          cognitiveLoad: 1, // Lowest cognitive load
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isFreePeriod: true
        };

        weekSchedule[dayIndex][timeIndex] = freePeriod;
        freePeriodCounter++;
      }
    }
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if Firebase Admin is properly initialized
    if (!db) {
      console.error('[Timetable API] Firebase Admin not properly initialized');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Database not available'
        },
        { status: 500 }
      );
    }

    // For development/testing, allow unauthenticated requests
    // In production, uncomment the authentication check below
    /*
    const authResult = await verifyAuthToken(request);
    if (!authResult) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    */

    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const date = searchParams.get('date');
    const weekParam = searchParams.get('week');
    
    // Parse week parameter (default to week 1)
    const academicWeek = weekParam ? parseInt(weekParam, 10) : 1;
    
    if (isNaN(academicWeek) || academicWeek < 1 || academicWeek > 30) {
      return NextResponse.json(
        { success: false, error: 'Invalid week number. Must be between 1 and 30.' },
        { status: 400 }
      );
    }
    
    if (!studentId) {
      return NextResponse.json(
        { success: false, error: 'Student ID is required' },
        { status: 400 }
      );
    }

    console.log(`[Timetable API] Fetching timetable for student: ${studentId}, week: ${academicWeek}, date: ${date}`);

    // Fetch student grade level
    const studentGrade = await getStudentGrade(studentId);
    console.log(`[Timetable API] Student grade: ${studentGrade}`);

    // Fetch student enrollments (this is now the single source of truth)
    const enrollments = await fetchStudentEnrollments(studentId);
    
    if (enrollments.length === 0) {
      console.log('[Timetable API] No active enrollments found');
      return NextResponse.json({ 
        success: true,
        data: {
          schedule: [],
          timetable: [],
          totalLessons: 0,
          enrollments: [],
          student_id: studentId,
          academic_week: academicWeek,
          total_weeks: 30,
          student_grade: studentGrade,
          date: date
        }
      });
    }

    // Generate weekly timetable based on enrollments
    const weeklySchedule = distributeLessonsAcrossWeek(enrollments, academicWeek, studentGrade);

    // Fetch lesson completion states from Firestore
    const completionStates = await fetchLessonCompletionStates(studentId);

    // Apply actual completion states to the generated lessons
    const scheduleWithCompletionStates = applyLessonCompletionStates(weeklySchedule, completionStates);

    // Sort weekly schedule by day and time
    const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    scheduleWithCompletionStates.sort((a, b) => {
      const dayA = dayOrder.indexOf(a.day);
      const dayB = dayOrder.indexOf(b.day);

      if (dayA !== dayB) {
        return dayA - dayB;
      }

      // Sort by time within the same day
      if (a.time && b.time) {
        return a.time.localeCompare(b.time);
      }

      return 0;
    });

    console.log(`[Timetable API] Returning ${scheduleWithCompletionStates.length} lessons for week ${academicWeek}`);
    
    return NextResponse.json({
      success: true,
      data: {
        schedule: scheduleWithCompletionStates,
        timetable: scheduleWithCompletionStates, // Keep both for backward compatibility
        totalLessons: scheduleWithCompletionStates.length,
        enrollments: enrollments,
        student_id: studentId,
        academic_week: academicWeek,
        total_weeks: 30,
        student_grade: studentGrade,
        date: date
      }
    });

  } catch (error) {
    console.error('[Timetable API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch timetable',
        details: error instanceof Error ? error.stack : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
