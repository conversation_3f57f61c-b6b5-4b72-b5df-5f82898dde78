"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/student-dashboard/[id]/page",{

/***/ "(app-pages-browser)/./src/app/components/dashboard/SummaryTab.tsx":
/*!*****************************************************!*\
  !*** ./src/app/components/dashboard/SummaryTab.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryTab)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/list-checks.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BookOpen,Brain,Calendar,CheckCircle,ChevronRight,Clock,Cpu,Eye,ListChecks,Loader2,MessageSquare,PlayCircle,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/../../../../../../node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_LoadingState__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LoadingState */ \"(app-pages-browser)/./src/components/LoadingState.tsx\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/LessonProgressService */ \"(app-pages-browser)/./src/services/LessonProgressService.ts\");\n/* harmony import */ var _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/providers/ClientToastWrapper */ \"(app-pages-browser)/./src/app/providers/ClientToastWrapper.tsx\");\n/* harmony import */ var _lib_authService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/authService */ \"(app-pages-browser)/./src/lib/authService.ts\");\n/* harmony import */ var _lib_time_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/time-utils */ \"(app-pages-browser)/./src/lib/time-utils.ts\");\n// src/app/components/dashboard/SummaryTab.tsx\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n // Combined imports\n\n\n // For assignment priority\n\n\n\n\n // Updated to match project structure\n // Import getAuthHeaders\n // Import standardized time utilities including getCurrentAcademicWeek\n// --- Inline Progress Component ---\nconst getProgressColor = (value)=>{\n    if (value >= 80) return 'bg-green-500';\n    if (value >= 60) return 'bg-blue-500';\n    if (value >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n};\nconst Progress = (param)=>{\n    let { value = 0, className = \"\" } = param;\n    const bgColor = getProgressColor(value);\n    const widthPercentage = Math.max(0, Math.min(100, value)); // Ensure value is between 0 and 100\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-2 w-full bg-gray-200 rounded-full overflow-hidden \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full rounded-full \".concat(bgColor, \" transition-all duration-300 ease-in-out\"),\n            style: {\n                width: \"\".concat(widthPercentage, \"%\")\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Progress;\n// --- Main Component ---\n// Floating action button for parents\nconst ParentFAB = (param)=>{\n    let { isParent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    if (!isParent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"outline\",\n            className: \"rounded-full p-4 shadow-lg bg-white hover:bg-gray-50\",\n            onClick: ()=>router.push('/parent-dashboard'),\n            children: \"Back to Parent Dashboard\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ParentFAB, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c1 = ParentFAB;\nfunction SummaryTab(param) {\n    let { studentId, studentData, gradeLevel, isParentView = false, onTabChange = ()=>{}, onStartLesson = ()=>{} } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_7__.getAuth)(); // Get auth instance\n    const { toast } = (0,_app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__.useToast)(); // Use the toast hook properly\n    // --- State ---\n    const [todaysSchedule, setTodaysSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingSchedule, setLoadingSchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleError, setScheduleError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [lessonProgress, setLessonProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loadingProgress, setLoadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progressError, setProgressError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // --- Fetch Today's Schedule ---\n    const fetchTodaysSchedule = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SummaryTab.useCallback[fetchTodaysSchedule]\": async ()=>{\n            if (!studentId) return;\n            console.log(\"[SummaryTab] Fetching schedule for \".concat(studentId));\n            setLoadingSchedule(true);\n            setScheduleError(null);\n            setTodaysSchedule(null); // Clear previous schedule\n            try {\n                var _result_data;\n                // Get current academic week instead of using date\n                const currentAcademicWeek = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentAcademicWeek)();\n                const today = new Date();\n                const currentDayName = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentDayName)(); // Get current day name (e.g., \"monday\")\n                console.log(\"[SummaryTab] Fetching schedule for week: \".concat(currentAcademicWeek, \", day: \").concat(currentDayName));\n                // Use week-based API instead of date-based\n                const response = await fetch(\"/api/timetable?studentId=\".concat(studentId, \"&week=\").concat(currentAcademicWeek), {\n                    headers: (0,_lib_authService__WEBPACK_IMPORTED_MODULE_10__.getAuthHeaders)()\n                });\n                const result = await response.json();\n                if (response.ok && result.success && Array.isArray((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.schedule)) {\n                    console.log(\"[SummaryTab] Received \".concat(result.data.schedule.length, \" schedule items from API.\"));\n                    // Filter for today's lessons only\n                    const todaysLessons = result.data.schedule.filter({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule].todaysLessons\": (item)=>{\n                            var _item_day;\n                            // Check if the lesson is for today\n                            const lessonDay = (_item_day = item.day) === null || _item_day === void 0 ? void 0 : _item_day.toLowerCase();\n                            return lessonDay === currentDayName.toLowerCase();\n                        }\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule].todaysLessons\"]);\n                    console.log(\"[SummaryTab] Filtered \".concat(todaysLessons.length, \" lessons for today (\").concat(currentDayName, \").\"));\n                    // Process items *without* relying on API status for sorting/initial display state\n                    const processedSchedule = todaysLessons.map({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule].processedSchedule\": (item)=>({\n                                ...item,\n                                // Use status from API primarily for initial visual cue, but rely on time/progress for buttons\n                                statusFromApi: item.status || 'upcoming',\n                                time: item.time || 'Time not set',\n                                subject: item.subject || 'Untitled Lesson',\n                                lessonRef: item.lessonRef || null,\n                                day: item.day,\n                                // *** Ensure these fields are present from API ***\n                                grade: item.grade || (studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel) || '',\n                                level: item.level || '',\n                                curriculum: item.curriculum || '',\n                                country: item.country || '',\n                                academicWeek: item.academicWeek || currentAcademicWeek\n                            })\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule].processedSchedule\"]);\n                    // *** MODIFIED SORTING: Strictly Chronological ***\n                    processedSchedule.sort({\n                        \"SummaryTab.useCallback[fetchTodaysSchedule]\": (a, b)=>{\n                            try {\n                                // Helper to parse start time string (e.g., \"08:00\") into minutes since midnight\n                                const parseStartTimeToMinutes = {\n                                    \"SummaryTab.useCallback[fetchTodaysSchedule].parseStartTimeToMinutes\": (timeStr)=>{\n                                        if (!timeStr || !timeStr.includes(' - ')) return Infinity; // Handle invalid format\n                                        const startTimePart = timeStr.split(' - ')[0].trim();\n                                        const [hours, minutes] = startTimePart.split(':').map(Number);\n                                        if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {\n                                            console.warn(\"[Sort] Invalid time format detected: \".concat(startTimePart));\n                                            return Infinity; // Put invalid times last\n                                        }\n                                        return hours * 60 + minutes;\n                                    }\n                                }[\"SummaryTab.useCallback[fetchTodaysSchedule].parseStartTimeToMinutes\"];\n                                const aStartMinutes = parseStartTimeToMinutes(a.time);\n                                const bStartMinutes = parseStartTimeToMinutes(b.time);\n                                return aStartMinutes - bStartMinutes;\n                            } catch (e) {\n                                console.error(\"[Sort] Error parsing time for sorting:\", a.time, b.time, e);\n                                return 0; // Avoid crashing sort on error\n                            }\n                        }\n                    }[\"SummaryTab.useCallback[fetchTodaysSchedule]\"]);\n                    console.log(\"[SummaryTab] Sorted schedule chronologically.\");\n                    setTodaysSchedule(processedSchedule);\n                    // *** MODIFIED: Call fetchLessonProgress WITHOUT lessonRefs ***\n                    // Fetch progress data after schedule is set\n                    if (processedSchedule.length > 0) {\n                        // Check if *any* item has a lessonRef before fetching progress\n                        const hasRefs = processedSchedule.some({\n                            \"SummaryTab.useCallback[fetchTodaysSchedule].hasRefs\": (item)=>item.lessonRef\n                        }[\"SummaryTab.useCallback[fetchTodaysSchedule].hasRefs\"]);\n                        if (hasRefs) {\n                            fetchLessonProgress(); // Call without arguments\n                        } else {\n                            setLessonProgress({}); // No lessons with refs, clear progress\n                            setLoadingProgress(false);\n                            console.log(\"[SummaryTab] No lessonRefs found in schedule, skipping progress fetch.\");\n                        }\n                    } else {\n                        setLoadingProgress(false); // No schedule items, no progress to load\n                    }\n                } else {\n                    throw new Error(result.error || result.message || \"Could not load today's schedule from the weekly timetable.\");\n                }\n            } catch (error) {\n                console.error('[SummaryTab] Error fetching schedule:', error);\n                setScheduleError(error.message || \"An unexpected error occurred\");\n                setTodaysSchedule([]); // Set empty array on error\n            } finally{\n                setLoadingSchedule(false);\n            }\n        }\n    }[\"SummaryTab.useCallback[fetchTodaysSchedule]\"], [\n        studentId,\n        studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel\n    ]); // Add studentData dependency if using fallback\n    // --- Fetch Lesson Progress (Modified to fetch ALL) ---\n    // *** REMOVED lessonRefs parameter ***\n    const fetchLessonProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SummaryTab.useCallback[fetchLessonProgress]\": async ()=>{\n            if (!studentId) {\n                setLessonProgress({});\n                setLoadingProgress(false);\n                return;\n            }\n            console.log(\"[SummaryTab] Fetching ALL lesson progress for student \".concat(studentId, \"...\"));\n            setLoadingProgress(true);\n            setProgressError(null);\n            try {\n                // *** Use getAllLessonProgress instead ***\n                const progressMap = await _services_LessonProgressService__WEBPACK_IMPORTED_MODULE_8__.LessonProgressService.getAllLessonProgress(studentId);\n                console.log(\"[SummaryTab] Fetched all progress data (\".concat(Object.keys(progressMap).length, \" entries):\"), progressMap);\n                setLessonProgress(progressMap);\n            } catch (error) {\n                console.error('[SummaryTab] Error fetching all lesson progress:', error);\n                // Check if the error is because the function doesn't exist\n                if (error instanceof TypeError && error.message.includes(\"is not a function\")) {\n                    setProgressError(\"Service Error: getAllLessonProgress method not found.\");\n                    console.error(\"LessonProgressService might be missing the getAllLessonProgress static method.\");\n                } else {\n                    setProgressError(error.message || 'Could not load lesson progress.');\n                }\n                setLessonProgress({}); // Clear progress on error\n            } finally{\n                setLoadingProgress(false);\n            }\n        // Keep only studentId as dependency, as we fetch all progress based on it\n        }\n    }[\"SummaryTab.useCallback[fetchLessonProgress]\"], [\n        studentId\n    ]);\n    // Fetch schedule on initial load or when studentId changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SummaryTab.useEffect\": ()=>{\n            if (studentId) {\n                fetchTodaysSchedule(); // This will trigger fetchLessonProgress if needed\n            } else {\n                // Clear state if studentId becomes null/undefined\n                setTodaysSchedule(null);\n                setLessonProgress({});\n                setLoadingSchedule(false);\n                setLoadingProgress(false);\n            }\n        }\n    }[\"SummaryTab.useEffect\"], [\n        studentId,\n        fetchTodaysSchedule\n    ]);\n    // --- Derived Data ---\n    const upcomingAssignments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[upcomingAssignments]\": ()=>(studentData === null || studentData === void 0 ? void 0 : studentData.upcomingAssignments) || []\n    }[\"SummaryTab.useMemo[upcomingAssignments]\"], [\n        studentData\n    ]);\n    const recentActivities = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[recentActivities]\": ()=>(studentData === null || studentData === void 0 ? void 0 : studentData.recentActivities) || []\n    }[\"SummaryTab.useMemo[recentActivities]\"], [\n        studentData\n    ]);\n    const subjectProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[subjectProgress]\": ()=>{\n            var _studentData_completionStatus;\n            return (studentData === null || studentData === void 0 ? void 0 : (_studentData_completionStatus = studentData.completionStatus) === null || _studentData_completionStatus === void 0 ? void 0 : _studentData_completionStatus.subjects) || {};\n        }\n    }[\"SummaryTab.useMemo[subjectProgress]\"], [\n        studentData\n    ]);\n    const displayGradeLevel = gradeLevel || (studentData === null || studentData === void 0 ? void 0 : studentData.gradeLevel) || 'N/A';\n    const overallProgressValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[overallProgressValue]\": ()=>{\n            var _studentData_overallProgress;\n            return (_studentData_overallProgress = studentData === null || studentData === void 0 ? void 0 : studentData.overallProgress) !== null && _studentData_overallProgress !== void 0 ? _studentData_overallProgress : 0;\n        }\n    }[\"SummaryTab.useMemo[overallProgressValue]\"], [\n        studentData\n    ]);\n    // Keep only this declaration of completedTodayCount\n    const completedTodayCount = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[completedTodayCount]\": ()=>{\n            var _todaysSchedule_filter_length;\n            return (_todaysSchedule_filter_length = todaysSchedule === null || todaysSchedule === void 0 ? void 0 : todaysSchedule.filter({\n                \"SummaryTab.useMemo[completedTodayCount]\": (item)=>item.statusFromApi === 'completed'\n            }[\"SummaryTab.useMemo[completedTodayCount]\"]).length) !== null && _todaysSchedule_filter_length !== void 0 ? _todaysSchedule_filter_length : 0;\n        }\n    }[\"SummaryTab.useMemo[completedTodayCount]\"], [\n        todaysSchedule\n    ]);\n    // Recalculate nextLesson based on the *chronologically sorted* list\n    const nextLesson = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SummaryTab.useMemo[nextLesson]\": ()=>{\n            if (!todaysSchedule) return null;\n            const now = new Date();\n            const currentTimeMinutes = now.getHours() * 60 + now.getMinutes();\n            // Helper to parse start time string (e.g., \"08:00\") into minutes\n            const parseStartTime = {\n                \"SummaryTab.useMemo[nextLesson].parseStartTime\": (timeStr)=>{\n                    try {\n                        if (!timeStr || !timeStr.includes(' - ')) return Infinity;\n                        const startTimePart = timeStr.split(' - ')[0].trim();\n                        const [hours, minutes] = startTimePart.split(':').map(Number);\n                        if (isNaN(hours) || isNaN(minutes)) return Infinity;\n                        return hours * 60 + minutes;\n                    } catch (e) {\n                        return Infinity;\n                    }\n                }\n            }[\"SummaryTab.useMemo[nextLesson].parseStartTime\"];\n            // Helper to parse end time string (e.g., \"08:45\") into minutes\n            const parseEndTime = {\n                \"SummaryTab.useMemo[nextLesson].parseEndTime\": (timeStr)=>{\n                    try {\n                        if (!timeStr || !timeStr.includes(' - ')) return -1;\n                        const endTimePart = timeStr.split(' - ')[1].trim();\n                        const [hours, minutes] = endTimePart.split(':').map(Number);\n                        if (isNaN(hours) || isNaN(minutes)) return -1;\n                        return hours * 60 + minutes;\n                    } catch (e) {\n                        return -1;\n                    }\n                }\n            }[\"SummaryTab.useMemo[nextLesson].parseEndTime\"];\n            // Priority 1: Find the first lesson that is currently happening based on time\n            const currentLesson = todaysSchedule.find({\n                \"SummaryTab.useMemo[nextLesson].currentLesson\": (item)=>{\n                    const startMinutes = parseStartTime(item.time);\n                    const endMinutes = parseEndTime(item.time);\n                    return currentTimeMinutes >= startMinutes && currentTimeMinutes < endMinutes;\n                }\n            }[\"SummaryTab.useMemo[nextLesson].currentLesson\"]);\n            if (currentLesson) {\n                console.log(\"[SummaryTab] Next Lesson (P1 - Current by Time):\", currentLesson.subject);\n                return {\n                    ...currentLesson,\n                    derivedStatus: 'current'\n                }; // Add derived status\n            }\n            // Priority 2: Find the first lesson starting later today\n            const upcomingLesson = todaysSchedule.find({\n                \"SummaryTab.useMemo[nextLesson].upcomingLesson\": (item)=>{\n                    const startMinutes = parseStartTime(item.time);\n                    return startMinutes > currentTimeMinutes;\n                }\n            }[\"SummaryTab.useMemo[nextLesson].upcomingLesson\"]);\n            if (upcomingLesson) {\n                console.log(\"[SummaryTab] Next Lesson (P2 - Upcoming by Time):\", upcomingLesson.subject);\n                return {\n                    ...upcomingLesson,\n                    derivedStatus: 'upcoming'\n                }; // Add derived status\n            }\n            // If no current or upcoming found (all might be completed by time)\n            console.log(\"[SummaryTab] No current or upcoming lesson found based on time.\");\n            return null;\n        }\n    }[\"SummaryTab.useMemo[nextLesson]\"], [\n        todaysSchedule\n    ]);\n    // Function to determine button state using standardized time utilities\n    const getButtonState = (item)=>{\n        const lessonRef = item.lessonRef;\n        const progressData = lessonRef ? lessonProgress[lessonRef] : null;\n        const progressStatus = (progressData === null || progressData === void 0 ? void 0 : progressData.status) || 'not_started';\n        // Fix isStudent detection: For students, buttons should be enabled when not in parent view\n        // The auth comparison was causing issues since studentId (username) != auth.currentUser.uid (Firebase UID)\n        // Instead, rely on isParentView prop which correctly indicates if this is a parent viewing\n        const isStudent = !isParentView;\n        // Use API status as primary source of truth for actual completion\n        // Only use time-based status for current/upcoming determination\n        const apiStatus = item.statusFromApi || 'upcoming';\n        // Determine the effective lesson status\n        let effectiveStatus = apiStatus;\n        // If API says upcoming, check if it should be current based on time\n        if (apiStatus === 'upcoming') {\n            const timeStatus = (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.calculateLessonStatus)(item.time, item.day || (0,_lib_time_utils__WEBPACK_IMPORTED_MODULE_11__.getCurrentDayName)(), new Date(), progressStatus);\n            // Only override to 'current' if time-based calculation says it's current\n            // Never override to 'completed' based on time alone\n            if (timeStatus.status === 'current') {\n                effectiveStatus = 'current';\n            }\n        }\n        console.log('[SummaryTab] Lesson status for \"'.concat(item.subject, '\": ').concat(effectiveStatus), {\n            apiStatus,\n            timeRange: item.time,\n            day: item.day,\n            progressStatus,\n            lessonRef\n        });\n        // --- Define Action with ALL parameters ---\n        let action = ()=>{\n            if (lessonRef) {\n                // Collect all available data from the item\n                const params = {\n                    lessonRef: lessonRef,\n                    studentId: studentId || '',\n                    subject: item.subject || '',\n                    grade: item.grade || '',\n                    level: item.level || '',\n                    curriculum: item.curriculum || '',\n                    country: item.country || ''\n                };\n                // Check if we have the minimum required data\n                const missingCriticalParams = [\n                    'lessonRef',\n                    'studentId'\n                ].filter((key)=>!params[key]);\n                if (missingCriticalParams.length > 0) {\n                    console.error(\"Cannot navigate to lesson: Missing critical parameters: \".concat(missingCriticalParams.join(', ')));\n                    toast({\n                        title: \"Error\",\n                        description: \"Cannot start lesson: Missing critical information\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Log and navigate\n                console.log('[SummaryTab] Navigating to lesson with params:', params);\n                const queryParams = new URLSearchParams(params).toString();\n                router.push(\"/classroom?\".concat(queryParams));\n            }\n        };\n        let buttonText = 'View';\n        let buttonVariant = 'secondary';\n        let buttonClass = 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n        let buttonDisabled = !lessonRef; // Generally disabled if no lessonRef\n        let IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        // --- Standardized Logic Flow ---\n        if (effectiveStatus === 'completed') {\n            // For students, update action to go to /start-lesson for review as well\n            if (isStudent) {\n                action = ()=>{\n                    if (lessonRef && studentId) {\n                        const params = {\n                            lessonRef: lessonRef,\n                            studentId: studentId || '',\n                            subject: item.subject || '',\n                            grade: item.grade || '',\n                            level: item.level || '',\n                            curriculum: item.curriculum || '',\n                            country: item.country || ''\n                        };\n                        const missingCriticalParams = [\n                            'lessonRef',\n                            'studentId'\n                        ].filter((key)=>!params[key]);\n                        if (missingCriticalParams.length > 0) {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot review lesson: Missing critical information\",\n                                variant: \"destructive\"\n                            });\n                            return;\n                        }\n                        const queryParams = new URLSearchParams(params).toString();\n                        router.push(\"/start-lesson?\".concat(queryParams));\n                    } else {\n                        toast({\n                            title: \"Error\",\n                            description: \"Cannot review lesson: Missing lessonRef or studentId\",\n                            variant: \"destructive\"\n                        });\n                    }\n                };\n            }\n            buttonText = isStudent ? 'Review' : 'View';\n            buttonVariant = 'outline';\n            buttonClass = isStudent ? 'text-green-700 bg-green-50 hover:bg-green-100 border-green-300' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n            buttonDisabled = !lessonRef;\n            IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n        } else if (effectiveStatus === 'current') {\n            // Lesson is happening now\n            if (progressStatus === 'in_progress') {\n                buttonText = isStudent ? 'Resume' : 'View';\n                buttonVariant = 'outline';\n                buttonClass = isStudent ? 'text-yellow-800 bg-yellow-50 hover:bg-yellow-100 border-yellow-300' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n                buttonDisabled = !isStudent || !lessonRef;\n                IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n            } else {\n                buttonText = isStudent ? 'Join' : 'View';\n                buttonVariant = isStudent ? 'default' : 'outline';\n                buttonClass = isStudent ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'text-gray-700 bg-white hover:bg-gray-100 border-gray-300';\n                buttonDisabled = !isStudent || !lessonRef;\n                IconComponent = isStudent ? _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                // Update action to go to /start-lesson for students\n                if (isStudent) {\n                    action = ()=>{\n                        if (lessonRef && studentId) {\n                            const params = {\n                                lessonRef: lessonRef,\n                                studentId: studentId || '',\n                                subject: item.subject || '',\n                                grade: item.grade || '',\n                                level: item.level || '',\n                                curriculum: item.curriculum || '',\n                                country: item.country || ''\n                            };\n                            const missingCriticalParams = [\n                                'lessonRef',\n                                'studentId'\n                            ].filter((key)=>!params[key]);\n                            if (missingCriticalParams.length > 0) {\n                                toast({\n                                    title: \"Error\",\n                                    description: \"Cannot start lesson: Missing critical information\",\n                                    variant: \"destructive\"\n                                });\n                                return;\n                            }\n                            const queryParams = new URLSearchParams(params).toString();\n                            router.push(\"/start-lesson?\".concat(queryParams));\n                        } else {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot start lesson: Missing lessonRef or studentId\",\n                                variant: \"destructive\"\n                            });\n                        }\n                    };\n                }\n            }\n        } else {\n            // Future lesson (upcoming)\n            // Enable buttons for upcoming lessons so students can start them\n            if (isStudent && lessonRef) {\n                buttonText = 'Start';\n                buttonVariant = 'default';\n                buttonClass = 'bg-blue-600 hover:bg-blue-700 text-white';\n                buttonDisabled = false;\n                IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"];\n                action = ()=>{\n                    if (lessonRef && studentId) {\n                        const params = {\n                            lessonRef: lessonRef,\n                            studentId: studentId || '',\n                            subject: item.subject || '',\n                            grade: item.grade || '',\n                            level: item.level || '',\n                            curriculum: item.curriculum || '',\n                            country: item.country || ''\n                        };\n                        const missingCriticalParams = [\n                            'lessonRef',\n                            'studentId'\n                        ].filter((key)=>!params[key]);\n                        if (missingCriticalParams.length > 0) {\n                            toast({\n                                title: \"Error\",\n                                description: \"Cannot start lesson: Missing critical information\",\n                                variant: \"destructive\"\n                            });\n                            return;\n                        }\n                        const queryParams = new URLSearchParams(params).toString();\n                        router.push(\"/start-lesson?\".concat(queryParams));\n                    } else {\n                        toast({\n                            title: \"Error\",\n                            description: \"Cannot start lesson: Missing lessonRef or studentId\",\n                            variant: \"destructive\"\n                        });\n                    }\n                };\n            } else {\n                // For parents or lessons without lessonRef\n                buttonText = 'Upcoming';\n                buttonVariant = 'secondary';\n                buttonClass = 'text-gray-500 bg-gray-200 cursor-not-allowed';\n                buttonDisabled = true;\n                action = ()=>{};\n                IconComponent = _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"];\n            }\n        }\n        console.log('[SummaryTab] Final button state for \"'.concat(item.subject, '\":\\n      - Effective Status: ').concat(effectiveStatus, \"\\n      - API Status: \").concat(apiStatus, \"\\n      - Progress status: \").concat(progressStatus, \"\\n      - Button text: \").concat(buttonText, \"\\n      - Button disabled: \").concat(buttonDisabled, \"\\n      - Is Student: \").concat(isStudent, \"\\n      - Has lessonRef: \").concat(!!lessonRef));\n        return {\n            buttonText,\n            buttonVariant,\n            buttonClass,\n            buttonDisabled,\n            action,\n            IconComponent\n        };\n    };\n    // --- Render Logic ---\n    if (!studentData && !isParentView) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        message: \"Loading student data...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 553,\n        columnNumber: 45\n    }, this);\n    if (loadingSchedule && !todaysSchedule) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingState__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        message: \"Loading schedule...\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 554,\n        columnNumber: 50\n    }, this); // Show loading if schedule is null\n    // Helper function for priority badge class\n    const getPriorityBadgeClass = (priority)=>{\n        switch(priority.toLowerCase()){\n            case 'high':\n                return 'bg-red-100 text-red-800';\n            case 'medium':\n                return 'bg-yellow-100 text-yellow-800';\n            case 'low':\n                return 'bg-green-100 text-green-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    // Helper function for activity icon\n    const getActivityIcon = (type)=>{\n        switch(type.toLowerCase()){\n            case 'quiz':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n            case 'assignment':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n            case 'lesson':\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n            default:\n                return _barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        }\n    };\n    var _todaysSchedule_length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"lg:col-span-1 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 shadow-md hover:shadow-lg transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg font-semibold text-blue-800 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: 20,\n                                            className: \"mr-2 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Next Up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 14\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"flex flex-col items-center justify-center text-center pt-2 pb-6 min-h-[200px]\",\n                                children: [\n                                    \" \",\n                                    loadingSchedule || loadingProgress ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-500 my-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 21\n                                    }, this) : scheduleError || progressError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-red-600 text-sm px-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"inline h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 25\n                                            }, this),\n                                            scheduleError || progressError || \"Error loading data\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 21\n                                    }, this) : nextLesson ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-gray-800 mb-1 truncate w-full px-4\",\n                                                title: nextLesson.subject,\n                                                children: nextLesson.subject\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-base text-gray-600 mb-4\",\n                                                children: nextLesson.derivedStatus === 'current' ? \"In Progress (\".concat(nextLesson.time.split(' - ')[1], \")\") : \"Starts at \".concat(nextLesson.time.split(' - ')[0])\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 25\n                                            }, this),\n                                            (()=>{\n                                                const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(nextLesson);\n                                                const isStudent = !isParentView;\n                                                // Adjust button text/action specifically for the \"Next Up\" card\n                                                let nextUpText = buttonText;\n                                                let nextUpAction = action;\n                                                let nextUpDisabled = buttonDisabled;\n                                                let nextUpClass = buttonClass;\n                                                let nextUpVariant = buttonVariant;\n                                                if (nextLesson.derivedStatus === 'current' && isStudent && !buttonDisabled) {\n                                                    nextUpText = 'Join Lesson Now';\n                                                    nextUpClass = 'bg-blue-600 hover:bg-blue-700 text-white'; // Ensure correct class for Join\n                                                    nextUpVariant = 'default';\n                                                } else if (nextLesson.derivedStatus === 'upcoming') {\n                                                    nextUpText = 'View on Timetable';\n                                                    nextUpAction = ()=>onTabChange('timetable');\n                                                    nextUpDisabled = false; // Allow viewing timetable\n                                                    nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';\n                                                    nextUpVariant = 'default'; // Use default style for primary action\n                                                } else if (buttonText === 'Review' && isStudent) {\n                                                    // If it's reviewable but also the \"next\" item (unlikely unless only one lesson)\n                                                    nextUpText = 'Review Lesson';\n                                                } else if (!isStudent) {\n                                                    // Parent view adjustments for Next Up\n                                                    nextUpText = 'View Details';\n                                                    nextUpAction = ()=>onTabChange('timetable');\n                                                    nextUpDisabled = false;\n                                                    nextUpClass = 'bg-gray-500 hover:bg-gray-600 text-white';\n                                                    nextUpVariant = 'default';\n                                                }\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    size: \"lg\",\n                                                    variant: nextUpVariant,\n                                                    className: \"w-full max-w-xs \".concat(nextUpClass, \" font-semibold transition-colors shadow-md hover:shadow-lg\"),\n                                                    onClick: nextUpAction,\n                                                    disabled: nextUpDisabled,\n                                                    children: [\n                                                        nextUpText,\n                                                        !nextUpDisabled && nextUpAction !== onTabChange ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 657,\n                                                            columnNumber: 88\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 33\n                                                }, this);\n                                            })()\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-10 w-10 text-green-500 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-gray-700\",\n                                                children: \"All Classes Done!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: \"Great job today!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                className: \"mt-4\",\n                                                onClick: ()=>onTabChange('timetable'),\n                                                children: \" View Full Timetable \"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 26\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 10\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Overall Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 18,\n                                                    className: \"text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-800\",\n                                            children: [\n                                                overallProgressValue,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                            value: overallProgressValue,\n                                            className: \"h-1.5 mt-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-gray-500\",\n                                                    children: \"Today's Classes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 695,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    size: 18,\n                                                    className: \"text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        loadingSchedule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6 animate-spin text-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 18\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-3xl font-bold text-gray-800\",\n                                                    children: (_todaysSchedule_length = todaysSchedule === null || todaysSchedule === void 0 ? void 0 : todaysSchedule.length) !== null && _todaysSchedule_length !== void 0 ? _todaysSchedule_length : 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: [\n                                                        completedTodayCount,\n                                                        \" completed\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5 flex flex-col justify-between h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"AI Form Tutor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            size: 18,\n                                                            className: \"text-amber-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 mb-3\",\n                                                    children: \"Need help or guidance?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 716,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            className: \"w-full border-amber-400 text-amber-700 hover:bg-amber-50\",\n                                            onClick: ()=>router.push('/ai-form-tutor'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 14,\n                                                    className: \"mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" Ask Tutor\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-lg font-semibold text-gray-800\",\n                                        children: \"Today's Schedule\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 737,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"pt-2 pb-4 pr-2\",\n                                    children: loadingSchedule && !todaysSchedule ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex items-center justify-center text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-6 w-6 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" Loading Schedule...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 18\n                                    }, this) : scheduleError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex flex-col items-center justify-center text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-8 w-8 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 95\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: scheduleError\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 136\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 18\n                                    }, this) : todaysSchedule && todaysSchedule.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative pl-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 19\n                                            }, this),\n                                            todaysSchedule.map((item, index)=>{\n                                                var _lessonProgress_lessonRef;\n                                                const { buttonText, buttonVariant, buttonClass, buttonDisabled, action, IconComponent } = getButtonState(item);\n                                                const lessonRef = item.lessonRef;\n                                                const progressStatus = lessonRef ? (_lessonProgress_lessonRef = lessonProgress[lessonRef]) === null || _lessonProgress_lessonRef === void 0 ? void 0 : _lessonProgress_lessonRef.status : 'not_started';\n                                                const timeStatusFromApi = item.statusFromApi; // Use the status calculated by API for dot color\n                                                // Determine dot color based on API status primarily\n                                                let dotClass = 'bg-gray-300'; // Default: upcoming\n                                                if (timeStatusFromApi === 'completed' || progressStatus === 'completed') {\n                                                    dotClass = 'bg-green-500';\n                                                } else if (timeStatusFromApi === 'current') {\n                                                    dotClass = 'bg-blue-500 ring-4 ring-blue-200 animate-pulse';\n                                                } else if (progressStatus === 'in_progress') {\n                                                    dotClass = 'bg-yellow-500'; // In progress but maybe not current time\n                                                }\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative pl-8 py-4 group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-[18px] top-7 -ml-[7px] w-3.5 h-3.5 rounded-full border-2 border-white group-hover:scale-110 transition-transform \".concat(dotClass)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 769,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between gap-4 ml-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-grow min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs font-medium text-gray-500 uppercase tracking-wider mb-0.5\",\n                                                                            children: item.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-gray-800 truncate\",\n                                                                            title: item.subject,\n                                                                            children: item.subject\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 mt-0.5 flex items-center\",\n                                                                            children: [\n                                                                                \" \",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    size: 12,\n                                                                                    className: \"mr-1 text-gray-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                                    lineNumber: 777,\n                                                                                    columnNumber: 92\n                                                                                }, this),\n                                                                                \" \",\n                                                                                item.instructor || \"AI Instructor\",\n                                                                                \" \"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        loadingProgress && lessonRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 italic ml-2\",\n                                                                            children: \" (loading status...)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 779,\n                                                                            columnNumber: 64\n                                                                        }, this),\n                                                                        progressError && lessonRef && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-red-500 ml-2\",\n                                                                            children: \" (status error)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 60\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 774,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0\",\n                                                                    children: loadingProgress && lessonRef ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4 animate-spin text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: buttonVariant,\n                                                                        className: \"px-3 py-1.5 rounded-md text-xs font-medium transition-all shadow-sm hover:shadow-md \".concat(buttonClass),\n                                                                        onClick: action,\n                                                                        disabled: buttonDisabled || loadingProgress,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                size: 14,\n                                                                                className: \"mr-1.5\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                                lineNumber: 795,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            buttonText\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 784,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, item.id || index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 23\n                                                }, this); // End of return for map callback\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-60 flex flex-col items-center justify-center text-center text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-10 w-10 text-gray-400 mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No classes scheduled for today.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 735,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Subject Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            Object.keys(subjectProgress).length > 0 ? Object.entries(subjectProgress).map((param)=>{\n                                                let [subject, progress] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-baseline mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-gray-800\",\n                                                                    children: subject\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-semibold text-gray-600\",\n                                                                    children: [\n                                                                        progress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 826,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Progress, {\n                                                            value: progress,\n                                                            className: \"h-1.5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 830,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, subject, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 825,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center text-gray-500 py-4\",\n                                                children: \"No progress data available.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 834,\n                                                columnNumber: 18\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                size: \"sm\",\n                                                className: \"p-0 h-auto text-blue-600 text-xs\",\n                                                onClick: ()=>onTabChange('reports'),\n                                                children: \"View Detailed Reports\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 18\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"border border-gray-200 shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base font-semibold text-gray-700\",\n                                            children: \"Upcoming Assignments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            upcomingAssignments.length > 0 ? upcomingAssignments.slice(0, 3).map((assignment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between gap-2 p-2 rounded bg-gray-50/50 border border-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BookOpen_Brain_Calendar_CheckCircle_ChevronRight_Clock_Cpu_Eye_ListChecks_Loader2_MessageSquare_PlayCircle_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-purple-500 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-grow min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-800 truncate\",\n                                                                    title: assignment.title,\n                                                                    children: [\n                                                                        assignment.title,\n                                                                        \" (\",\n                                                                        assignment.subject,\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: [\n                                                                        \"Due: \",\n                                                                        assignment.dueDate\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs \".concat(getPriorityBadgeClass(assignment.priority)),\n                                                            children: assignment.priority\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                            lineNumber: 855,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, assignment.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center text-gray-500 py-4\",\n                                                children: \"No upcoming assignments.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 861,\n                                                columnNumber: 18\n                                            }, this),\n                                            upcomingAssignments.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                size: \"sm\",\n                                                className: \"p-0 h-auto text-blue-600 text-xs\",\n                                                onClick: ()=>onTabChange('homework'),\n                                                children: \"View All Assignments\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                                lineNumber: 864,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                                lineNumber: 841,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 732,\n                columnNumber: 7\n            }, this),\n            isParentView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ParentFAB, {\n                isParent: isParentView\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n                lineNumber: 870,\n                columnNumber: 24\n            }, this),\n            \" \"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Desktop\\\\Solynta_Website\\\\frontend\\\\lesson-platform\\\\src\\\\app\\\\components\\\\dashboard\\\\SummaryTab.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_s1(SummaryTab, \"qSsrS3Whspl5xtqgLkkzbJGhNGc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _app_providers_ClientToastWrapper__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c2 = SummaryTab;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Progress\");\n$RefreshReg$(_c1, \"ParentFAB\");\n$RefreshReg$(_c2, \"SummaryTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/dashboard/SummaryTab.tsx\n"));

/***/ })

});