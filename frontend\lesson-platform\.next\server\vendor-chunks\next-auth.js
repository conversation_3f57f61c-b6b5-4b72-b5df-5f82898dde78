/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/core/errors.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/core/errors.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.UnsupportedStrategy = exports.UnknownError = exports.OAuthCallbackError = exports.MissingSecret = exports.MissingAuthorize = exports.MissingAdapterMethods = exports.MissingAdapter = exports.MissingAPIRoute = exports.InvalidCallbackUrl = exports.AccountNotLinkedError = void 0;\nexports.adapterErrorHandler = adapterErrorHandler;\nexports.capitalize = capitalize;\nexports.eventsErrorHandler = eventsErrorHandler;\nexports.upperSnake = upperSnake;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(rsc)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(rsc)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(rsc)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _classCallCheck2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/classCallCheck */ \"(rsc)/./node_modules/@babel/runtime/helpers/classCallCheck.js\"));\nvar _createClass2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/createClass */ \"(rsc)/./node_modules/@babel/runtime/helpers/createClass.js\"));\nvar _possibleConstructorReturn2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/possibleConstructorReturn */ \"(rsc)/./node_modules/@babel/runtime/helpers/possibleConstructorReturn.js\"));\nvar _getPrototypeOf2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/getPrototypeOf */ \"(rsc)/./node_modules/@babel/runtime/helpers/getPrototypeOf.js\"));\nvar _inherits2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/inherits */ \"(rsc)/./node_modules/@babel/runtime/helpers/inherits.js\"));\nvar _wrapNativeSuper2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/wrapNativeSuper */ \"(rsc)/./node_modules/@babel/runtime/helpers/wrapNativeSuper.js\"));\nfunction _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nvar UnknownError = exports.UnknownError = function (_Error) {\n  function UnknownError(error) {\n    var _message;\n    var _this;\n    (0, _classCallCheck2.default)(this, UnknownError);\n    _this = _callSuper(this, UnknownError, [(_message = error === null || error === void 0 ? void 0 : error.message) !== null && _message !== void 0 ? _message : error]);\n    _this.name = \"UnknownError\";\n    _this.code = error.code;\n    if (error instanceof Error) {\n      _this.stack = error.stack;\n    }\n    return _this;\n  }\n  (0, _inherits2.default)(UnknownError, _Error);\n  return (0, _createClass2.default)(UnknownError, [{\n    key: \"toJSON\",\n    value: function toJSON() {\n      return {\n        name: this.name,\n        message: this.message,\n        stack: this.stack\n      };\n    }\n  }]);\n}((0, _wrapNativeSuper2.default)(Error));\nvar OAuthCallbackError = exports.OAuthCallbackError = function (_UnknownError) {\n  function OAuthCallbackError() {\n    var _this2;\n    (0, _classCallCheck2.default)(this, OAuthCallbackError);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this2 = _callSuper(this, OAuthCallbackError, [].concat(args));\n    (0, _defineProperty2.default)(_this2, \"name\", \"OAuthCallbackError\");\n    return _this2;\n  }\n  (0, _inherits2.default)(OAuthCallbackError, _UnknownError);\n  return (0, _createClass2.default)(OAuthCallbackError);\n}(UnknownError);\nvar AccountNotLinkedError = exports.AccountNotLinkedError = function (_UnknownError2) {\n  function AccountNotLinkedError() {\n    var _this3;\n    (0, _classCallCheck2.default)(this, AccountNotLinkedError);\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    _this3 = _callSuper(this, AccountNotLinkedError, [].concat(args));\n    (0, _defineProperty2.default)(_this3, \"name\", \"AccountNotLinkedError\");\n    return _this3;\n  }\n  (0, _inherits2.default)(AccountNotLinkedError, _UnknownError2);\n  return (0, _createClass2.default)(AccountNotLinkedError);\n}(UnknownError);\nvar MissingAPIRoute = exports.MissingAPIRoute = function (_UnknownError3) {\n  function MissingAPIRoute() {\n    var _this4;\n    (0, _classCallCheck2.default)(this, MissingAPIRoute);\n    for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n      args[_key3] = arguments[_key3];\n    }\n    _this4 = _callSuper(this, MissingAPIRoute, [].concat(args));\n    (0, _defineProperty2.default)(_this4, \"name\", \"MissingAPIRouteError\");\n    (0, _defineProperty2.default)(_this4, \"code\", \"MISSING_NEXTAUTH_API_ROUTE_ERROR\");\n    return _this4;\n  }\n  (0, _inherits2.default)(MissingAPIRoute, _UnknownError3);\n  return (0, _createClass2.default)(MissingAPIRoute);\n}(UnknownError);\nvar MissingSecret = exports.MissingSecret = function (_UnknownError4) {\n  function MissingSecret() {\n    var _this5;\n    (0, _classCallCheck2.default)(this, MissingSecret);\n    for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    _this5 = _callSuper(this, MissingSecret, [].concat(args));\n    (0, _defineProperty2.default)(_this5, \"name\", \"MissingSecretError\");\n    (0, _defineProperty2.default)(_this5, \"code\", \"NO_SECRET\");\n    return _this5;\n  }\n  (0, _inherits2.default)(MissingSecret, _UnknownError4);\n  return (0, _createClass2.default)(MissingSecret);\n}(UnknownError);\nvar MissingAuthorize = exports.MissingAuthorize = function (_UnknownError5) {\n  function MissingAuthorize() {\n    var _this6;\n    (0, _classCallCheck2.default)(this, MissingAuthorize);\n    for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n      args[_key5] = arguments[_key5];\n    }\n    _this6 = _callSuper(this, MissingAuthorize, [].concat(args));\n    (0, _defineProperty2.default)(_this6, \"name\", \"MissingAuthorizeError\");\n    (0, _defineProperty2.default)(_this6, \"code\", \"CALLBACK_CREDENTIALS_HANDLER_ERROR\");\n    return _this6;\n  }\n  (0, _inherits2.default)(MissingAuthorize, _UnknownError5);\n  return (0, _createClass2.default)(MissingAuthorize);\n}(UnknownError);\nvar MissingAdapter = exports.MissingAdapter = function (_UnknownError6) {\n  function MissingAdapter() {\n    var _this7;\n    (0, _classCallCheck2.default)(this, MissingAdapter);\n    for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n      args[_key6] = arguments[_key6];\n    }\n    _this7 = _callSuper(this, MissingAdapter, [].concat(args));\n    (0, _defineProperty2.default)(_this7, \"name\", \"MissingAdapterError\");\n    (0, _defineProperty2.default)(_this7, \"code\", \"EMAIL_REQUIRES_ADAPTER_ERROR\");\n    return _this7;\n  }\n  (0, _inherits2.default)(MissingAdapter, _UnknownError6);\n  return (0, _createClass2.default)(MissingAdapter);\n}(UnknownError);\nvar MissingAdapterMethods = exports.MissingAdapterMethods = function (_UnknownError7) {\n  function MissingAdapterMethods() {\n    var _this8;\n    (0, _classCallCheck2.default)(this, MissingAdapterMethods);\n    for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n      args[_key7] = arguments[_key7];\n    }\n    _this8 = _callSuper(this, MissingAdapterMethods, [].concat(args));\n    (0, _defineProperty2.default)(_this8, \"name\", \"MissingAdapterMethodsError\");\n    (0, _defineProperty2.default)(_this8, \"code\", \"MISSING_ADAPTER_METHODS_ERROR\");\n    return _this8;\n  }\n  (0, _inherits2.default)(MissingAdapterMethods, _UnknownError7);\n  return (0, _createClass2.default)(MissingAdapterMethods);\n}(UnknownError);\nvar UnsupportedStrategy = exports.UnsupportedStrategy = function (_UnknownError8) {\n  function UnsupportedStrategy() {\n    var _this9;\n    (0, _classCallCheck2.default)(this, UnsupportedStrategy);\n    for (var _len8 = arguments.length, args = new Array(_len8), _key8 = 0; _key8 < _len8; _key8++) {\n      args[_key8] = arguments[_key8];\n    }\n    _this9 = _callSuper(this, UnsupportedStrategy, [].concat(args));\n    (0, _defineProperty2.default)(_this9, \"name\", \"UnsupportedStrategyError\");\n    (0, _defineProperty2.default)(_this9, \"code\", \"CALLBACK_CREDENTIALS_JWT_ERROR\");\n    return _this9;\n  }\n  (0, _inherits2.default)(UnsupportedStrategy, _UnknownError8);\n  return (0, _createClass2.default)(UnsupportedStrategy);\n}(UnknownError);\nvar InvalidCallbackUrl = exports.InvalidCallbackUrl = function (_UnknownError9) {\n  function InvalidCallbackUrl() {\n    var _this10;\n    (0, _classCallCheck2.default)(this, InvalidCallbackUrl);\n    for (var _len9 = arguments.length, args = new Array(_len9), _key9 = 0; _key9 < _len9; _key9++) {\n      args[_key9] = arguments[_key9];\n    }\n    _this10 = _callSuper(this, InvalidCallbackUrl, [].concat(args));\n    (0, _defineProperty2.default)(_this10, \"name\", \"InvalidCallbackUrl\");\n    (0, _defineProperty2.default)(_this10, \"code\", \"INVALID_CALLBACK_URL_ERROR\");\n    return _this10;\n  }\n  (0, _inherits2.default)(InvalidCallbackUrl, _UnknownError9);\n  return (0, _createClass2.default)(InvalidCallbackUrl);\n}(UnknownError);\nfunction upperSnake(s) {\n  return s.replace(/([A-Z])/g, \"_$1\").toUpperCase();\n}\nfunction capitalize(s) {\n  return \"\".concat(s[0].toUpperCase()).concat(s.slice(1));\n}\nfunction eventsErrorHandler(methods, logger) {\n  return Object.keys(methods).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee() {\n      var method,\n        _args = arguments;\n      return _regenerator.default.wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.prev = 0;\n            method = methods[name];\n            _context.next = 4;\n            return method.apply(void 0, _args);\n          case 4:\n            return _context.abrupt(\"return\", _context.sent);\n          case 7:\n            _context.prev = 7;\n            _context.t0 = _context[\"catch\"](0);\n            logger.error(\"\".concat(upperSnake(name), \"_EVENT_ERROR\"), _context.t0);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee, null, [[0, 7]]);\n    }));\n    return acc;\n  }, {});\n}\nfunction adapterErrorHandler(adapter, logger) {\n  if (!adapter) return;\n  return Object.keys(adapter).reduce(function (acc, name) {\n    acc[name] = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee2() {\n      var _len10,\n        args,\n        _key10,\n        method,\n        e,\n        _args2 = arguments;\n      return _regenerator.default.wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.prev = 0;\n            for (_len10 = _args2.length, args = new Array(_len10), _key10 = 0; _key10 < _len10; _key10++) {\n              args[_key10] = _args2[_key10];\n            }\n            logger.debug(\"adapter_\".concat(name), {\n              args: args\n            });\n            method = adapter[name];\n            _context2.next = 6;\n            return method.apply(void 0, args);\n          case 6:\n            return _context2.abrupt(\"return\", _context2.sent);\n          case 9:\n            _context2.prev = 9;\n            _context2.t0 = _context2[\"catch\"](0);\n            logger.error(\"adapter_error_\".concat(name), _context2.t0);\n            e = new UnknownError(_context2.t0);\n            e.name = \"\".concat(capitalize(name), \"Error\");\n            throw e;\n          case 15:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2, null, [[0, 9]]);\n    }));\n    return acc;\n  }, {});\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/errors.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/index.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/core/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.AuthHandler = AuthHandler;\nvar _logger = _interopRequireWildcard(__webpack_require__(/*! ../utils/logger */ \"(rsc)/./node_modules/next-auth/utils/logger.js\"));\nvar _detectOrigin = __webpack_require__(/*! ../utils/detect-origin */ \"(rsc)/./node_modules/next-auth/utils/detect-origin.js\");\nvar routes = _interopRequireWildcard(__webpack_require__(/*! ./routes */ \"(rsc)/./node_modules/next-auth/core/routes/index.js\"));\nvar _pages = _interopRequireDefault(__webpack_require__(/*! ./pages */ \"(rsc)/./node_modules/next-auth/core/pages/index.js\"));\nvar _init = __webpack_require__(/*! ./init */ \"(rsc)/./node_modules/next-auth/core/init.js\");\nvar _assert = __webpack_require__(/*! ./lib/assert */ \"(rsc)/./node_modules/next-auth/core/lib/assert.js\");\nvar _cookie = __webpack_require__(/*! ./lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nvar _cookie2 = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/index.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getBody(req) {\n  try {\n    return await req.json();\n  } catch (_unused) {}\n}\nasync function toInternalRequest(req) {\n  var _headers$xForwarded2;\n  if (req instanceof Request) {\n    var _req$headers$get, _url$searchParams$get, _headers$xForwarded;\n    const url = new URL(req.url);\n    const nextauth = url.pathname.split(\"/\").slice(3);\n    const headers = Object.fromEntries(req.headers);\n    const query = Object.fromEntries(url.searchParams);\n    query.nextauth = nextauth;\n    return {\n      action: nextauth[0],\n      method: req.method,\n      headers,\n      body: await getBody(req),\n      cookies: (0, _cookie2.parse)((_req$headers$get = req.headers.get(\"cookie\")) !== null && _req$headers$get !== void 0 ? _req$headers$get : \"\"),\n      providerId: nextauth[1],\n      error: (_url$searchParams$get = url.searchParams.get(\"error\")) !== null && _url$searchParams$get !== void 0 ? _url$searchParams$get : nextauth[1],\n      origin: (0, _detectOrigin.detectOrigin)((_headers$xForwarded = headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded !== void 0 ? _headers$xForwarded : headers.host, headers[\"x-forwarded-proto\"]),\n      query\n    };\n  }\n  const {\n    headers\n  } = req;\n  const host = (_headers$xForwarded2 = headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-host\"]) !== null && _headers$xForwarded2 !== void 0 ? _headers$xForwarded2 : headers === null || headers === void 0 ? void 0 : headers.host;\n  req.origin = (0, _detectOrigin.detectOrigin)(host, headers === null || headers === void 0 ? void 0 : headers[\"x-forwarded-proto\"]);\n  return req;\n}\nasync function AuthHandler(params) {\n  var _req$body$callbackUrl, _req$body, _req$query2, _req$body2;\n  const {\n    options: authOptions,\n    req: incomingRequest\n  } = params;\n  const req = await toInternalRequest(incomingRequest);\n  (0, _logger.setLogger)(authOptions.logger, authOptions.debug);\n  const assertionResult = (0, _assert.assertConfig)({\n    options: authOptions,\n    req\n  });\n  if (Array.isArray(assertionResult)) {\n    assertionResult.forEach(_logger.default.warn);\n  } else if (assertionResult instanceof Error) {\n    var _req$query;\n    _logger.default.error(assertionResult.code, assertionResult);\n    const htmlPages = [\"signin\", \"signout\", \"error\", \"verify-request\"];\n    if (!htmlPages.includes(req.action) || req.method !== \"GET\") {\n      const message = `There is a problem with the server configuration. Check the server logs for more information.`;\n      return {\n        status: 500,\n        headers: [{\n          key: \"Content-Type\",\n          value: \"application/json\"\n        }],\n        body: {\n          message\n        }\n      };\n    }\n    const {\n      pages,\n      theme\n    } = authOptions;\n    const authOnErrorPage = (pages === null || pages === void 0 ? void 0 : pages.error) && ((_req$query = req.query) === null || _req$query === void 0 || (_req$query = _req$query.callbackUrl) === null || _req$query === void 0 ? void 0 : _req$query.startsWith(pages.error));\n    if (!(pages !== null && pages !== void 0 && pages.error) || authOnErrorPage) {\n      if (authOnErrorPage) {\n        _logger.default.error(\"AUTH_ON_ERROR_PAGE_ERROR\", new Error(`The error page ${pages === null || pages === void 0 ? void 0 : pages.error} should not require authentication`));\n      }\n      const render = (0, _pages.default)({\n        theme\n      });\n      return render.error({\n        error: \"configuration\"\n      });\n    }\n    return {\n      redirect: `${pages.error}?error=Configuration`\n    };\n  }\n  const {\n    action,\n    providerId,\n    error,\n    method = \"GET\"\n  } = req;\n  const {\n    options,\n    cookies\n  } = await (0, _init.init)({\n    authOptions,\n    action,\n    providerId,\n    origin: req.origin,\n    callbackUrl: (_req$body$callbackUrl = (_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.callbackUrl) !== null && _req$body$callbackUrl !== void 0 ? _req$body$callbackUrl : (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl,\n    csrfToken: (_req$body2 = req.body) === null || _req$body2 === void 0 ? void 0 : _req$body2.csrfToken,\n    cookies: req.cookies,\n    isPost: method === \"POST\"\n  });\n  const sessionStore = new _cookie.SessionStore(options.cookies.sessionToken, req, options.logger);\n  if (method === \"GET\") {\n    const render = (0, _pages.default)({\n      ...options,\n      query: req.query,\n      cookies\n    });\n    const {\n      pages\n    } = options;\n    switch (action) {\n      case \"providers\":\n        return await routes.providers(options.providers);\n      case \"session\":\n        {\n          const session = await routes.session({\n            options,\n            sessionStore\n          });\n          if (session.cookies) cookies.push(...session.cookies);\n          return {\n            ...session,\n            cookies\n          };\n        }\n      case \"csrf\":\n        return {\n          headers: [{\n            key: \"Content-Type\",\n            value: \"application/json\"\n          }],\n          body: {\n            csrfToken: options.csrfToken\n          },\n          cookies\n        };\n      case \"signin\":\n        if (pages.signIn) {\n          let signinUrl = `${pages.signIn}${pages.signIn.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(options.callbackUrl)}`;\n          if (error) signinUrl = `${signinUrl}&error=${encodeURIComponent(error)}`;\n          return {\n            redirect: signinUrl,\n            cookies\n          };\n        }\n        return render.signin();\n      case \"signout\":\n        if (pages.signOut) return {\n          redirect: pages.signOut,\n          cookies\n        };\n        return render.signout();\n      case \"callback\":\n        if (options.provider) {\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"verify-request\":\n        if (pages.verifyRequest) {\n          return {\n            redirect: pages.verifyRequest,\n            cookies\n          };\n        }\n        return render.verifyRequest();\n      case \"error\":\n        if ([\"Signin\", \"OAuthSignin\", \"OAuthCallback\", \"OAuthCreateAccount\", \"EmailCreateAccount\", \"Callback\", \"OAuthAccountNotLinked\", \"EmailSignin\", \"CredentialsSignin\", \"SessionRequired\"].includes(error)) {\n          return {\n            redirect: `${options.url}/signin?error=${error}`,\n            cookies\n          };\n        }\n        if (pages.error) {\n          return {\n            redirect: `${pages.error}${pages.error.includes(\"?\") ? \"&\" : \"?\"}error=${error}`,\n            cookies\n          };\n        }\n        return render.error({\n          error: error\n        });\n      default:\n    }\n  } else if (method === \"POST\") {\n    switch (action) {\n      case \"signin\":\n        if (options.csrfTokenVerified && options.provider) {\n          const signin = await routes.signin({\n            query: req.query,\n            body: req.body,\n            options\n          });\n          if (signin.cookies) cookies.push(...signin.cookies);\n          return {\n            ...signin,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signin?csrf=true`,\n          cookies\n        };\n      case \"signout\":\n        if (options.csrfTokenVerified) {\n          const signout = await routes.signout({\n            options,\n            sessionStore\n          });\n          if (signout.cookies) cookies.push(...signout.cookies);\n          return {\n            ...signout,\n            cookies\n          };\n        }\n        return {\n          redirect: `${options.url}/signout?csrf=true`,\n          cookies\n        };\n      case \"callback\":\n        if (options.provider) {\n          if (options.provider.type === \"credentials\" && !options.csrfTokenVerified) {\n            return {\n              redirect: `${options.url}/signin?csrf=true`,\n              cookies\n            };\n          }\n          const callback = await routes.callback({\n            body: req.body,\n            query: req.query,\n            headers: req.headers,\n            cookies: req.cookies,\n            method,\n            options,\n            sessionStore\n          });\n          if (callback.cookies) cookies.push(...callback.cookies);\n          return {\n            ...callback,\n            cookies\n          };\n        }\n        break;\n      case \"_log\":\n        {\n          if (authOptions.logger) {\n            try {\n              var _req$body3;\n              const {\n                code,\n                level,\n                ...metadata\n              } = (_req$body3 = req.body) !== null && _req$body3 !== void 0 ? _req$body3 : {};\n              _logger.default[level](code, metadata);\n            } catch (error) {\n              _logger.default.error(\"LOGGER_ERROR\", error);\n            }\n          }\n          return {};\n        }\n      case \"session\":\n        {\n          if (options.csrfTokenVerified) {\n            var _req$body4;\n            const session = await routes.session({\n              options,\n              sessionStore,\n              newSession: (_req$body4 = req.body) === null || _req$body4 === void 0 ? void 0 : _req$body4.data,\n              isUpdate: true\n            });\n            if (session.cookies) cookies.push(...session.cookies);\n            return {\n              ...session,\n              cookies\n            };\n          }\n          return {\n            status: 400,\n            body: {},\n            cookies\n          };\n        }\n      default:\n    }\n  }\n  return {\n    status: 400,\n    body: `Error: This action with HTTP ${method} is not supported by NextAuth.js`\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/init.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/core/init.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.init = init;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar _logger = _interopRequireDefault(__webpack_require__(/*! ../utils/logger */ \"(rsc)/./node_modules/next-auth/utils/logger.js\"));\nvar _errors = __webpack_require__(/*! ./errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _providers = _interopRequireDefault(__webpack_require__(/*! ./lib/providers */ \"(rsc)/./node_modules/next-auth/core/lib/providers.js\"));\nvar _utils = __webpack_require__(/*! ./lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nvar cookie = _interopRequireWildcard(__webpack_require__(/*! ./lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\"));\nvar jwt = _interopRequireWildcard(__webpack_require__(/*! ../jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\"));\nvar _defaultCallbacks = __webpack_require__(/*! ./lib/default-callbacks */ \"(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js\");\nvar _csrfToken = __webpack_require__(/*! ./lib/csrf-token */ \"(rsc)/./node_modules/next-auth/core/lib/csrf-token.js\");\nvar _callbackUrl = __webpack_require__(/*! ./lib/callback-url */ \"(rsc)/./node_modules/next-auth/core/lib/callback-url.js\");\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../utils/parse-url */ \"(rsc)/./node_modules/next-auth/utils/parse-url.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function init({\n  authOptions,\n  providerId,\n  action,\n  origin,\n  cookies: reqCookies,\n  callbackUrl: reqCallbackUrl,\n  csrfToken: reqCsrfToken,\n  isPost\n}) {\n  var _authOptions$useSecur, _authOptions$events;\n  const url = (0, _parseUrl.default)(origin);\n  const secret = (0, _utils.createSecret)({\n    authOptions,\n    url\n  });\n  const {\n    providers,\n    provider\n  } = (0, _providers.default)({\n    providers: authOptions.providers,\n    url,\n    providerId\n  });\n  const maxAge = 30 * 24 * 60 * 60;\n  const options = {\n    debug: false,\n    pages: {},\n    theme: {\n      colorScheme: \"auto\",\n      logo: \"\",\n      brandColor: \"\",\n      buttonText: \"\"\n    },\n    ...authOptions,\n    url,\n    action,\n    provider,\n    cookies: {\n      ...cookie.defaultCookies((_authOptions$useSecur = authOptions.useSecureCookies) !== null && _authOptions$useSecur !== void 0 ? _authOptions$useSecur : url.base.startsWith(\"https://\")),\n      ...authOptions.cookies\n    },\n    secret,\n    providers,\n    session: {\n      strategy: authOptions.adapter ? \"database\" : \"jwt\",\n      maxAge,\n      updateAge: 24 * 60 * 60,\n      generateSessionToken: () => {\n        var _randomUUID;\n        return (_randomUUID = _crypto.randomUUID === null || _crypto.randomUUID === void 0 ? void 0 : (0, _crypto.randomUUID)()) !== null && _randomUUID !== void 0 ? _randomUUID : (0, _crypto.randomBytes)(32).toString(\"hex\");\n      },\n      ...authOptions.session\n    },\n    jwt: {\n      secret,\n      maxAge,\n      encode: jwt.encode,\n      decode: jwt.decode,\n      ...authOptions.jwt\n    },\n    events: (0, _errors.eventsErrorHandler)((_authOptions$events = authOptions.events) !== null && _authOptions$events !== void 0 ? _authOptions$events : {}, _logger.default),\n    adapter: (0, _errors.adapterErrorHandler)(authOptions.adapter, _logger.default),\n    callbacks: {\n      ..._defaultCallbacks.defaultCallbacks,\n      ...authOptions.callbacks\n    },\n    logger: _logger.default,\n    callbackUrl: url.origin\n  };\n  const cookies = [];\n  const {\n    csrfToken,\n    cookie: csrfCookie,\n    csrfTokenVerified\n  } = (0, _csrfToken.createCSRFToken)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.csrfToken.name],\n    isPost,\n    bodyValue: reqCsrfToken\n  });\n  options.csrfToken = csrfToken;\n  options.csrfTokenVerified = csrfTokenVerified;\n  if (csrfCookie) {\n    cookies.push({\n      name: options.cookies.csrfToken.name,\n      value: csrfCookie,\n      options: options.cookies.csrfToken.options\n    });\n  }\n  const {\n    callbackUrl,\n    callbackUrlCookie\n  } = await (0, _callbackUrl.createCallbackUrl)({\n    options,\n    cookieValue: reqCookies === null || reqCookies === void 0 ? void 0 : reqCookies[options.cookies.callbackUrl.name],\n    paramValue: reqCallbackUrl\n  });\n  options.callbackUrl = callbackUrl;\n  if (callbackUrlCookie) {\n    cookies.push({\n      name: options.cookies.callbackUrl.name,\n      value: callbackUrlCookie,\n      options: options.cookies.callbackUrl.options\n    });\n  }\n  return {\n    options,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/init.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/assert.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/core/lib/assert.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.assertConfig = assertConfig;\nvar _errors = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _parseUrl = _interopRequireDefault(__webpack_require__(/*! ../../utils/parse-url */ \"(rsc)/./node_modules/next-auth/utils/parse-url.js\"));\nvar _cookie = __webpack_require__(/*! ./cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nlet warned = false;\nfunction isValidHttpUrl(url, baseUrl) {\n  try {\n    return /^https?:/.test(new URL(url, url.startsWith(\"/\") ? baseUrl : undefined).protocol);\n  } catch (_unused) {\n    return false;\n  }\n}\nfunction assertConfig(params) {\n  var _req$query, _req$query2, _options$useSecureCoo, _req$cookies, _options$cookies$call, _options$cookies;\n  const {\n    options,\n    req\n  } = params;\n  const warnings = [];\n  if (!warned) {\n    if (!req.origin) warnings.push(\"NEXTAUTH_URL\");\n    if (!options.secret && \"development\" !== \"production\") warnings.push(\"NO_SECRET\");\n    if (options.debug) warnings.push(\"DEBUG_ENABLED\");\n  }\n  if (!options.secret && \"development\" === \"production\") {}\n  if (!((_req$query = req.query) !== null && _req$query !== void 0 && _req$query.nextauth) && !req.action) {\n    return new _errors.MissingAPIRoute(\"Cannot find [...nextauth].{js,ts} in `/pages/api/auth`. Make sure the filename is written correctly.\");\n  }\n  const callbackUrlParam = (_req$query2 = req.query) === null || _req$query2 === void 0 ? void 0 : _req$query2.callbackUrl;\n  const url = (0, _parseUrl.default)(req.origin);\n  if (callbackUrlParam && !isValidHttpUrl(callbackUrlParam, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlParam}`);\n  }\n  const {\n    callbackUrl: defaultCallbackUrl\n  } = (0, _cookie.defaultCookies)((_options$useSecureCoo = options.useSecureCookies) !== null && _options$useSecureCoo !== void 0 ? _options$useSecureCoo : url.base.startsWith(\"https://\"));\n  const callbackUrlCookie = (_req$cookies = req.cookies) === null || _req$cookies === void 0 ? void 0 : _req$cookies[(_options$cookies$call = (_options$cookies = options.cookies) === null || _options$cookies === void 0 || (_options$cookies = _options$cookies.callbackUrl) === null || _options$cookies === void 0 ? void 0 : _options$cookies.name) !== null && _options$cookies$call !== void 0 ? _options$cookies$call : defaultCallbackUrl.name];\n  if (callbackUrlCookie && !isValidHttpUrl(callbackUrlCookie, url.base)) {\n    return new _errors.InvalidCallbackUrl(`Invalid callback URL. Received: ${callbackUrlCookie}`);\n  }\n  let hasCredentials, hasEmail;\n  let hasTwitterOAuth2;\n  for (const provider of options.providers) {\n    if (provider.type === \"credentials\") hasCredentials = true;else if (provider.type === \"email\") hasEmail = true;else if (provider.id === \"twitter\" && provider.version === \"2.0\") hasTwitterOAuth2 = true;\n  }\n  if (hasCredentials) {\n    var _options$session;\n    const dbStrategy = ((_options$session = options.session) === null || _options$session === void 0 ? void 0 : _options$session.strategy) === \"database\";\n    const onlyCredentials = !options.providers.some(p => p.type !== \"credentials\");\n    if (dbStrategy && onlyCredentials) {\n      return new _errors.UnsupportedStrategy(\"Signin in with credentials only supported if JWT strategy is enabled\");\n    }\n    const credentialsNoAuthorize = options.providers.some(p => p.type === \"credentials\" && !p.authorize);\n    if (credentialsNoAuthorize) {\n      return new _errors.MissingAuthorize(\"Must define an authorize() handler to use credentials authentication provider\");\n    }\n  }\n  if (hasEmail) {\n    const {\n      adapter\n    } = options;\n    if (!adapter) {\n      return new _errors.MissingAdapter(\"E-mail login requires an adapter.\");\n    }\n    const missingMethods = [\"createVerificationToken\", \"useVerificationToken\", \"getUserByEmail\"].filter(method => !adapter[method]);\n    if (missingMethods.length) {\n      return new _errors.MissingAdapterMethods(`Required adapter methods were missing: ${missingMethods.join(\", \")}`);\n    }\n  }\n  if (!warned) {\n    if (hasTwitterOAuth2) warnings.push(\"TWITTER_OAUTH_2_BETA\");\n    warned = true;\n  }\n  return warnings;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/callback-handler.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/callback-handler.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = callbackHandler;\nvar _errors = __webpack_require__(/*! ../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function callbackHandler(params) {\n  const {\n    sessionToken,\n    profile: _profile,\n    account,\n    options\n  } = params;\n  if (!(account !== null && account !== void 0 && account.providerAccountId) || !account.type) throw new Error(\"Missing or invalid provider account\");\n  if (![\"email\", \"oauth\"].includes(account.type)) throw new Error(\"Provider not supported\");\n  const {\n    adapter,\n    jwt,\n    events,\n    session: {\n      strategy: sessionStrategy,\n      generateSessionToken\n    }\n  } = options;\n  if (!adapter) {\n    return {\n      user: _profile,\n      account\n    };\n  }\n  const profile = _profile;\n  const {\n    createUser,\n    updateUser,\n    getUser,\n    getUserByAccount,\n    getUserByEmail,\n    linkAccount,\n    createSession,\n    getSessionAndUser,\n    deleteSession\n  } = adapter;\n  let session = null;\n  let user = null;\n  let isNewUser = false;\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (sessionToken) {\n    if (useJwtSession) {\n      try {\n        session = await jwt.decode({\n          ...jwt,\n          token: sessionToken\n        });\n        if (session && \"sub\" in session && session.sub) {\n          user = await getUser(session.sub);\n        }\n      } catch (_unused) {}\n    } else {\n      const userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession) {\n        session = userAndSession.session;\n        user = userAndSession.user;\n      }\n    }\n  }\n  if (account.type === \"email\") {\n    const userByEmail = await getUserByEmail(profile.email);\n    if (userByEmail) {\n      var _user, _events$updateUser;\n      if (((_user = user) === null || _user === void 0 ? void 0 : _user.id) !== userByEmail.id && !useJwtSession && sessionToken) {\n        await deleteSession(sessionToken);\n      }\n      user = await updateUser({\n        id: userByEmail.id,\n        emailVerified: new Date()\n      });\n      await ((_events$updateUser = events.updateUser) === null || _events$updateUser === void 0 ? void 0 : _events$updateUser.call(events, {\n        user\n      }));\n    } else {\n      var _events$createUser;\n      const {\n        id: _,\n        ...newUser\n      } = {\n        ...profile,\n        emailVerified: new Date()\n      };\n      user = await createUser(newUser);\n      await ((_events$createUser = events.createUser) === null || _events$createUser === void 0 ? void 0 : _events$createUser.call(events, {\n        user\n      }));\n      isNewUser = true;\n    }\n    session = useJwtSession ? {} : await createSession({\n      sessionToken: await generateSessionToken(),\n      userId: user.id,\n      expires: (0, _utils.fromDate)(options.session.maxAge)\n    });\n    return {\n      session,\n      user,\n      isNewUser\n    };\n  } else if (account.type === \"oauth\") {\n    const userByAccount = await getUserByAccount({\n      providerAccountId: account.providerAccountId,\n      provider: account.provider\n    });\n    if (userByAccount) {\n      if (user) {\n        if (userByAccount.id === user.id) {\n          return {\n            session,\n            user,\n            isNewUser\n          };\n        }\n        throw new _errors.AccountNotLinkedError(\"The account is already associated with another user\");\n      }\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: userByAccount.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user: userByAccount,\n        isNewUser\n      };\n    } else {\n      var _events$createUser2, _events$linkAccount2;\n      if (user) {\n        var _events$linkAccount;\n        await linkAccount({\n          ...account,\n          userId: user.id\n        });\n        await ((_events$linkAccount = events.linkAccount) === null || _events$linkAccount === void 0 ? void 0 : _events$linkAccount.call(events, {\n          user,\n          account,\n          profile\n        }));\n        return {\n          session,\n          user,\n          isNewUser\n        };\n      }\n      const userByEmail = profile.email ? await getUserByEmail(profile.email) : null;\n      if (userByEmail) {\n        const provider = options.provider;\n        if (provider !== null && provider !== void 0 && provider.allowDangerousEmailAccountLinking) {\n          user = userByEmail;\n        } else {\n          throw new _errors.AccountNotLinkedError(\"Another account already exists with the same e-mail address\");\n        }\n      } else {\n        const {\n          id: _,\n          ...newUser\n        } = {\n          ...profile,\n          emailVerified: null\n        };\n        user = await createUser(newUser);\n      }\n      await ((_events$createUser2 = events.createUser) === null || _events$createUser2 === void 0 ? void 0 : _events$createUser2.call(events, {\n        user\n      }));\n      await linkAccount({\n        ...account,\n        userId: user.id\n      });\n      await ((_events$linkAccount2 = events.linkAccount) === null || _events$linkAccount2 === void 0 ? void 0 : _events$linkAccount2.call(events, {\n        user,\n        account,\n        profile\n      }));\n      session = useJwtSession ? {} : await createSession({\n        sessionToken: await generateSessionToken(),\n        userId: user.id,\n        expires: (0, _utils.fromDate)(options.session.maxAge)\n      });\n      return {\n        session,\n        user,\n        isNewUser: true\n      };\n    }\n  }\n  throw new Error(\"Unsupported account type\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/callback-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/callback-url.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/callback-url.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createCallbackUrl = createCallbackUrl;\nasync function createCallbackUrl({\n  options,\n  paramValue,\n  cookieValue\n}) {\n  const {\n    url,\n    callbacks\n  } = options;\n  let callbackUrl = url.origin;\n  if (paramValue) {\n    callbackUrl = await callbacks.redirect({\n      url: paramValue,\n      baseUrl: url.origin\n    });\n  } else if (cookieValue) {\n    callbackUrl = await callbacks.redirect({\n      url: cookieValue,\n      baseUrl: url.origin\n    });\n  }\n  return {\n    callbackUrl,\n    callbackUrlCookie: callbackUrl !== cookieValue ? callbackUrl : undefined\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2NhbGxiYWNrLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxjb3JlXFxsaWJcXGNhbGxiYWNrLXVybC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuY3JlYXRlQ2FsbGJhY2tVcmwgPSBjcmVhdGVDYWxsYmFja1VybDtcbmFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUNhbGxiYWNrVXJsKHtcbiAgb3B0aW9ucyxcbiAgcGFyYW1WYWx1ZSxcbiAgY29va2llVmFsdWVcbn0pIHtcbiAgY29uc3Qge1xuICAgIHVybCxcbiAgICBjYWxsYmFja3NcbiAgfSA9IG9wdGlvbnM7XG4gIGxldCBjYWxsYmFja1VybCA9IHVybC5vcmlnaW47XG4gIGlmIChwYXJhbVZhbHVlKSB7XG4gICAgY2FsbGJhY2tVcmwgPSBhd2FpdCBjYWxsYmFja3MucmVkaXJlY3Qoe1xuICAgICAgdXJsOiBwYXJhbVZhbHVlLFxuICAgICAgYmFzZVVybDogdXJsLm9yaWdpblxuICAgIH0pO1xuICB9IGVsc2UgaWYgKGNvb2tpZVZhbHVlKSB7XG4gICAgY2FsbGJhY2tVcmwgPSBhd2FpdCBjYWxsYmFja3MucmVkaXJlY3Qoe1xuICAgICAgdXJsOiBjb29raWVWYWx1ZSxcbiAgICAgIGJhc2VVcmw6IHVybC5vcmlnaW5cbiAgICB9KTtcbiAgfVxuICByZXR1cm4ge1xuICAgIGNhbGxiYWNrVXJsLFxuICAgIGNhbGxiYWNrVXJsQ29va2llOiBjYWxsYmFja1VybCAhPT0gY29va2llVmFsdWUgPyBjYWxsYmFja1VybCA6IHVuZGVmaW5lZFxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/callback-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/cookie.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/core/lib/cookie.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.SessionStore = void 0;\nexports.defaultCookies = defaultCookies;\nfunction _classPrivateMethodInitSpec(e, a) { _checkPrivateRedeclaration(e, a), a.add(e); }\nfunction _classPrivateFieldInitSpec(e, t, a) { _checkPrivateRedeclaration(e, t), t.set(e, a); }\nfunction _checkPrivateRedeclaration(e, t) { if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\"); }\nfunction _classPrivateFieldGet(s, a) { return s.get(_assertClassBrand(s, a)); }\nfunction _classPrivateFieldSet(s, a, r) { return s.set(_assertClassBrand(s, a), r), r; }\nfunction _assertClassBrand(e, t, n) { if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n; throw new TypeError(\"Private element is not present on this object\"); }\nconst ALLOWED_COOKIE_SIZE = 4096;\nconst ESTIMATED_EMPTY_COOKIE_SIZE = 163;\nconst CHUNK_SIZE = ALLOWED_COOKIE_SIZE - ESTIMATED_EMPTY_COOKIE_SIZE;\nfunction defaultCookies(useSecureCookies) {\n  const cookiePrefix = useSecureCookies ? \"__Secure-\" : \"\";\n  return {\n    sessionToken: {\n      name: `${cookiePrefix}next-auth.session-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    callbackUrl: {\n      name: `${cookiePrefix}next-auth.callback-url`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    csrfToken: {\n      name: `${useSecureCookies ? \"__Host-\" : \"\"}next-auth.csrf-token`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    },\n    pkceCodeVerifier: {\n      name: `${cookiePrefix}next-auth.pkce.code_verifier`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    state: {\n      name: `${cookiePrefix}next-auth.state`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies,\n        maxAge: 60 * 15\n      }\n    },\n    nonce: {\n      name: `${cookiePrefix}next-auth.nonce`,\n      options: {\n        httpOnly: true,\n        sameSite: \"lax\",\n        path: \"/\",\n        secure: useSecureCookies\n      }\n    }\n  };\n}\nvar _chunks = new WeakMap();\nvar _option = new WeakMap();\nvar _logger = new WeakMap();\nvar _SessionStore_brand = new WeakSet();\nclass SessionStore {\n  constructor(option, req, logger) {\n    _classPrivateMethodInitSpec(this, _SessionStore_brand);\n    _classPrivateFieldInitSpec(this, _chunks, {});\n    _classPrivateFieldInitSpec(this, _option, void 0);\n    _classPrivateFieldInitSpec(this, _logger, void 0);\n    _classPrivateFieldSet(_logger, this, logger);\n    _classPrivateFieldSet(_option, this, option);\n    const {\n      cookies: _cookies\n    } = req;\n    const {\n      name: cookieName\n    } = option;\n    if (typeof (_cookies === null || _cookies === void 0 ? void 0 : _cookies.getAll) === \"function\") {\n      for (const {\n        name,\n        value\n      } of _cookies.getAll()) {\n        if (name.startsWith(cookieName)) {\n          _classPrivateFieldGet(_chunks, this)[name] = value;\n        }\n      }\n    } else if (_cookies instanceof Map) {\n      for (const name of _cookies.keys()) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies.get(name);\n      }\n    } else {\n      for (const name in _cookies) {\n        if (name.startsWith(cookieName)) _classPrivateFieldGet(_chunks, this)[name] = _cookies[name];\n      }\n    }\n  }\n  get value() {\n    const sortedKeys = Object.keys(_classPrivateFieldGet(_chunks, this)).sort((a, b) => {\n      var _a$split$pop, _b$split$pop;\n      const aSuffix = parseInt((_a$split$pop = a.split(\".\").pop()) !== null && _a$split$pop !== void 0 ? _a$split$pop : \"0\");\n      const bSuffix = parseInt((_b$split$pop = b.split(\".\").pop()) !== null && _b$split$pop !== void 0 ? _b$split$pop : \"0\");\n      return aSuffix - bSuffix;\n    });\n    return sortedKeys.map(key => _classPrivateFieldGet(_chunks, this)[key]).join(\"\");\n  }\n  chunk(value, options) {\n    const cookies = _assertClassBrand(_SessionStore_brand, this, _clean).call(this);\n    const chunked = _assertClassBrand(_SessionStore_brand, this, _chunk).call(this, {\n      name: _classPrivateFieldGet(_option, this).name,\n      value,\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        ...options\n      }\n    });\n    for (const chunk of chunked) {\n      cookies[chunk.name] = chunk;\n    }\n    return Object.values(cookies);\n  }\n  clean() {\n    return Object.values(_assertClassBrand(_SessionStore_brand, this, _clean).call(this));\n  }\n}\nexports.SessionStore = SessionStore;\nfunction _chunk(cookie) {\n  const chunkCount = Math.ceil(cookie.value.length / CHUNK_SIZE);\n  if (chunkCount === 1) {\n    _classPrivateFieldGet(_chunks, this)[cookie.name] = cookie.value;\n    return [cookie];\n  }\n  const cookies = [];\n  for (let i = 0; i < chunkCount; i++) {\n    const name = `${cookie.name}.${i}`;\n    const value = cookie.value.substr(i * CHUNK_SIZE, CHUNK_SIZE);\n    cookies.push({\n      ...cookie,\n      name,\n      value\n    });\n    _classPrivateFieldGet(_chunks, this)[name] = value;\n  }\n  _classPrivateFieldGet(_logger, this).debug(\"CHUNKING_SESSION_COOKIE\", {\n    message: `Session cookie exceeds allowed ${ALLOWED_COOKIE_SIZE} bytes.`,\n    emptyCookieSize: ESTIMATED_EMPTY_COOKIE_SIZE,\n    valueSize: cookie.value.length,\n    chunks: cookies.map(c => c.value.length + ESTIMATED_EMPTY_COOKIE_SIZE)\n  });\n  return cookies;\n}\nfunction _clean() {\n  const cleanedChunks = {};\n  for (const name in _classPrivateFieldGet(_chunks, this)) {\n    var _classPrivateFieldGet2;\n    (_classPrivateFieldGet2 = _classPrivateFieldGet(_chunks, this)) === null || _classPrivateFieldGet2 === void 0 || delete _classPrivateFieldGet2[name];\n    cleanedChunks[name] = {\n      name,\n      value: \"\",\n      options: {\n        ..._classPrivateFieldGet(_option, this).options,\n        maxAge: 0\n      }\n    };\n  }\n  return cleanedChunks;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/cookie.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/csrf-token.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/lib/csrf-token.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createCSRFToken = createCSRFToken;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nfunction createCSRFToken({\n  options,\n  cookieValue,\n  isPost,\n  bodyValue\n}) {\n  if (cookieValue) {\n    const [csrfToken, csrfTokenHash] = cookieValue.split(\"|\");\n    const expectedCsrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n    if (csrfTokenHash === expectedCsrfTokenHash) {\n      const csrfTokenVerified = isPost && csrfToken === bodyValue;\n      return {\n        csrfTokenVerified,\n        csrfToken\n      };\n    }\n  }\n  const csrfToken = (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const csrfTokenHash = (0, _crypto.createHash)(\"sha256\").update(`${csrfToken}${options.secret}`).digest(\"hex\");\n  const cookie = `${csrfToken}|${csrfTokenHash}`;\n  return {\n    cookie,\n    csrfToken\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/csrf-token.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js":
/*!**************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/default-callbacks.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.defaultCallbacks = void 0;\nconst defaultCallbacks = exports.defaultCallbacks = {\n  signIn() {\n    return true;\n  },\n  redirect({\n    url,\n    baseUrl\n  }) {\n    if (url.startsWith(\"/\")) return `${baseUrl}${url}`;else if (new URL(url).origin === baseUrl) return url;\n    return baseUrl;\n  },\n  session({\n    session\n  }) {\n    return session;\n  },\n  jwt({\n    token\n  }) {\n    return token;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2RlZmF1bHQtY2FsbGJhY2tzLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLHdCQUF3QjtBQUN4Qix5QkFBeUIsd0JBQXdCO0FBQ2pEO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHVDQUF1QyxRQUFRLEVBQUUsSUFBSSxFQUFFO0FBQ3ZEO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxjb3JlXFxsaWJcXGRlZmF1bHQtY2FsbGJhY2tzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0Q2FsbGJhY2tzID0gdm9pZCAwO1xuY29uc3QgZGVmYXVsdENhbGxiYWNrcyA9IGV4cG9ydHMuZGVmYXVsdENhbGxiYWNrcyA9IHtcbiAgc2lnbkluKCkge1xuICAgIHJldHVybiB0cnVlO1xuICB9LFxuICByZWRpcmVjdCh7XG4gICAgdXJsLFxuICAgIGJhc2VVcmxcbiAgfSkge1xuICAgIGlmICh1cmwuc3RhcnRzV2l0aChcIi9cIikpIHJldHVybiBgJHtiYXNlVXJsfSR7dXJsfWA7ZWxzZSBpZiAobmV3IFVSTCh1cmwpLm9yaWdpbiA9PT0gYmFzZVVybCkgcmV0dXJuIHVybDtcbiAgICByZXR1cm4gYmFzZVVybDtcbiAgfSxcbiAgc2Vzc2lvbih7XG4gICAgc2Vzc2lvblxuICB9KSB7XG4gICAgcmV0dXJuIHNlc3Npb247XG4gIH0sXG4gIGp3dCh7XG4gICAgdG9rZW5cbiAgfSkge1xuICAgIHJldHVybiB0b2tlbjtcbiAgfVxufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/default-callbacks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/email/getUserFromEmail.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = getAdapterUserFromEmail;\nasync function getAdapterUserFromEmail({\n  email,\n  adapter\n}) {\n  const {\n    getUserByEmail\n  } = adapter;\n  const adapterUser = email ? await getUserByEmail(email) : null;\n  if (adapterUser) return adapterUser;\n  return {\n    id: email,\n    email,\n    emailVerified: null\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2VtYWlsL2dldFVzZXJGcm9tRW1haWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxcY29yZVxcbGliXFxlbWFpbFxcZ2V0VXNlckZyb21FbWFpbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IGdldEFkYXB0ZXJVc2VyRnJvbUVtYWlsO1xuYXN5bmMgZnVuY3Rpb24gZ2V0QWRhcHRlclVzZXJGcm9tRW1haWwoe1xuICBlbWFpbCxcbiAgYWRhcHRlclxufSkge1xuICBjb25zdCB7XG4gICAgZ2V0VXNlckJ5RW1haWxcbiAgfSA9IGFkYXB0ZXI7XG4gIGNvbnN0IGFkYXB0ZXJVc2VyID0gZW1haWwgPyBhd2FpdCBnZXRVc2VyQnlFbWFpbChlbWFpbCkgOiBudWxsO1xuICBpZiAoYWRhcHRlclVzZXIpIHJldHVybiBhZGFwdGVyVXNlcjtcbiAgcmV0dXJuIHtcbiAgICBpZDogZW1haWwsXG4gICAgZW1haWwsXG4gICAgZW1haWxWZXJpZmllZDogbnVsbFxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/email/signin.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/email/signin.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = email;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nvar _utils = __webpack_require__(/*! ../utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function email(identifier, options) {\n  var _await$provider$gener, _provider$generateVer, _provider$maxAge, _adapter$createVerifi;\n  const {\n    url,\n    adapter,\n    provider,\n    callbackUrl,\n    theme\n  } = options;\n  const token = (_await$provider$gener = await ((_provider$generateVer = provider.generateVerificationToken) === null || _provider$generateVer === void 0 ? void 0 : _provider$generateVer.call(provider))) !== null && _await$provider$gener !== void 0 ? _await$provider$gener : (0, _crypto.randomBytes)(32).toString(\"hex\");\n  const ONE_DAY_IN_SECONDS = 86400;\n  const expires = new Date(Date.now() + ((_provider$maxAge = provider.maxAge) !== null && _provider$maxAge !== void 0 ? _provider$maxAge : ONE_DAY_IN_SECONDS) * 1000);\n  const params = new URLSearchParams({\n    callbackUrl,\n    token,\n    email: identifier\n  });\n  const _url = `${url}/callback/${provider.id}?${params}`;\n  await Promise.all([provider.sendVerificationRequest({\n    identifier,\n    token,\n    expires,\n    url: _url,\n    provider,\n    theme\n  }), (_adapter$createVerifi = adapter.createVerificationToken) === null || _adapter$createVerifi === void 0 ? void 0 : _adapter$createVerifi.call(adapter, {\n    identifier,\n    token: (0, _utils.hashToken)(token, options),\n    expires\n  })]);\n  return `${url}/verify-request?${new URLSearchParams({\n    provider: provider.id,\n    type: provider.type\n  })}`;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvbGliL2VtYWlsL3NpZ25pbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmLGNBQWMsbUJBQU8sQ0FBQyxzQkFBUTtBQUM5QixhQUFhLG1CQUFPLENBQUMsa0VBQVU7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxrQkFBa0IsSUFBSSxZQUFZLFlBQVksR0FBRyxPQUFPO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxZQUFZLElBQUksa0JBQWtCO0FBQ2xDO0FBQ0E7QUFDQSxHQUFHLEVBQUU7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXGNvcmVcXGxpYlxcZW1haWxcXHNpZ25pbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IGVtYWlsO1xudmFyIF9jcnlwdG8gPSByZXF1aXJlKFwiY3J5cHRvXCIpO1xudmFyIF91dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcbmFzeW5jIGZ1bmN0aW9uIGVtYWlsKGlkZW50aWZpZXIsIG9wdGlvbnMpIHtcbiAgdmFyIF9hd2FpdCRwcm92aWRlciRnZW5lciwgX3Byb3ZpZGVyJGdlbmVyYXRlVmVyLCBfcHJvdmlkZXIkbWF4QWdlLCBfYWRhcHRlciRjcmVhdGVWZXJpZmk7XG4gIGNvbnN0IHtcbiAgICB1cmwsXG4gICAgYWRhcHRlcixcbiAgICBwcm92aWRlcixcbiAgICBjYWxsYmFja1VybCxcbiAgICB0aGVtZVxuICB9ID0gb3B0aW9ucztcbiAgY29uc3QgdG9rZW4gPSAoX2F3YWl0JHByb3ZpZGVyJGdlbmVyID0gYXdhaXQgKChfcHJvdmlkZXIkZ2VuZXJhdGVWZXIgPSBwcm92aWRlci5nZW5lcmF0ZVZlcmlmaWNhdGlvblRva2VuKSA9PT0gbnVsbCB8fCBfcHJvdmlkZXIkZ2VuZXJhdGVWZXIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9wcm92aWRlciRnZW5lcmF0ZVZlci5jYWxsKHByb3ZpZGVyKSkpICE9PSBudWxsICYmIF9hd2FpdCRwcm92aWRlciRnZW5lciAhPT0gdm9pZCAwID8gX2F3YWl0JHByb3ZpZGVyJGdlbmVyIDogKDAsIF9jcnlwdG8ucmFuZG9tQnl0ZXMpKDMyKS50b1N0cmluZyhcImhleFwiKTtcbiAgY29uc3QgT05FX0RBWV9JTl9TRUNPTkRTID0gODY0MDA7XG4gIGNvbnN0IGV4cGlyZXMgPSBuZXcgRGF0ZShEYXRlLm5vdygpICsgKChfcHJvdmlkZXIkbWF4QWdlID0gcHJvdmlkZXIubWF4QWdlKSAhPT0gbnVsbCAmJiBfcHJvdmlkZXIkbWF4QWdlICE9PSB2b2lkIDAgPyBfcHJvdmlkZXIkbWF4QWdlIDogT05FX0RBWV9JTl9TRUNPTkRTKSAqIDEwMDApO1xuICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHtcbiAgICBjYWxsYmFja1VybCxcbiAgICB0b2tlbixcbiAgICBlbWFpbDogaWRlbnRpZmllclxuICB9KTtcbiAgY29uc3QgX3VybCA9IGAke3VybH0vY2FsbGJhY2svJHtwcm92aWRlci5pZH0/JHtwYXJhbXN9YDtcbiAgYXdhaXQgUHJvbWlzZS5hbGwoW3Byb3ZpZGVyLnNlbmRWZXJpZmljYXRpb25SZXF1ZXN0KHtcbiAgICBpZGVudGlmaWVyLFxuICAgIHRva2VuLFxuICAgIGV4cGlyZXMsXG4gICAgdXJsOiBfdXJsLFxuICAgIHByb3ZpZGVyLFxuICAgIHRoZW1lXG4gIH0pLCAoX2FkYXB0ZXIkY3JlYXRlVmVyaWZpID0gYWRhcHRlci5jcmVhdGVWZXJpZmljYXRpb25Ub2tlbikgPT09IG51bGwgfHwgX2FkYXB0ZXIkY3JlYXRlVmVyaWZpID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYWRhcHRlciRjcmVhdGVWZXJpZmkuY2FsbChhZGFwdGVyLCB7XG4gICAgaWRlbnRpZmllcixcbiAgICB0b2tlbjogKDAsIF91dGlscy5oYXNoVG9rZW4pKHRva2VuLCBvcHRpb25zKSxcbiAgICBleHBpcmVzXG4gIH0pXSk7XG4gIHJldHVybiBgJHt1cmx9L3ZlcmlmeS1yZXF1ZXN0PyR7bmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgcHJvdmlkZXI6IHByb3ZpZGVyLmlkLFxuICAgIHR5cGU6IHByb3ZpZGVyLnR5cGVcbiAgfSl9YDtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/email/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js":
/*!********************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/authorization-url.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = getAuthorizationUrl;\nvar _client = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\");\nvar _clientLegacy = __webpack_require__(/*! ./client-legacy */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\");\nvar checks = _interopRequireWildcard(__webpack_require__(/*! ./checks */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function getAuthorizationUrl({\n  options,\n  query\n}) {\n  var _provider$version;\n  const {\n    logger,\n    provider\n  } = options;\n  let params = {};\n  if (typeof provider.authorization === \"string\") {\n    const parsedUrl = new URL(provider.authorization);\n    const parsedParams = Object.fromEntries(parsedUrl.searchParams);\n    params = {\n      ...params,\n      ...parsedParams\n    };\n  } else {\n    var _provider$authorizati;\n    params = {\n      ...params,\n      ...((_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.params)\n    };\n  }\n  params = {\n    ...params,\n    ...query\n  };\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    var _provider$authorizati2;\n    const client = (0, _clientLegacy.oAuth1Client)(options);\n    const tokens = await client.getOAuthRequestToken(params);\n    const url = `${(_provider$authorizati2 = provider.authorization) === null || _provider$authorizati2 === void 0 ? void 0 : _provider$authorizati2.url}?${new URLSearchParams({\n      oauth_token: tokens.oauth_token,\n      oauth_token_secret: tokens.oauth_token_secret,\n      ...tokens.params\n    })}`;\n    _clientLegacy.oAuth1TokenStore.set(tokens.oauth_token, tokens.oauth_token_secret);\n    logger.debug(\"GET_AUTHORIZATION_URL\", {\n      url,\n      provider\n    });\n    return {\n      redirect: url\n    };\n  }\n  const client = await (0, _client.openidClient)(options);\n  const authorizationParams = params;\n  const cookies = [];\n  await checks.state.create(options, cookies, authorizationParams);\n  await checks.pkce.create(options, cookies, authorizationParams);\n  await checks.nonce.create(options, cookies, authorizationParams);\n  const url = client.authorizationUrl(authorizationParams);\n  logger.debug(\"GET_AUTHORIZATION_URL\", {\n    url,\n    cookies,\n    provider\n  });\n  return {\n    redirect: url,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js":
/*!***********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/callback.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = oAuthCallback;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nvar _client = __webpack_require__(/*! ./client */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\");\nvar _clientLegacy = __webpack_require__(/*! ./client-legacy */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\");\nvar _checks = _interopRequireWildcard(__webpack_require__(/*! ./checks */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\"));\nvar _errors = __webpack_require__(/*! ../../errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function oAuthCallback(params) {\n  var _body$error, _provider$version;\n  const {\n    options,\n    query,\n    body,\n    method,\n    cookies\n  } = params;\n  const {\n    logger,\n    provider\n  } = options;\n  const errorMessage = (_body$error = body === null || body === void 0 ? void 0 : body.error) !== null && _body$error !== void 0 ? _body$error : query === null || query === void 0 ? void 0 : query.error;\n  if (errorMessage) {\n    const error = new Error(errorMessage);\n    logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      error,\n      error_description: query === null || query === void 0 ? void 0 : query.error_description,\n      providerId: provider.id\n    });\n    logger.debug(\"OAUTH_CALLBACK_HANDLER_ERROR\", {\n      body\n    });\n    throw error;\n  }\n  if ((_provider$version = provider.version) !== null && _provider$version !== void 0 && _provider$version.startsWith(\"1.\")) {\n    try {\n      const client = await (0, _clientLegacy.oAuth1Client)(options);\n      const {\n        oauth_token,\n        oauth_verifier\n      } = query !== null && query !== void 0 ? query : {};\n      const tokens = await client.getOAuthAccessToken(oauth_token, _clientLegacy.oAuth1TokenStore.get(oauth_token), oauth_verifier);\n      let profile = await client.get(provider.profileUrl, tokens.oauth_token, tokens.oauth_token_secret);\n      if (typeof profile === \"string\") {\n        profile = JSON.parse(profile);\n      }\n      const newProfile = await getProfile({\n        profile,\n        tokens,\n        provider,\n        logger\n      });\n      return {\n        ...newProfile,\n        cookies: []\n      };\n    } catch (error) {\n      logger.error(\"OAUTH_V1_GET_ACCESS_TOKEN_ERROR\", error);\n      throw error;\n    }\n  }\n  if (query !== null && query !== void 0 && query.oauth_token) _clientLegacy.oAuth1TokenStore.delete(query.oauth_token);\n  try {\n    var _provider$token, _provider$token2, _provider$userinfo;\n    const client = await (0, _client.openidClient)(options);\n    let tokens;\n    const checks = {};\n    const resCookies = [];\n    await _checks.state.use(cookies, resCookies, options, checks);\n    await _checks.pkce.use(cookies, resCookies, options, checks);\n    await _checks.nonce.use(cookies, resCookies, options, checks);\n    const params = {\n      ...client.callbackParams({\n        url: `http://n?${new URLSearchParams(query)}`,\n        body,\n        method\n      }),\n      ...((_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.params)\n    };\n    if ((_provider$token2 = provider.token) !== null && _provider$token2 !== void 0 && _provider$token2.request) {\n      const response = await provider.token.request({\n        provider,\n        params,\n        checks,\n        client\n      });\n      tokens = new _openidClient.TokenSet(response.tokens);\n    } else if (provider.idToken) {\n      tokens = await client.callback(provider.callbackUrl, params, checks);\n    } else {\n      tokens = await client.oauthCallback(provider.callbackUrl, params, checks);\n    }\n    if (Array.isArray(tokens.scope)) {\n      tokens.scope = tokens.scope.join(\" \");\n    }\n    let profile;\n    if ((_provider$userinfo = provider.userinfo) !== null && _provider$userinfo !== void 0 && _provider$userinfo.request) {\n      profile = await provider.userinfo.request({\n        provider,\n        tokens,\n        client\n      });\n    } else if (provider.idToken) {\n      profile = tokens.claims();\n    } else {\n      var _provider$userinfo2;\n      profile = await client.userinfo(tokens, {\n        params: (_provider$userinfo2 = provider.userinfo) === null || _provider$userinfo2 === void 0 ? void 0 : _provider$userinfo2.params\n      });\n    }\n    const profileResult = await getProfile({\n      profile,\n      provider,\n      tokens,\n      logger\n    });\n    return {\n      ...profileResult,\n      cookies: resCookies\n    };\n  } catch (error) {\n    throw new _errors.OAuthCallbackError(error);\n  }\n}\nasync function getProfile({\n  profile: OAuthProfile,\n  tokens,\n  provider,\n  logger\n}) {\n  try {\n    var _profile$email;\n    logger.debug(\"PROFILE_DATA\", {\n      OAuthProfile\n    });\n    const profile = await provider.profile(OAuthProfile, tokens);\n    profile.email = (_profile$email = profile.email) === null || _profile$email === void 0 ? void 0 : _profile$email.toLowerCase();\n    if (!profile.id) throw new TypeError(`Profile id is missing in ${provider.name} OAuth profile response`);\n    return {\n      profile,\n      account: {\n        provider: provider.id,\n        type: provider.type,\n        providerAccountId: profile.id.toString(),\n        ...tokens\n      },\n      OAuthProfile\n    };\n  } catch (error) {\n    logger.error(\"OAUTH_PARSE_PROFILE_ERROR\", {\n      error: error,\n      OAuthProfile\n    });\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/checks.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.pkce = exports.nonce = exports.PKCE_CODE_CHALLENGE_METHOD = void 0;\nexports.signCookie = signCookie;\nexports.state = void 0;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nvar jwt = _interopRequireWildcard(__webpack_require__(/*! ../../../jwt */ \"(rsc)/./node_modules/next-auth/jwt/index.js\"));\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nasync function signCookie(type, value, maxAge, options) {\n  const {\n    cookies,\n    logger\n  } = options;\n  logger.debug(`CREATE_${type.toUpperCase()}`, {\n    value,\n    maxAge\n  });\n  const {\n    name\n  } = cookies[type];\n  const expires = new Date();\n  expires.setTime(expires.getTime() + maxAge * 1000);\n  return {\n    name,\n    value: await jwt.encode({\n      ...options.jwt,\n      maxAge,\n      token: {\n        value\n      },\n      salt: name\n    }),\n    options: {\n      ...cookies[type].options,\n      expires\n    }\n  };\n}\nconst PKCE_MAX_AGE = 60 * 15;\nconst PKCE_CODE_CHALLENGE_METHOD = exports.PKCE_CODE_CHALLENGE_METHOD = \"S256\";\nconst pkce = exports.pkce = {\n  async create(options, cookies, resParams) {\n    var _options$provider, _options$cookies$pkce;\n    if (!((_options$provider = options.provider) !== null && _options$provider !== void 0 && (_options$provider = _options$provider.checks) !== null && _options$provider !== void 0 && _options$provider.includes(\"pkce\"))) return;\n    const code_verifier = _openidClient.generators.codeVerifier();\n    const value = _openidClient.generators.codeChallenge(code_verifier);\n    resParams.code_challenge = value;\n    resParams.code_challenge_method = PKCE_CODE_CHALLENGE_METHOD;\n    const maxAge = (_options$cookies$pkce = options.cookies.pkceCodeVerifier.options.maxAge) !== null && _options$cookies$pkce !== void 0 ? _options$cookies$pkce : PKCE_MAX_AGE;\n    cookies.push(await signCookie(\"pkceCodeVerifier\", code_verifier, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider2;\n    if (!((_options$provider2 = options.provider) !== null && _options$provider2 !== void 0 && (_options$provider2 = _options$provider2.checks) !== null && _options$provider2 !== void 0 && _options$provider2.includes(\"pkce\"))) return;\n    const codeVerifier = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.pkceCodeVerifier.name];\n    if (!codeVerifier) throw new TypeError(\"PKCE code_verifier cookie was missing.\");\n    const {\n      name\n    } = options.cookies.pkceCodeVerifier;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: codeVerifier,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"PKCE code_verifier value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.pkceCodeVerifier.options,\n        maxAge: 0\n      }\n    });\n    checks.code_verifier = value.value;\n  }\n};\nconst STATE_MAX_AGE = 60 * 15;\nconst state = exports.state = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che, _options$cookies$stat;\n    if (!((_options$provider$che = options.provider.checks) !== null && _options$provider$che !== void 0 && _options$provider$che.includes(\"state\"))) return;\n    const value = _openidClient.generators.state();\n    resParams.state = value;\n    const maxAge = (_options$cookies$stat = options.cookies.state.options.maxAge) !== null && _options$cookies$stat !== void 0 ? _options$cookies$stat : STATE_MAX_AGE;\n    cookies.push(await signCookie(\"state\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider$che2;\n    if (!((_options$provider$che2 = options.provider.checks) !== null && _options$provider$che2 !== void 0 && _options$provider$che2.includes(\"state\"))) return;\n    const state = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.state.name];\n    if (!state) throw new TypeError(\"State cookie was missing.\");\n    const {\n      name\n    } = options.cookies.state;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: state,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"State value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.state.options,\n        maxAge: 0\n      }\n    });\n    checks.state = value.value;\n  }\n};\nconst NONCE_MAX_AGE = 60 * 15;\nconst nonce = exports.nonce = {\n  async create(options, cookies, resParams) {\n    var _options$provider$che3, _options$cookies$nonc;\n    if (!((_options$provider$che3 = options.provider.checks) !== null && _options$provider$che3 !== void 0 && _options$provider$che3.includes(\"nonce\"))) return;\n    const value = _openidClient.generators.nonce();\n    resParams.nonce = value;\n    const maxAge = (_options$cookies$nonc = options.cookies.nonce.options.maxAge) !== null && _options$cookies$nonc !== void 0 ? _options$cookies$nonc : NONCE_MAX_AGE;\n    cookies.push(await signCookie(\"nonce\", value, maxAge, options));\n  },\n  async use(cookies, resCookies, options, checks) {\n    var _options$provider3;\n    if (!((_options$provider3 = options.provider) !== null && _options$provider3 !== void 0 && (_options$provider3 = _options$provider3.checks) !== null && _options$provider3 !== void 0 && _options$provider3.includes(\"nonce\"))) return;\n    const nonce = cookies === null || cookies === void 0 ? void 0 : cookies[options.cookies.nonce.name];\n    if (!nonce) throw new TypeError(\"Nonce cookie was missing.\");\n    const {\n      name\n    } = options.cookies.nonce;\n    const value = await jwt.decode({\n      ...options.jwt,\n      token: nonce,\n      salt: name\n    });\n    if (!(value !== null && value !== void 0 && value.value)) throw new TypeError(\"Nonce value could not be parsed.\");\n    resCookies.push({\n      name,\n      value: \"\",\n      options: {\n        ...options.cookies.nonce.options,\n        maxAge: 0\n      }\n    });\n    checks.nonce = value.value;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/checks.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js":
/*!****************************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/client-legacy.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.oAuth1Client = oAuth1Client;\nexports.oAuth1TokenStore = void 0;\nvar _oauth = __webpack_require__(/*! oauth */ \"(rsc)/./node_modules/oauth/index.js\");\nfunction oAuth1Client(options) {\n  var _provider$version, _provider$encoding;\n  const provider = options.provider;\n  const oauth1Client = new _oauth.OAuth(provider.requestTokenUrl, provider.accessTokenUrl, provider.clientId, provider.clientSecret, (_provider$version = provider.version) !== null && _provider$version !== void 0 ? _provider$version : \"1.0\", provider.callbackUrl, (_provider$encoding = provider.encoding) !== null && _provider$encoding !== void 0 ? _provider$encoding : \"HMAC-SHA1\");\n  const originalGet = oauth1Client.get.bind(oauth1Client);\n  oauth1Client.get = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGet(...args, (error, result) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve(result);\n      });\n    });\n  };\n  const originalGetOAuth1AccessToken = oauth1Client.getOAuthAccessToken.bind(oauth1Client);\n  oauth1Client.getOAuthAccessToken = async (...args) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuth1AccessToken(...args, (error, oauth_token, oauth_token_secret) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret\n        });\n      });\n    });\n  };\n  const originalGetOAuthRequestToken = oauth1Client.getOAuthRequestToken.bind(oauth1Client);\n  oauth1Client.getOAuthRequestToken = async (params = {}) => {\n    return await new Promise((resolve, reject) => {\n      originalGetOAuthRequestToken(params, (error, oauth_token, oauth_token_secret, params) => {\n        if (error) {\n          return reject(error);\n        }\n        resolve({\n          oauth_token,\n          oauth_token_secret,\n          params\n        });\n      });\n    });\n  };\n  return oauth1Client;\n}\nconst oAuth1TokenStore = exports.oAuth1TokenStore = new Map();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/client-legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/oauth/client.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/lib/oauth/client.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.openidClient = openidClient;\nvar _openidClient = __webpack_require__(/*! openid-client */ \"(rsc)/./node_modules/openid-client/lib/index.js\");\nasync function openidClient(options) {\n  const provider = options.provider;\n  if (provider.httpOptions) _openidClient.custom.setHttpOptionsDefaults(provider.httpOptions);\n  let issuer;\n  if (provider.wellKnown) {\n    issuer = await _openidClient.Issuer.discover(provider.wellKnown);\n  } else {\n    var _provider$authorizati, _provider$token, _provider$userinfo;\n    issuer = new _openidClient.Issuer({\n      issuer: provider.issuer,\n      authorization_endpoint: (_provider$authorizati = provider.authorization) === null || _provider$authorizati === void 0 ? void 0 : _provider$authorizati.url,\n      token_endpoint: (_provider$token = provider.token) === null || _provider$token === void 0 ? void 0 : _provider$token.url,\n      userinfo_endpoint: (_provider$userinfo = provider.userinfo) === null || _provider$userinfo === void 0 ? void 0 : _provider$userinfo.url,\n      jwks_uri: provider.jwks_endpoint\n    });\n  }\n  const client = new issuer.Client({\n    client_id: provider.clientId,\n    client_secret: provider.clientSecret,\n    redirect_uris: [provider.callbackUrl],\n    ...provider.client\n  }, provider.jwks);\n  client[_openidClient.custom.clock_tolerance] = 10;\n  return client;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/oauth/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/providers.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/lib/providers.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseProviders;\nvar _merge = __webpack_require__(/*! ../../utils/merge */ \"(rsc)/./node_modules/next-auth/utils/merge.js\");\nfunction parseProviders(params) {\n  const {\n    url,\n    providerId\n  } = params;\n  const providers = params.providers.map(({\n    options: userOptions,\n    ...rest\n  }) => {\n    var _ref;\n    if (rest.type === \"oauth\") {\n      var _normalizedUserOption;\n      const normalizedOptions = normalizeOAuthOptions(rest);\n      const normalizedUserOptions = normalizeOAuthOptions(userOptions, true);\n      const id = (_normalizedUserOption = normalizedUserOptions === null || normalizedUserOptions === void 0 ? void 0 : normalizedUserOptions.id) !== null && _normalizedUserOption !== void 0 ? _normalizedUserOption : rest.id;\n      return (0, _merge.merge)(normalizedOptions, {\n        ...normalizedUserOptions,\n        signinUrl: `${url}/signin/${id}`,\n        callbackUrl: `${url}/callback/${id}`\n      });\n    }\n    const id = (_ref = userOptions === null || userOptions === void 0 ? void 0 : userOptions.id) !== null && _ref !== void 0 ? _ref : rest.id;\n    return (0, _merge.merge)(rest, {\n      ...userOptions,\n      signinUrl: `${url}/signin/${id}`,\n      callbackUrl: `${url}/callback/${id}`\n    });\n  });\n  return {\n    providers,\n    provider: providers.find(({\n      id\n    }) => id === providerId)\n  };\n}\nfunction normalizeOAuthOptions(oauthOptions, isUserOptions = false) {\n  var _normalized$version;\n  if (!oauthOptions) return;\n  const normalized = Object.entries(oauthOptions).reduce((acc, [key, value]) => {\n    if ([\"authorization\", \"token\", \"userinfo\"].includes(key) && typeof value === \"string\") {\n      var _url$searchParams;\n      const url = new URL(value);\n      acc[key] = {\n        url: `${url.origin}${url.pathname}`,\n        params: Object.fromEntries((_url$searchParams = url.searchParams) !== null && _url$searchParams !== void 0 ? _url$searchParams : [])\n      };\n    } else {\n      acc[key] = value;\n    }\n    return acc;\n  }, {});\n  if (!isUserOptions && !((_normalized$version = normalized.version) !== null && _normalized$version !== void 0 && _normalized$version.startsWith(\"1.\"))) {\n    var _ref2, _normalized$idToken, _normalized$wellKnown, _normalized$authoriza;\n    normalized.idToken = Boolean((_ref2 = (_normalized$idToken = normalized.idToken) !== null && _normalized$idToken !== void 0 ? _normalized$idToken : (_normalized$wellKnown = normalized.wellKnown) === null || _normalized$wellKnown === void 0 ? void 0 : _normalized$wellKnown.includes(\"openid-configuration\")) !== null && _ref2 !== void 0 ? _ref2 : (_normalized$authoriza = normalized.authorization) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.params) === null || _normalized$authoriza === void 0 || (_normalized$authoriza = _normalized$authoriza.scope) === null || _normalized$authoriza === void 0 ? void 0 : _normalized$authoriza.includes(\"openid\"));\n    if (!normalized.checks) normalized.checks = [\"state\"];\n  }\n  return normalized;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/providers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/lib/utils.js":
/*!**************************************************!*\
  !*** ./node_modules/next-auth/core/lib/utils.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.createSecret = createSecret;\nexports.fromDate = fromDate;\nexports.hashToken = hashToken;\nvar _crypto = __webpack_require__(/*! crypto */ \"crypto\");\nfunction fromDate(time, date = Date.now()) {\n  return new Date(date + time * 1000);\n}\nfunction hashToken(token, options) {\n  var _provider$secret;\n  const {\n    provider,\n    secret\n  } = options;\n  return (0, _crypto.createHash)(\"sha256\").update(`${token}${(_provider$secret = provider.secret) !== null && _provider$secret !== void 0 ? _provider$secret : secret}`).digest(\"hex\");\n}\nfunction createSecret(params) {\n  var _authOptions$secret;\n  const {\n    authOptions,\n    url\n  } = params;\n  return (_authOptions$secret = authOptions.secret) !== null && _authOptions$secret !== void 0 ? _authOptions$secret : (0, _crypto.createHash)(\"sha256\").update(JSON.stringify({\n    ...url,\n    ...authOptions\n  })).digest(\"hex\");\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/error.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/error.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = ErrorPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/preact/dist/preact.js\");\nfunction ErrorPage(props) {\n  var _errors$error$toLower;\n  const {\n    url,\n    error = \"default\",\n    theme\n  } = props;\n  const signinPageUrl = `${url}/signin`;\n  const errors = {\n    default: {\n      status: 200,\n      heading: \"Error\",\n      message: (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"site\",\n        href: url === null || url === void 0 ? void 0 : url.origin\n      }, url === null || url === void 0 ? void 0 : url.host))\n    },\n    configuration: {\n      status: 500,\n      heading: \"Server error\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"There is a problem with the server configuration.\"), (0, _preact.h)(\"p\", null, \"Check the server logs for more information.\"))\n    },\n    accessdenied: {\n      status: 403,\n      heading: \"Access Denied\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"You do not have permission to sign in.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")))\n    },\n    verification: {\n      status: 403,\n      heading: \"Unable to sign in\",\n      message: (0, _preact.h)(\"div\", null, (0, _preact.h)(\"p\", null, \"The sign in link is no longer valid.\"), (0, _preact.h)(\"p\", null, \"It may have been used already or it may have expired.\")),\n      signin: (0, _preact.h)(\"a\", {\n        className: \"button\",\n        href: signinPageUrl\n      }, \"Sign in\")\n    }\n  };\n  const {\n    status,\n    heading,\n    message,\n    signin\n  } = (_errors$error$toLower = errors[error.toLowerCase()]) !== null && _errors$error$toLower !== void 0 ? _errors$error$toLower : errors.default;\n  return {\n    status,\n    html: (0, _preact.h)(\"div\", {\n      className: \"error\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.brandColor) && (0, _preact.h)(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: `\n        :root {\n          --brand-color: ${theme === null || theme === void 0 ? void 0 : theme.brandColor}\n        }\n      `\n      }\n    }), (0, _preact.h)(\"div\", {\n      className: \"card\"\n    }, (theme === null || theme === void 0 ? void 0 : theme.logo) && (0, _preact.h)(\"img\", {\n      src: theme.logo,\n      alt: \"Logo\",\n      className: \"logo\"\n    }), (0, _preact.h)(\"h1\", null, heading), (0, _preact.h)(\"div\", {\n      className: \"message\"\n    }, message), signin))\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/index.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = renderPage;\nvar _preactRenderToString = _interopRequireDefault(__webpack_require__(/*! preact-render-to-string */ \"(rsc)/./node_modules/preact-render-to-string/dist/index.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ./signin */ \"(rsc)/./node_modules/next-auth/core/pages/signin.js\"));\nvar _signout = _interopRequireDefault(__webpack_require__(/*! ./signout */ \"(rsc)/./node_modules/next-auth/core/pages/signout.js\"));\nvar _verifyRequest = _interopRequireDefault(__webpack_require__(/*! ./verify-request */ \"(rsc)/./node_modules/next-auth/core/pages/verify-request.js\"));\nvar _error = _interopRequireDefault(__webpack_require__(/*! ./error */ \"(rsc)/./node_modules/next-auth/core/pages/error.js\"));\nvar _css = _interopRequireDefault(__webpack_require__(/*! ../../css */ \"(rsc)/./node_modules/next-auth/css/index.js\"));\nfunction renderPage(params) {\n  const {\n    url,\n    theme,\n    query,\n    cookies\n  } = params;\n  function send({\n    html,\n    title,\n    status\n  }) {\n    var _theme$colorScheme;\n    return {\n      cookies,\n      status,\n      headers: [{\n        key: \"Content-Type\",\n        value: \"text/html\"\n      }],\n      body: `<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><style>${(0, _css.default)()}</style><title>${title}</title></head><body class=\"__next-auth-theme-${(_theme$colorScheme = theme === null || theme === void 0 ? void 0 : theme.colorScheme) !== null && _theme$colorScheme !== void 0 ? _theme$colorScheme : \"auto\"}\"><div class=\"page\">${(0, _preactRenderToString.default)(html)}</div></body></html>`\n    };\n  }\n  return {\n    signin(props) {\n      return send({\n        html: (0, _signin.default)({\n          csrfToken: params.csrfToken,\n          providers: params.providers,\n          callbackUrl: params.callbackUrl,\n          theme,\n          ...query,\n          ...props\n        }),\n        title: \"Sign In\"\n      });\n    },\n    signout(props) {\n      return send({\n        html: (0, _signout.default)({\n          csrfToken: params.csrfToken,\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Sign Out\"\n      });\n    },\n    verifyRequest(props) {\n      return send({\n        html: (0, _verifyRequest.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Verify Request\"\n      });\n    },\n    error(props) {\n      return send({\n        ...(0, _error.default)({\n          url,\n          theme,\n          ...props\n        }),\n        title: \"Error\"\n      });\n    }\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/signin.js":
/*!*****************************************************!*\
  !*** ./node_modules/next-auth/core/pages/signin.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = SigninPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/preact/dist/preact.js\");\nvar _extends2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/extends */ \"(rsc)/./node_modules/@babel/runtime/helpers/extends.js\"));\nfunction hexToRgba(hex, alpha = 1) {\n  if (!hex) {\n    return;\n  }\n  hex = hex.replace(/^#/, \"\");\n  if (hex.length === 3) {\n    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];\n  }\n  const bigint = parseInt(hex, 16);\n  const r = bigint >> 16 & 255;\n  const g = bigint >> 8 & 255;\n  const b = bigint & 255;\n  alpha = Math.min(Math.max(alpha, 0), 1);\n  const rgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;\n  return rgba;\n}\nfunction SigninPage(props) {\n  var _errors$errorType;\n  const {\n    csrfToken,\n    providers,\n    callbackUrl,\n    theme,\n    email,\n    error: errorType\n  } = props;\n  const providersToRender = providers.filter(provider => {\n    if (provider.type === \"oauth\" || provider.type === \"email\") {\n      return true;\n    } else if (provider.type === \"credentials\" && provider.credentials) {\n      return true;\n    }\n    return false;\n  });\n  if (typeof document !== \"undefined\" && theme.buttonText) {\n    document.documentElement.style.setProperty(\"--button-text-color\", theme.buttonText);\n  }\n  if (typeof document !== \"undefined\" && theme.brandColor) {\n    document.documentElement.style.setProperty(\"--brand-color\", theme.brandColor);\n  }\n  const errors = {\n    Signin: \"Try signing in with a different account.\",\n    OAuthSignin: \"Try signing in with a different account.\",\n    OAuthCallback: \"Try signing in with a different account.\",\n    OAuthCreateAccount: \"Try signing in with a different account.\",\n    EmailCreateAccount: \"Try signing in with a different account.\",\n    Callback: \"Try signing in with a different account.\",\n    OAuthAccountNotLinked: \"To confirm your identity, sign in with the same account you used originally.\",\n    EmailSignin: \"The e-mail could not be sent.\",\n    CredentialsSignin: \"Sign in failed. Check the details you provided are correct.\",\n    SessionRequired: \"Please sign in to access this page.\",\n    default: \"Unable to sign in.\"\n  };\n  const error = errorType && ((_errors$errorType = errors[errorType]) !== null && _errors$errorType !== void 0 ? _errors$errorType : errors.default);\n  const providerLogoPath = \"https://authjs.dev/img/providers\";\n  return (0, _preact.h)(\"div\", {\n    className: \"signin\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), error && (0, _preact.h)(\"div\", {\n    className: \"error\"\n  }, (0, _preact.h)(\"p\", null, error)), providersToRender.map((provider, i) => {\n    let bg, text, logo, logoDark, bgDark, textDark;\n    if (provider.type === \"oauth\") {\n      var _provider$style;\n      ;\n      ({\n        bg = \"\",\n        text = \"\",\n        logo = \"\",\n        bgDark = bg,\n        textDark = text,\n        logoDark = \"\"\n      } = (_provider$style = provider.style) !== null && _provider$style !== void 0 ? _provider$style : {});\n      logo = logo.startsWith(\"/\") ? `${providerLogoPath}${logo}` : logo;\n      logoDark = logoDark.startsWith(\"/\") ? `${providerLogoPath}${logoDark}` : logoDark || logo;\n      logoDark || (logoDark = logo);\n    }\n    return (0, _preact.h)(\"div\", {\n      key: provider.id,\n      className: \"provider\"\n    }, provider.type === \"oauth\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), callbackUrl && (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"callbackUrl\",\n      value: callbackUrl\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\",\n      className: \"button\",\n      style: {\n        \"--provider-bg\": bg,\n        \"--provider-dark-bg\": bgDark,\n        \"--provider-color\": text,\n        \"--provider-dark-color\": textDark,\n        \"--provider-bg-hover\": hexToRgba(bg, 0.8),\n        \"--provider-dark-bg-hover\": hexToRgba(bgDark, 0.8)\n      }\n    }, logo && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logo}`\n    }), logoDark && (0, _preact.h)(\"img\", {\n      loading: \"lazy\",\n      height: 24,\n      width: 24,\n      id: \"provider-logo-dark\",\n      src: `${logo.startsWith(\"/\") ? providerLogoPath : \"\"}${logoDark}`\n    }), (0, _preact.h)(\"span\", null, \"Sign in with \", provider.name))), (provider.type === \"email\" || provider.type === \"credentials\") && i > 0 && providersToRender[i - 1].type !== \"email\" && providersToRender[i - 1].type !== \"credentials\" && (0, _preact.h)(\"hr\", null), provider.type === \"email\" && (0, _preact.h)(\"form\", {\n      action: provider.signinUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), (0, _preact.h)(\"label\", {\n      className: \"section-header\",\n      htmlFor: `input-email-for-${provider.id}-provider`\n    }, \"Email\"), (0, _preact.h)(\"input\", {\n      id: `input-email-for-${provider.id}-provider`,\n      autoFocus: true,\n      type: \"email\",\n      name: \"email\",\n      value: email,\n      placeholder: \"<EMAIL>\",\n      required: true\n    }), (0, _preact.h)(\"button\", {\n      id: \"submitButton\",\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), provider.type === \"credentials\" && (0, _preact.h)(\"form\", {\n      action: provider.callbackUrl,\n      method: \"POST\"\n    }, (0, _preact.h)(\"input\", {\n      type: \"hidden\",\n      name: \"csrfToken\",\n      value: csrfToken\n    }), Object.keys(provider.credentials).map(credential => {\n      var _provider$credentials, _provider$credentials2, _provider$credentials3;\n      return (0, _preact.h)(\"div\", {\n        key: `input-group-${provider.id}`\n      }, (0, _preact.h)(\"label\", {\n        className: \"section-header\",\n        htmlFor: `input-${credential}-for-${provider.id}-provider`\n      }, (_provider$credentials = provider.credentials[credential].label) !== null && _provider$credentials !== void 0 ? _provider$credentials : credential), (0, _preact.h)(\"input\", (0, _extends2.default)({\n        name: credential,\n        id: `input-${credential}-for-${provider.id}-provider`,\n        type: (_provider$credentials2 = provider.credentials[credential].type) !== null && _provider$credentials2 !== void 0 ? _provider$credentials2 : \"text\",\n        placeholder: (_provider$credentials3 = provider.credentials[credential].placeholder) !== null && _provider$credentials3 !== void 0 ? _provider$credentials3 : \"\"\n      }, provider.credentials[credential])));\n    }), (0, _preact.h)(\"button\", {\n      type: \"submit\"\n    }, \"Sign in with \", provider.name)), (provider.type === \"email\" || provider.type === \"credentials\") && i + 1 < providersToRender.length && (0, _preact.h)(\"hr\", null));\n  })));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/signout.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/pages/signout.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = SignoutPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/preact/dist/preact.js\");\nfunction SignoutPage(props) {\n  const {\n    url,\n    csrfToken,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"signout\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), theme.buttonText && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --button-text-color: ${theme.buttonText}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Signout\"), (0, _preact.h)(\"p\", null, \"Are you sure you want to sign out?\"), (0, _preact.h)(\"form\", {\n    action: `${url}/signout`,\n    method: \"POST\"\n  }, (0, _preact.h)(\"input\", {\n    type: \"hidden\",\n    name: \"csrfToken\",\n    value: csrfToken\n  }), (0, _preact.h)(\"button\", {\n    id: \"submitButton\",\n    type: \"submit\"\n  }, \"Sign out\"))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/signout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/pages/verify-request.js":
/*!*************************************************************!*\
  !*** ./node_modules/next-auth/core/pages/verify-request.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = VerifyRequestPage;\nvar _preact = __webpack_require__(/*! preact */ \"(rsc)/./node_modules/preact/dist/preact.js\");\nfunction VerifyRequestPage(props) {\n  const {\n    url,\n    theme\n  } = props;\n  return (0, _preact.h)(\"div\", {\n    className: \"verify-request\"\n  }, theme.brandColor && (0, _preact.h)(\"style\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        :root {\n          --brand-color: ${theme.brandColor}\n        }\n      `\n    }\n  }), (0, _preact.h)(\"div\", {\n    className: \"card\"\n  }, theme.logo && (0, _preact.h)(\"img\", {\n    src: theme.logo,\n    alt: \"Logo\",\n    className: \"logo\"\n  }), (0, _preact.h)(\"h1\", null, \"Check your email\"), (0, _preact.h)(\"p\", null, \"A sign in link has been sent to your email address.\"), (0, _preact.h)(\"p\", null, (0, _preact.h)(\"a\", {\n    className: \"site\",\n    href: url.origin\n  }, url.host))));\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/pages/verify-request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/callback.js":
/*!********************************************************!*\
  !*** ./node_modules/next-auth/core/routes/callback.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = callback;\nvar _callback = _interopRequireDefault(__webpack_require__(/*! ../lib/oauth/callback */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/callback.js\"));\nvar _callbackHandler = _interopRequireDefault(__webpack_require__(/*! ../lib/callback-handler */ \"(rsc)/./node_modules/next-auth/core/lib/callback-handler.js\"));\nvar _utils = __webpack_require__(/*! ../lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nvar _getUserFromEmail = _interopRequireDefault(__webpack_require__(/*! ../lib/email/getUserFromEmail */ \"(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\"));\nasync function callback(params) {\n  const {\n    options,\n    query,\n    body,\n    method,\n    headers,\n    sessionStore\n  } = params;\n  const {\n    provider,\n    adapter,\n    url,\n    callbackUrl,\n    pages,\n    jwt,\n    events,\n    callbacks,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    },\n    logger\n  } = options;\n  const cookies = [];\n  const useJwtSession = sessionStrategy === \"jwt\";\n  if (provider.type === \"oauth\") {\n    try {\n      const {\n        profile,\n        account,\n        OAuthProfile,\n        cookies: oauthCookies\n      } = await (0, _callback.default)({\n        query,\n        body,\n        method,\n        options,\n        cookies: params.cookies\n      });\n      if (oauthCookies.length) cookies.push(...oauthCookies);\n      try {\n        var _events$signIn;\n        logger.debug(\"OAUTH_CALLBACK_RESPONSE\", {\n          profile,\n          account,\n          OAuthProfile\n        });\n        if (!profile || !account || !OAuthProfile) {\n          return {\n            redirect: `${url}/signin`,\n            cookies\n          };\n        }\n        let userOrProfile = profile;\n        if (adapter) {\n          const {\n            getUserByAccount\n          } = adapter;\n          const userByAccount = await getUserByAccount({\n            providerAccountId: account.providerAccountId,\n            provider: provider.id\n          });\n          if (userByAccount) userOrProfile = userByAccount;\n        }\n        try {\n          const isAllowed = await callbacks.signIn({\n            user: userOrProfile,\n            account,\n            profile: OAuthProfile\n          });\n          if (!isAllowed) {\n            return {\n              redirect: `${url}/error?error=AccessDenied`,\n              cookies\n            };\n          } else if (typeof isAllowed === \"string\") {\n            return {\n              redirect: isAllowed,\n              cookies\n            };\n          }\n        } catch (error) {\n          return {\n            redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n            cookies\n          };\n        }\n        const {\n          user,\n          session,\n          isNewUser\n        } = await (0, _callbackHandler.default)({\n          sessionToken: sessionStore.value,\n          profile,\n          account,\n          options\n        });\n        if (useJwtSession) {\n          var _user$id;\n          const defaultToken = {\n            name: user.name,\n            email: user.email,\n            picture: user.image,\n            sub: (_user$id = user.id) === null || _user$id === void 0 ? void 0 : _user$id.toString()\n          };\n          const token = await callbacks.jwt({\n            token: defaultToken,\n            user,\n            account,\n            profile: OAuthProfile,\n            isNewUser,\n            trigger: isNewUser ? \"signUp\" : \"signIn\"\n          });\n          const newToken = await jwt.encode({\n            ...jwt,\n            token\n          });\n          const cookieExpires = new Date();\n          cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n          const sessionCookies = sessionStore.chunk(newToken, {\n            expires: cookieExpires\n          });\n          cookies.push(...sessionCookies);\n        } else {\n          cookies.push({\n            name: options.cookies.sessionToken.name,\n            value: session.sessionToken,\n            options: {\n              ...options.cookies.sessionToken.options,\n              expires: session.expires\n            }\n          });\n        }\n        await ((_events$signIn = events.signIn) === null || _events$signIn === void 0 ? void 0 : _events$signIn.call(events, {\n          user,\n          account,\n          profile,\n          isNewUser\n        }));\n        if (isNewUser && pages.newUser) {\n          return {\n            redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n            cookies\n          };\n        }\n        return {\n          redirect: callbackUrl,\n          cookies\n        };\n      } catch (error) {\n        if (error.name === \"AccountNotLinkedError\") {\n          return {\n            redirect: `${url}/error?error=OAuthAccountNotLinked`,\n            cookies\n          };\n        } else if (error.name === \"CreateUserError\") {\n          return {\n            redirect: `${url}/error?error=OAuthCreateAccount`,\n            cookies\n          };\n        }\n        logger.error(\"OAUTH_CALLBACK_HANDLER_ERROR\", error);\n        return {\n          redirect: `${url}/error?error=Callback`,\n          cookies\n        };\n      }\n    } catch (error) {\n      if (error.name === \"OAuthCallbackError\") {\n        logger.error(\"OAUTH_CALLBACK_ERROR\", {\n          error: error,\n          providerId: provider.id\n        });\n        return {\n          redirect: `${url}/error?error=OAuthCallback`,\n          cookies\n        };\n      }\n      logger.error(\"OAUTH_CALLBACK_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"email\") {\n    try {\n      var _events$signIn2;\n      const paramToken = query === null || query === void 0 ? void 0 : query.token;\n      const paramIdentifier = query === null || query === void 0 ? void 0 : query.email;\n      if (!paramToken) {\n        return {\n          redirect: `${url}/error?error=configuration`,\n          cookies\n        };\n      }\n      const invite = await adapter.useVerificationToken({\n        identifier: paramIdentifier,\n        token: (0, _utils.hashToken)(paramToken, options)\n      });\n      const invalidInvite = !invite || invite.expires.valueOf() < Date.now() || paramIdentifier && invite.identifier !== paramIdentifier;\n      if (invalidInvite) {\n        return {\n          redirect: `${url}/error?error=Verification`,\n          cookies\n        };\n      }\n      const profile = await (0, _getUserFromEmail.default)({\n        email: invite.identifier,\n        adapter\n      });\n      const account = {\n        providerAccountId: profile.email,\n        type: \"email\",\n        provider: provider.id\n      };\n      try {\n        const signInCallbackResponse = await callbacks.signIn({\n          user: profile,\n          account\n        });\n        if (!signInCallbackResponse) {\n          return {\n            redirect: `${url}/error?error=AccessDenied`,\n            cookies\n          };\n        } else if (typeof signInCallbackResponse === \"string\") {\n          return {\n            redirect: signInCallbackResponse,\n            cookies\n          };\n        }\n      } catch (error) {\n        return {\n          redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n          cookies\n        };\n      }\n      const {\n        user,\n        session,\n        isNewUser\n      } = await (0, _callbackHandler.default)({\n        sessionToken: sessionStore.value,\n        profile,\n        account,\n        options\n      });\n      if (useJwtSession) {\n        var _user$id2;\n        const defaultToken = {\n          name: user.name,\n          email: user.email,\n          picture: user.image,\n          sub: (_user$id2 = user.id) === null || _user$id2 === void 0 ? void 0 : _user$id2.toString()\n        };\n        const token = await callbacks.jwt({\n          token: defaultToken,\n          user,\n          account,\n          isNewUser,\n          trigger: isNewUser ? \"signUp\" : \"signIn\"\n        });\n        const newToken = await jwt.encode({\n          ...jwt,\n          token\n        });\n        const cookieExpires = new Date();\n        cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n        const sessionCookies = sessionStore.chunk(newToken, {\n          expires: cookieExpires\n        });\n        cookies.push(...sessionCookies);\n      } else {\n        cookies.push({\n          name: options.cookies.sessionToken.name,\n          value: session.sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: session.expires\n          }\n        });\n      }\n      await ((_events$signIn2 = events.signIn) === null || _events$signIn2 === void 0 ? void 0 : _events$signIn2.call(events, {\n        user,\n        account,\n        isNewUser\n      }));\n      if (isNewUser && pages.newUser) {\n        return {\n          redirect: `${pages.newUser}${pages.newUser.includes(\"?\") ? \"&\" : \"?\"}callbackUrl=${encodeURIComponent(callbackUrl)}`,\n          cookies\n        };\n      }\n      return {\n        redirect: callbackUrl,\n        cookies\n      };\n    } catch (error) {\n      if (error.name === \"CreateUserError\") {\n        return {\n          redirect: `${url}/error?error=EmailCreateAccount`,\n          cookies\n        };\n      }\n      logger.error(\"CALLBACK_EMAIL_ERROR\", error);\n      return {\n        redirect: `${url}/error?error=Callback`,\n        cookies\n      };\n    }\n  } else if (provider.type === \"credentials\" && method === \"POST\") {\n    var _user$id3, _events$signIn3;\n    const credentials = body;\n    let user;\n    try {\n      user = await provider.authorize(credentials, {\n        query,\n        body,\n        headers,\n        method\n      });\n      if (!user) {\n        return {\n          status: 401,\n          redirect: `${url}/error?${new URLSearchParams({\n            error: \"CredentialsSignin\",\n            provider: provider.id\n          })}`,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        status: 401,\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const account = {\n      providerAccountId: user.id,\n      type: \"credentials\",\n      provider: provider.id\n    };\n    try {\n      const isAllowed = await callbacks.signIn({\n        user,\n        account,\n        credentials\n      });\n      if (!isAllowed) {\n        return {\n          status: 403,\n          redirect: `${url}/error?error=AccessDenied`,\n          cookies\n        };\n      } else if (typeof isAllowed === \"string\") {\n        return {\n          redirect: isAllowed,\n          cookies\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?error=${encodeURIComponent(error.message)}`,\n        cookies\n      };\n    }\n    const defaultToken = {\n      name: user.name,\n      email: user.email,\n      picture: user.image,\n      sub: (_user$id3 = user.id) === null || _user$id3 === void 0 ? void 0 : _user$id3.toString()\n    };\n    const token = await callbacks.jwt({\n      token: defaultToken,\n      user,\n      account,\n      isNewUser: false,\n      trigger: \"signIn\"\n    });\n    const newToken = await jwt.encode({\n      ...jwt,\n      token\n    });\n    const cookieExpires = new Date();\n    cookieExpires.setTime(cookieExpires.getTime() + sessionMaxAge * 1000);\n    const sessionCookies = sessionStore.chunk(newToken, {\n      expires: cookieExpires\n    });\n    cookies.push(...sessionCookies);\n    await ((_events$signIn3 = events.signIn) === null || _events$signIn3 === void 0 ? void 0 : _events$signIn3.call(events, {\n      user,\n      account\n    }));\n    return {\n      redirect: callbackUrl,\n      cookies\n    };\n  }\n  return {\n    status: 500,\n    body: `Error: Callback for provider type ${provider.type} not supported`,\n    cookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/callback.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/next-auth/core/routes/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"callback\", ({\n  enumerable: true,\n  get: function () {\n    return _callback.default;\n  }\n}));\nObject.defineProperty(exports, \"providers\", ({\n  enumerable: true,\n  get: function () {\n    return _providers.default;\n  }\n}));\nObject.defineProperty(exports, \"session\", ({\n  enumerable: true,\n  get: function () {\n    return _session.default;\n  }\n}));\nObject.defineProperty(exports, \"signin\", ({\n  enumerable: true,\n  get: function () {\n    return _signin.default;\n  }\n}));\nObject.defineProperty(exports, \"signout\", ({\n  enumerable: true,\n  get: function () {\n    return _signout.default;\n  }\n}));\nvar _callback = _interopRequireDefault(__webpack_require__(/*! ./callback */ \"(rsc)/./node_modules/next-auth/core/routes/callback.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ./signin */ \"(rsc)/./node_modules/next-auth/core/routes/signin.js\"));\nvar _signout = _interopRequireDefault(__webpack_require__(/*! ./signout */ \"(rsc)/./node_modules/next-auth/core/routes/signout.js\"));\nvar _session = _interopRequireDefault(__webpack_require__(/*! ./session */ \"(rsc)/./node_modules/next-auth/core/routes/session.js\"));\nvar _providers = _interopRequireDefault(__webpack_require__(/*! ./providers */ \"(rsc)/./node_modules/next-auth/core/routes/providers.js\"));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/providers.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/core/routes/providers.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = providers;\nfunction providers(providers) {\n  return {\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    body: providers.reduce((acc, {\n      id,\n      name,\n      type,\n      signinUrl,\n      callbackUrl\n    }) => {\n      acc[id] = {\n        id,\n        name,\n        type,\n        signinUrl,\n        callbackUrl\n      };\n      return acc;\n    }, {})\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvcm91dGVzL3Byb3ZpZGVycy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLLElBQUk7QUFDVDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxcY29yZVxccm91dGVzXFxwcm92aWRlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBwcm92aWRlcnM7XG5mdW5jdGlvbiBwcm92aWRlcnMocHJvdmlkZXJzKSB7XG4gIHJldHVybiB7XG4gICAgaGVhZGVyczogW3tcbiAgICAgIGtleTogXCJDb250ZW50LVR5cGVcIixcbiAgICAgIHZhbHVlOiBcImFwcGxpY2F0aW9uL2pzb25cIlxuICAgIH1dLFxuICAgIGJvZHk6IHByb3ZpZGVycy5yZWR1Y2UoKGFjYywge1xuICAgICAgaWQsXG4gICAgICBuYW1lLFxuICAgICAgdHlwZSxcbiAgICAgIHNpZ25pblVybCxcbiAgICAgIGNhbGxiYWNrVXJsXG4gICAgfSkgPT4ge1xuICAgICAgYWNjW2lkXSA9IHtcbiAgICAgICAgaWQsXG4gICAgICAgIG5hbWUsXG4gICAgICAgIHR5cGUsXG4gICAgICAgIHNpZ25pblVybCxcbiAgICAgICAgY2FsbGJhY2tVcmxcbiAgICAgIH07XG4gICAgICByZXR1cm4gYWNjO1xuICAgIH0sIHt9KVxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/providers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/session.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/session.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = session;\nvar _utils = __webpack_require__(/*! ../lib/utils */ \"(rsc)/./node_modules/next-auth/core/lib/utils.js\");\nasync function session(params) {\n  const {\n    options,\n    sessionStore,\n    newSession,\n    isUpdate\n  } = params;\n  const {\n    adapter,\n    jwt,\n    events,\n    callbacks,\n    logger,\n    session: {\n      strategy: sessionStrategy,\n      maxAge: sessionMaxAge\n    }\n  } = options;\n  const response = {\n    body: {},\n    headers: [{\n      key: \"Content-Type\",\n      value: \"application/json\"\n    }],\n    cookies: []\n  };\n  const sessionToken = sessionStore.value;\n  if (!sessionToken) return response;\n  if (sessionStrategy === \"jwt\") {\n    try {\n      var _response$cookies, _events$session;\n      const decodedToken = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      if (!decodedToken) throw new Error(\"JWT invalid\");\n      const token = await callbacks.jwt({\n        token: decodedToken,\n        ...(isUpdate && {\n          trigger: \"update\"\n        }),\n        session: newSession\n      });\n      const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n      const updatedSession = await callbacks.session({\n        session: {\n          user: {\n            name: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.name,\n            email: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.email,\n            image: decodedToken === null || decodedToken === void 0 ? void 0 : decodedToken.picture\n          },\n          expires: newExpires.toISOString()\n        },\n        token\n      });\n      response.body = updatedSession;\n      const newToken = await jwt.encode({\n        ...jwt,\n        token,\n        maxAge: options.session.maxAge\n      });\n      const sessionCookies = sessionStore.chunk(newToken, {\n        expires: newExpires\n      });\n      (_response$cookies = response.cookies) === null || _response$cookies === void 0 || _response$cookies.push(...sessionCookies);\n      await ((_events$session = events.session) === null || _events$session === void 0 ? void 0 : _events$session.call(events, {\n        session: updatedSession,\n        token\n      }));\n    } catch (error) {\n      var _response$cookies2;\n      logger.error(\"JWT_SESSION_ERROR\", error);\n      (_response$cookies2 = response.cookies) === null || _response$cookies2 === void 0 || _response$cookies2.push(...sessionStore.clean());\n    }\n  } else {\n    try {\n      const {\n        getSessionAndUser,\n        deleteSession,\n        updateSession\n      } = adapter;\n      let userAndSession = await getSessionAndUser(sessionToken);\n      if (userAndSession && userAndSession.session.expires.valueOf() < Date.now()) {\n        await deleteSession(sessionToken);\n        userAndSession = null;\n      }\n      if (userAndSession) {\n        var _response$cookies3, _events$session2;\n        const {\n          user,\n          session\n        } = userAndSession;\n        const sessionUpdateAge = options.session.updateAge;\n        const sessionIsDueToBeUpdatedDate = session.expires.valueOf() - sessionMaxAge * 1000 + sessionUpdateAge * 1000;\n        const newExpires = (0, _utils.fromDate)(sessionMaxAge);\n        if (sessionIsDueToBeUpdatedDate <= Date.now()) {\n          await updateSession({\n            sessionToken,\n            expires: newExpires\n          });\n        }\n        const sessionPayload = await callbacks.session({\n          session: {\n            user: {\n              name: user.name,\n              email: user.email,\n              image: user.image\n            },\n            expires: session.expires.toISOString()\n          },\n          user,\n          newSession,\n          ...(isUpdate ? {\n            trigger: \"update\"\n          } : {})\n        });\n        response.body = sessionPayload;\n        (_response$cookies3 = response.cookies) === null || _response$cookies3 === void 0 || _response$cookies3.push({\n          name: options.cookies.sessionToken.name,\n          value: sessionToken,\n          options: {\n            ...options.cookies.sessionToken.options,\n            expires: newExpires\n          }\n        });\n        await ((_events$session2 = events.session) === null || _events$session2 === void 0 ? void 0 : _events$session2.call(events, {\n          session: sessionPayload\n        }));\n      } else if (sessionToken) {\n        var _response$cookies4;\n        (_response$cookies4 = response.cookies) === null || _response$cookies4 === void 0 || _response$cookies4.push(...sessionStore.clean());\n      }\n    } catch (error) {\n      logger.error(\"SESSION_ERROR\", error);\n    }\n  }\n  return response;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/session.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/signin.js":
/*!******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/signin.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = signin;\nvar _authorizationUrl = _interopRequireDefault(__webpack_require__(/*! ../lib/oauth/authorization-url */ \"(rsc)/./node_modules/next-auth/core/lib/oauth/authorization-url.js\"));\nvar _signin = _interopRequireDefault(__webpack_require__(/*! ../lib/email/signin */ \"(rsc)/./node_modules/next-auth/core/lib/email/signin.js\"));\nvar _getUserFromEmail = _interopRequireDefault(__webpack_require__(/*! ../lib/email/getUserFromEmail */ \"(rsc)/./node_modules/next-auth/core/lib/email/getUserFromEmail.js\"));\nasync function signin(params) {\n  const {\n    options,\n    query,\n    body\n  } = params;\n  const {\n    url,\n    callbacks,\n    logger,\n    provider\n  } = options;\n  if (!provider.type) {\n    return {\n      status: 500,\n      text: `Error: Type not specified for ${provider.name}`\n    };\n  }\n  if (provider.type === \"oauth\") {\n    try {\n      const response = await (0, _authorizationUrl.default)({\n        options,\n        query\n      });\n      return response;\n    } catch (error) {\n      logger.error(\"SIGNIN_OAUTH_ERROR\", {\n        error: error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=OAuthSignin`\n      };\n    }\n  } else if (provider.type === \"email\") {\n    var _provider$normalizeId;\n    let email = body === null || body === void 0 ? void 0 : body.email;\n    if (!email) return {\n      redirect: `${url}/error?error=EmailSignin`\n    };\n    const normalizer = (_provider$normalizeId = provider.normalizeIdentifier) !== null && _provider$normalizeId !== void 0 ? _provider$normalizeId : identifier => {\n      let [local, domain] = identifier.toLowerCase().trim().split(\"@\");\n      domain = domain.split(\",\")[0];\n      return `${local}@${domain}`;\n    };\n    try {\n      email = normalizer(body === null || body === void 0 ? void 0 : body.email);\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n    const user = await (0, _getUserFromEmail.default)({\n      email,\n      adapter: options.adapter\n    });\n    const account = {\n      providerAccountId: email,\n      userId: email,\n      type: \"email\",\n      provider: provider.id\n    };\n    try {\n      const signInCallbackResponse = await callbacks.signIn({\n        user,\n        account,\n        email: {\n          verificationRequest: true\n        }\n      });\n      if (!signInCallbackResponse) {\n        return {\n          redirect: `${url}/error?error=AccessDenied`\n        };\n      } else if (typeof signInCallbackResponse === \"string\") {\n        return {\n          redirect: signInCallbackResponse\n        };\n      }\n    } catch (error) {\n      return {\n        redirect: `${url}/error?${new URLSearchParams({\n          error: error\n        })}`\n      };\n    }\n    try {\n      const redirect = await (0, _signin.default)(email, options);\n      return {\n        redirect\n      };\n    } catch (error) {\n      logger.error(\"SIGNIN_EMAIL_ERROR\", {\n        error,\n        providerId: provider.id\n      });\n      return {\n        redirect: `${url}/error?error=EmailSignin`\n      };\n    }\n  }\n  return {\n    redirect: `${url}/signin`\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/signin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/routes/signout.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/core/routes/signout.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = signout;\nasync function signout(params) {\n  const {\n    options,\n    sessionStore\n  } = params;\n  const {\n    adapter,\n    events,\n    jwt,\n    callbackUrl,\n    logger,\n    session\n  } = options;\n  const sessionToken = sessionStore === null || sessionStore === void 0 ? void 0 : sessionStore.value;\n  if (!sessionToken) {\n    return {\n      redirect: callbackUrl\n    };\n  }\n  if (session.strategy === \"jwt\") {\n    try {\n      var _events$signOut;\n      const decodedJwt = await jwt.decode({\n        ...jwt,\n        token: sessionToken\n      });\n      await ((_events$signOut = events.signOut) === null || _events$signOut === void 0 ? void 0 : _events$signOut.call(events, {\n        token: decodedJwt\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  } else {\n    try {\n      var _events$signOut2;\n      const session = await adapter.deleteSession(sessionToken);\n      await ((_events$signOut2 = events.signOut) === null || _events$signOut2 === void 0 ? void 0 : _events$signOut2.call(events, {\n        session\n      }));\n    } catch (error) {\n      logger.error(\"SIGNOUT_ERROR\", error);\n    }\n  }\n  const sessionCookies = sessionStore.clean();\n  return {\n    redirect: callbackUrl,\n    cookies: sessionCookies\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/routes/signout.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/core/types.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/core/types.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2NvcmUvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxcY29yZVxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/core/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/css/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/css/index.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = function() { return \":root{--border-width:1px;--border-radius:0.5rem;--color-error:#c94b4b;--color-info:#157efb;--color-info-hover:#0f6ddb;--color-info-text:#fff}.__next-auth-theme-auto,.__next-auth-theme-light{--color-background:#ececec;--color-background-hover:hsla(0,0%,93%,.8);--color-background-card:#fff;--color-text:#000;--color-primary:#444;--color-control-border:#bbb;--color-button-active-background:#f9f9f9;--color-button-active-border:#aaa;--color-separator:#ccc}.__next-auth-theme-dark{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}@media (prefers-color-scheme:dark){.__next-auth-theme-auto{--color-background:#161b22;--color-background-hover:rgba(22,27,34,.8);--color-background-card:#0d1117;--color-text:#fff;--color-primary:#ccc;--color-control-border:#555;--color-button-active-background:#060606;--color-button-active-border:#666;--color-separator:#444}a.button,button{background-color:var(--provider-dark-bg,var(--color-background));color:var(--provider-dark-color,var(--color-primary))}a.button:hover,button:hover{background-color:var(--provider-dark-bg-hover,var(--color-background-hover))!important}#provider-logo{display:none!important}#provider-logo-dark{display:block!important;width:25px}}html{box-sizing:border-box}*,:after,:before{box-sizing:inherit;margin:0;padding:0}body{background-color:var(--color-background);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;margin:0;padding:0}h1{font-weight:400}h1,p{color:var(--color-text);margin-bottom:1.5rem;padding:0 1rem}form{margin:0;padding:0}label{font-weight:500;margin-bottom:.25rem;text-align:left}input[type],label{color:var(--color-text);display:block}input[type]{background:var(--color-background-card);border:var(--border-width) solid var(--color-control-border);border-radius:var(--border-radius);box-sizing:border-box;font-size:1rem;padding:.5rem 1rem;width:100%}input[type]:focus{box-shadow:none}p{font-size:1.1rem;line-height:2rem}a.button{line-height:1rem;text-decoration:none}a.button:link,a.button:visited{background-color:var(--color-background);color:var(--color-primary)}button span{flex-grow:1}a.button,button{align-items:center;background-color:var(--provider-bg);border-color:rgba(0,0,0,.1);border-radius:var(--border-radius);color:var(--provider-color,var(--color-primary));display:flex;font-size:1.1rem;font-weight:500;justify-content:center;min-height:62px;padding:.75rem 1rem;position:relative;transition:all .1s ease-in-out}a.button:hover,button:hover{background-color:var(--provider-bg-hover,var(--color-background-hover));cursor:pointer}a.button:active,button:active{cursor:pointer}a.button #provider-logo,button #provider-logo{display:block;width:25px}a.button #provider-logo-dark,button #provider-logo-dark{display:none}#submitButton{background-color:var(--brand-color,var(--color-info));color:var(--button-text-color,var(--color-info-text));width:100%}#submitButton:hover{background-color:var(--button-hover-bg,var(--color-info-hover))!important}a.site{color:var(--color-primary);font-size:1rem;line-height:2rem;text-decoration:none}a.site:hover{text-decoration:underline}.page{box-sizing:border-box;display:grid;height:100%;margin:0;padding:0;place-items:center;position:absolute;width:100%}.page>div{text-align:center}.error a.button{margin-top:.5rem;padding-left:2rem;padding-right:2rem}.error .message{margin-bottom:1.5rem}.signin input[type=text]{display:block;margin-left:auto;margin-right:auto}.signin hr{border:0;border-top:1px solid var(--color-separator);display:block;margin:2rem auto 1rem;overflow:visible}.signin hr:before{background:var(--color-background-card);color:#888;content:\\\"or\\\";padding:0 .4rem;position:relative;top:-.7rem}.signin .error{background:#f5f5f5;background:var(--color-error);border-radius:.3rem;font-weight:500}.signin .error p{color:var(--color-info-text);font-size:.9rem;line-height:1.2rem;padding:.5rem 1rem;text-align:left}.signin form,.signin>div{display:block}.signin form input[type],.signin>div input[type]{margin-bottom:.5rem}.signin form button,.signin>div button{width:100%}.signin .provider+.provider{margin-top:1rem}.logo{display:inline-block;margin:1.25rem 0;max-height:70px;max-width:150px}.card{background-color:var(--color-background-card);border-radius:2rem;padding:1.25rem 2rem}.card .header{color:var(--color-primary)}.section-header{color:var(--color-text)}@media screen and (min-width:450px){.card{margin:2rem 0;width:368px}}@media screen and (max-width:450px){.card{margin:1rem 0;width:343px}}\" }//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/css/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {};\nObject.defineProperty(exports, \"default\", ({\n  enumerable: true,\n  get: function () {\n    return _next.default;\n  }\n}));\nvar _types = __webpack_require__(/*! ./core/types */ \"(rsc)/./node_modules/next-auth/core/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nvar _next = _interopRequireWildcard(__webpack_require__(/*! ./next */ \"(rsc)/./node_modules/next-auth/next/index.js\"));\nObject.keys(_next).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _next[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _next[key];\n    }\n  });\n});\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/jwt/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/jwt/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nvar _exportNames = {\n  encode: true,\n  decode: true,\n  getToken: true\n};\nexports.decode = decode;\nexports.encode = encode;\nexports.getToken = getToken;\nvar _jose = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/cjs/index.js\");\nvar _hkdf = _interopRequireDefault(__webpack_require__(/*! @panva/hkdf */ \"(rsc)/./node_modules/@panva/hkdf/dist/node/cjs/index.js\"));\nvar _uuid = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/index.js\");\nvar _cookie = __webpack_require__(/*! ../core/lib/cookie */ \"(rsc)/./node_modules/next-auth/core/lib/cookie.js\");\nvar _types = __webpack_require__(/*! ./types */ \"(rsc)/./node_modules/next-auth/jwt/types.js\");\nObject.keys(_types).forEach(function (key) {\n  if (key === \"default\" || key === \"__esModule\") return;\n  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;\n  if (key in exports && exports[key] === _types[key]) return;\n  Object.defineProperty(exports, key, {\n    enumerable: true,\n    get: function () {\n      return _types[key];\n    }\n  });\n});\nconst DEFAULT_MAX_AGE = 30 * 24 * 60 * 60;\nconst now = () => Date.now() / 1000 | 0;\nasync function encode(params) {\n  const {\n    token = {},\n    secret,\n    maxAge = DEFAULT_MAX_AGE,\n    salt = \"\"\n  } = params;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  return await new _jose.EncryptJWT(token).setProtectedHeader({\n    alg: \"dir\",\n    enc: \"A256GCM\"\n  }).setIssuedAt().setExpirationTime(now() + maxAge).setJti((0, _uuid.v4)()).encrypt(encryptionSecret);\n}\nasync function decode(params) {\n  const {\n    token,\n    secret,\n    salt = \"\"\n  } = params;\n  if (!token) return null;\n  const encryptionSecret = await getDerivedEncryptionKey(secret, salt);\n  const {\n    payload\n  } = await (0, _jose.jwtDecrypt)(token, encryptionSecret, {\n    clockTolerance: 15\n  });\n  return payload;\n}\nasync function getToken(params) {\n  var _process$env$NEXTAUTH, _process$env$NEXTAUTH2, _process$env$NEXTAUTH3, _req$headers;\n  const {\n    req,\n    secureCookie = (_process$env$NEXTAUTH = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_URL) === null || _process$env$NEXTAUTH2 === void 0 ? void 0 : _process$env$NEXTAUTH2.startsWith(\"https://\")) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : !!process.env.VERCEL,\n    cookieName = secureCookie ? \"__Secure-next-auth.session-token\" : \"next-auth.session-token\",\n    raw,\n    decode: _decode = decode,\n    logger = console,\n    secret = (_process$env$NEXTAUTH3 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH3 !== void 0 ? _process$env$NEXTAUTH3 : process.env.AUTH_SECRET\n  } = params;\n  if (!req) throw new Error(\"Must pass `req` to JWT getToken()\");\n  const sessionStore = new _cookie.SessionStore({\n    name: cookieName,\n    options: {\n      secure: secureCookie\n    }\n  }, {\n    cookies: req.cookies,\n    headers: req.headers\n  }, logger);\n  let token = sessionStore.value;\n  const authorizationHeader = req.headers instanceof Headers ? req.headers.get(\"authorization\") : (_req$headers = req.headers) === null || _req$headers === void 0 ? void 0 : _req$headers.authorization;\n  if (!token && (authorizationHeader === null || authorizationHeader === void 0 ? void 0 : authorizationHeader.split(\" \")[0]) === \"Bearer\") {\n    const urlEncodedToken = authorizationHeader.split(\" \")[1];\n    token = decodeURIComponent(urlEncodedToken);\n  }\n  if (!token) return null;\n  if (raw) return token;\n  try {\n    return await _decode({\n      token,\n      secret\n    });\n  } catch (_unused) {\n    return null;\n  }\n}\nasync function getDerivedEncryptionKey(keyMaterial, salt) {\n  return await (0, _hkdf.default)(\"sha256\", keyMaterial, salt, `NextAuth.js Generated Encryption Key${salt ? ` (${salt})` : \"\"}`, 32);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/jwt/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/jwt/types.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/jwt/types.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2p3dC90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxqd3RcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/jwt/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/next/index.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/next/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.getServerSession = getServerSession;\nexports.unstable_getServerSession = unstable_getServerSession;\nvar _core = __webpack_require__(/*! ../core */ \"(rsc)/./node_modules/next-auth/core/index.js\");\nvar _utils = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/next-auth/next/utils.js\");\nasync function NextAuthApiHandler(req, res, options) {\n  var _options$secret, _ref, _options$jwt$secret, _options$jwt, _ref2, _handler$status, _handler$cookies, _handler$headers;\n  const {\n    nextauth,\n    ...query\n  } = req.query;\n  (_options$secret = options.secret) !== null && _options$secret !== void 0 ? _options$secret : options.secret = (_ref = (_options$jwt$secret = (_options$jwt = options.jwt) === null || _options$jwt === void 0 ? void 0 : _options$jwt.secret) !== null && _options$jwt$secret !== void 0 ? _options$jwt$secret : process.env.NEXTAUTH_SECRET) !== null && _ref !== void 0 ? _ref : process.env.AUTH_SECRET;\n  const handler = await (0, _core.AuthHandler)({\n    req: {\n      body: req.body,\n      query,\n      cookies: req.cookies,\n      headers: req.headers,\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_ref2 = req.query.error) !== null && _ref2 !== void 0 ? _ref2 : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  res.status((_handler$status = handler.status) !== null && _handler$status !== void 0 ? _handler$status : 200);\n  (_handler$cookies = handler.cookies) === null || _handler$cookies === void 0 || _handler$cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  (_handler$headers = handler.headers) === null || _handler$headers === void 0 || _handler$headers.forEach(h => res.setHeader(h.key, h.value));\n  if (handler.redirect) {\n    var _req$body;\n    if (((_req$body = req.body) === null || _req$body === void 0 ? void 0 : _req$body.json) !== \"true\") {\n      res.status(302).setHeader(\"Location\", handler.redirect);\n      res.end();\n      return;\n    }\n    return res.json({\n      url: handler.redirect\n    });\n  }\n  return res.send(handler.body);\n}\nasync function NextAuthRouteHandler(req, context, options) {\n  var _options$secret2, _process$env$NEXTAUTH, _await$context$params, _query$error;\n  (_options$secret2 = options.secret) !== null && _options$secret2 !== void 0 ? _options$secret2 : options.secret = (_process$env$NEXTAUTH = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH !== void 0 ? _process$env$NEXTAUTH : process.env.AUTH_SECRET;\n  const {\n    headers,\n    cookies\n  } = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n  const nextauth = (_await$context$params = await context.params) === null || _await$context$params === void 0 ? void 0 : _await$context$params.nextauth;\n  const query = Object.fromEntries(req.nextUrl.searchParams);\n  const body = await (0, _utils.getBody)(req);\n  const internalResponse = await (0, _core.AuthHandler)({\n    req: {\n      body,\n      query,\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value])),\n      headers: Object.fromEntries(await headers()),\n      method: req.method,\n      action: nextauth === null || nextauth === void 0 ? void 0 : nextauth[0],\n      providerId: nextauth === null || nextauth === void 0 ? void 0 : nextauth[1],\n      error: (_query$error = query.error) !== null && _query$error !== void 0 ? _query$error : nextauth === null || nextauth === void 0 ? void 0 : nextauth[1]\n    },\n    options\n  });\n  const response = (0, _utils.toResponse)(internalResponse);\n  const redirect = response.headers.get(\"Location\");\n  if ((body === null || body === void 0 ? void 0 : body.json) === \"true\" && redirect) {\n    response.headers.delete(\"Location\");\n    response.headers.set(\"Content-Type\", \"application/json\");\n    return new Response(JSON.stringify({\n      url: redirect\n    }), {\n      status: internalResponse.status,\n      headers: response.headers\n    });\n  }\n  return response;\n}\nfunction NextAuth(...args) {\n  var _args$;\n  if (args.length === 1) {\n    return async (req, res) => {\n      if (res !== null && res !== void 0 && res.params) {\n        return await NextAuthRouteHandler(req, res, args[0]);\n      }\n      return await NextAuthApiHandler(req, res, args[0]);\n    };\n  }\n  if ((_args$ = args[1]) !== null && _args$ !== void 0 && _args$.params) {\n    return NextAuthRouteHandler(...args);\n  }\n  return NextAuthApiHandler(...args);\n}\nvar _default = exports[\"default\"] = NextAuth;\nasync function getServerSession(...args) {\n  var _options, _options$secret3, _process$env$NEXTAUTH2;\n  const isRSC = args.length === 0 || args.length === 1;\n  let req, res, options;\n  if (isRSC) {\n    options = Object.assign({}, args[0], {\n      providers: []\n    });\n    const {\n      headers,\n      cookies\n    } = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n    req = {\n      headers: Object.fromEntries(await headers()),\n      cookies: Object.fromEntries((await cookies()).getAll().map(c => [c.name, c.value]))\n    };\n    res = {\n      getHeader() {},\n      setCookie() {},\n      setHeader() {}\n    };\n  } else {\n    req = args[0];\n    res = args[1];\n    options = Object.assign({}, args[2], {\n      providers: []\n    });\n  }\n  (_options$secret3 = (_options = options).secret) !== null && _options$secret3 !== void 0 ? _options$secret3 : _options.secret = (_process$env$NEXTAUTH2 = process.env.NEXTAUTH_SECRET) !== null && _process$env$NEXTAUTH2 !== void 0 ? _process$env$NEXTAUTH2 : process.env.AUTH_SECRET;\n  const session = await (0, _core.AuthHandler)({\n    options,\n    req: {\n      action: \"session\",\n      method: \"GET\",\n      cookies: req.cookies,\n      headers: req.headers\n    }\n  });\n  const {\n    body,\n    cookies,\n    status = 200\n  } = session;\n  cookies === null || cookies === void 0 || cookies.forEach(cookie => (0, _utils.setCookie)(res, cookie));\n  if (body && typeof body !== \"string\" && Object.keys(body).length) {\n    if (status === 200) {\n      if (isRSC) delete body.expires;\n      return body;\n    }\n    throw new Error(body.message);\n  }\n  return null;\n}\nlet deprecatedWarningShown = false;\nasync function unstable_getServerSession(...args) {\n  if (!deprecatedWarningShown && \"development\" !== \"production\") {\n    console.warn(\"`unstable_getServerSession` has been renamed to `getServerSession`.\");\n    deprecatedWarningShown = true;\n  }\n  return await getServerSession(...args);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/next/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/next/utils.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/next/utils.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.getBody = getBody;\nexports.setCookie = setCookie;\nexports.toResponse = toResponse;\nvar _cookie = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/cookie/index.js\");\nfunction setCookie(res, cookie) {\n  var _res$getHeader;\n  let setCookieHeader = (_res$getHeader = res.getHeader(\"Set-Cookie\")) !== null && _res$getHeader !== void 0 ? _res$getHeader : [];\n  if (!Array.isArray(setCookieHeader)) {\n    setCookieHeader = [setCookieHeader];\n  }\n  const {\n    name,\n    value,\n    options\n  } = cookie;\n  const cookieHeader = (0, _cookie.serialize)(name, value, options);\n  setCookieHeader.push(cookieHeader);\n  res.setHeader(\"Set-Cookie\", setCookieHeader);\n}\nasync function getBody(req) {\n  if (!(\"body\" in req) || !req.body || req.method !== \"POST\") return;\n  const contentType = req.headers.get(\"content-type\");\n  if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/json\")) {\n    return await req.json();\n  } else if (contentType !== null && contentType !== void 0 && contentType.includes(\"application/x-www-form-urlencoded\")) {\n    const params = new URLSearchParams(await req.text());\n    return Object.fromEntries(params);\n  }\n}\nfunction toResponse(res) {\n  var _res$headers, _res$cookies, _res$status;\n  const headers = new Headers((_res$headers = res.headers) === null || _res$headers === void 0 ? void 0 : _res$headers.reduce((acc, {\n    key,\n    value\n  }) => {\n    acc[key] = value;\n    return acc;\n  }, {}));\n  (_res$cookies = res.cookies) === null || _res$cookies === void 0 || _res$cookies.forEach(cookie => {\n    const {\n      name,\n      value,\n      options\n    } = cookie;\n    const cookieHeader = (0, _cookie.serialize)(name, value, options);\n    if (headers.has(\"Set-Cookie\")) headers.append(\"Set-Cookie\", cookieHeader);else headers.set(\"Set-Cookie\", cookieHeader);\n  });\n  let body = res.body;\n  if (headers.get(\"content-type\") === \"application/json\") body = JSON.stringify(res.body);else if (headers.get(\"content-type\") === \"application/x-www-form-urlencoded\") body = new URLSearchParams(res.body).toString();\n  const status = res.redirect ? 302 : (_res$status = res.status) !== null && _res$status !== void 0 ? _res$status : 200;\n  const response = new Response(body, {\n    headers,\n    status\n  });\n  if (res.redirect) response.headers.set(\"Location\", res.redirect);\n  return response;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/next/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NIL: () => (/* reexport safe */ _nil_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   stringify: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   v1: () => (/* reexport safe */ _v1_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   v3: () => (/* reexport safe */ _v3_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   v4: () => (/* reexport safe */ _v4_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   v5: () => (/* reexport safe */ _v5_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   validate: () => (/* reexport safe */ _validate_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _v1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v1.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v1.js\");\n/* harmony import */ var _v3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./v3.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v3.js\");\n/* harmony import */ var _v4_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./v4.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _v5_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./v5.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v5.js\");\n/* harmony import */ var _nil_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nil.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/nil.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./version.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/version.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/parse.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0U7QUFDUTtBQUNFO0FBQ0UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IGFzIHYxIH0gZnJvbSAnLi92MS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHYzIH0gZnJvbSAnLi92My5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHY0IH0gZnJvbSAnLi92NC5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHY1IH0gZnJvbSAnLi92NS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIE5JTCB9IGZyb20gJy4vbmlsLmpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHZhbGlkYXRlIH0gZnJvbSAnLi92YWxpZGF0ZS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHN0cmluZ2lmeSB9IGZyb20gJy4vc3RyaW5naWZ5LmpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgcGFyc2UgfSBmcm9tICcuL3BhcnNlLmpzJzsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/md5.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/md5.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction md5(bytes) {\n  if (Array.isArray(bytes)) {\n    bytes = Buffer.from(bytes);\n  } else if (typeof bytes === 'string') {\n    bytes = Buffer.from(bytes, 'utf8');\n  }\n\n  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('md5').update(bytes).digest();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (md5);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvbWQ1LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0Qjs7QUFFNUI7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUEsU0FBUyx3REFBaUI7QUFDMUI7O0FBRUEsaUVBQWUsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXG5vZGVfbW9kdWxlc1xcdXVpZFxcZGlzdFxcZXNtLW5vZGVcXG1kNS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5cbmZ1bmN0aW9uIG1kNShieXRlcykge1xuICBpZiAoQXJyYXkuaXNBcnJheShieXRlcykpIHtcbiAgICBieXRlcyA9IEJ1ZmZlci5mcm9tKGJ5dGVzKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgYnl0ZXMgPT09ICdzdHJpbmcnKSB7XG4gICAgYnl0ZXMgPSBCdWZmZXIuZnJvbShieXRlcywgJ3V0ZjgnKTtcbiAgfVxuXG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnbWQ1JykudXBkYXRlKGJ5dGVzKS5kaWdlc3QoKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbWQ1OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/md5.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/nil.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/nil.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ('00000000-0000-0000-0000-000000000000');//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvbmlsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxzQ0FBc0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFxuaWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgJzAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCc7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/nil.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/parse.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/parse.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js\");\n\n\nfunction parse(uuid) {\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/regex.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/regex.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyx5Q0FBeUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFxyZWdleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLTVdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCkkL2k7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/regex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/rng.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/rng.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\n\nlet poolPtr = rnds8Pool.length;\nfunction rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    crypto__WEBPACK_IMPORTED_MODULE_0___default().randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QjtBQUM1Qix1Q0FBdUM7O0FBRXZDO0FBQ2U7QUFDZjtBQUNBLElBQUksNERBQXFCO0FBQ3pCO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXG5vZGVfbW9kdWxlc1xcdXVpZFxcZGlzdFxcZXNtLW5vZGVcXHJuZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5jb25zdCBybmRzOFBvb2wgPSBuZXcgVWludDhBcnJheSgyNTYpOyAvLyAjIG9mIHJhbmRvbSB2YWx1ZXMgdG8gcHJlLWFsbG9jYXRlXG5cbmxldCBwb29sUHRyID0gcm5kczhQb29sLmxlbmd0aDtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJuZygpIHtcbiAgaWYgKHBvb2xQdHIgPiBybmRzOFBvb2wubGVuZ3RoIC0gMTYpIHtcbiAgICBjcnlwdG8ucmFuZG9tRmlsbFN5bmMocm5kczhQb29sKTtcbiAgICBwb29sUHRyID0gMDtcbiAgfVxuXG4gIHJldHVybiBybmRzOFBvb2wuc2xpY2UocG9vbFB0ciwgcG9vbFB0ciArPSAxNik7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/rng.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/sha1.js":
/*!************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/sha1.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction sha1(bytes) {\n  if (Array.isArray(bytes)) {\n    bytes = Buffer.from(bytes);\n  } else if (typeof bytes === 'string') {\n    bytes = Buffer.from(bytes, 'utf8');\n  }\n\n  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha1').update(bytes).digest();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sha1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvc2hhMS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEI7O0FBRTVCO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBLFNBQVMsd0RBQWlCO0FBQzFCOztBQUVBLGlFQUFlLElBQUkiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFxzaGExLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJztcblxuZnVuY3Rpb24gc2hhMShieXRlcykge1xuICBpZiAoQXJyYXkuaXNBcnJheShieXRlcykpIHtcbiAgICBieXRlcyA9IEJ1ZmZlci5mcm9tKGJ5dGVzKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgYnl0ZXMgPT09ICdzdHJpbmcnKSB7XG4gICAgYnl0ZXMgPSBCdWZmZXIuZnJvbShieXRlcywgJ3V0ZjgnKTtcbiAgfVxuXG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMScpLnVwZGF0ZShieXRlcykuZGlnZXN0KCk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHNoYTE7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/sha1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\n\nfunction stringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  const uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v1.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/v1.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rng.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js\");\n\n // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(b);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v1.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v3.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/v3.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v35.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v35.js\");\n/* harmony import */ var _md5_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./md5.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/md5.js\");\n\n\nconst v3 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('v3', 0x30, _md5_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v3);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJCO0FBQ0E7QUFDM0IsV0FBVyxtREFBRyxhQUFhLCtDQUFHO0FBQzlCLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFx2My5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdjM1IGZyb20gJy4vdjM1LmpzJztcbmltcG9ydCBtZDUgZnJvbSAnLi9tZDUuanMnO1xuY29uc3QgdjMgPSB2MzUoJ3YzJywgMHgzMCwgbWQ1KTtcbmV4cG9ydCBkZWZhdWx0IHYzOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v3.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v35.js":
/*!***********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/v35.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DNS: () => (/* binding */ DNS),\n/* harmony export */   URL: () => (/* binding */ URL),\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/parse.js\");\n\n\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nconst DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nconst URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = (0,_parse_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(namespace);\n    }\n\n    if (namespace.length !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v35.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v4.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/v4.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rng.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(rnds);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJCO0FBQ1k7O0FBRXZDO0FBQ0E7QUFDQSxpREFBaUQsK0NBQUcsS0FBSzs7QUFFekQ7QUFDQSxtQ0FBbUM7O0FBRW5DO0FBQ0E7O0FBRUEsb0JBQW9CLFFBQVE7QUFDNUI7QUFDQTs7QUFFQTtBQUNBOztBQUVBLFNBQVMseURBQVM7QUFDbEI7O0FBRUEsaUVBQWUsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXG5vZGVfbW9kdWxlc1xcdXVpZFxcZGlzdFxcZXNtLW5vZGVcXHY0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBybmcgZnJvbSAnLi9ybmcuanMnO1xuaW1wb3J0IHN0cmluZ2lmeSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5cbmZ1bmN0aW9uIHY0KG9wdGlvbnMsIGJ1Ziwgb2Zmc2V0KSB7XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gfHwgKG9wdGlvbnMucm5nIHx8IHJuZykoKTsgLy8gUGVyIDQuNCwgc2V0IGJpdHMgZm9yIHZlcnNpb24gYW5kIGBjbG9ja19zZXFfaGlfYW5kX3Jlc2VydmVkYFxuXG4gIHJuZHNbNl0gPSBybmRzWzZdICYgMHgwZiB8IDB4NDA7XG4gIHJuZHNbOF0gPSBybmRzWzhdICYgMHgzZiB8IDB4ODA7IC8vIENvcHkgYnl0ZXMgdG8gYnVmZmVyLCBpZiBwcm92aWRlZFxuXG4gIGlmIChidWYpIHtcbiAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgMTY7ICsraSkge1xuICAgICAgYnVmW29mZnNldCArIGldID0gcm5kc1tpXTtcbiAgICB9XG5cbiAgICByZXR1cm4gYnVmO1xuICB9XG5cbiAgcmV0dXJuIHN0cmluZ2lmeShybmRzKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgdjQ7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v5.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/v5.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v35.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v35.js\");\n/* harmony import */ var _sha1_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sha1.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/sha1.js\");\n\n\nconst v5 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('v5', 0x50, _sha1_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v5);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJCO0FBQ0U7QUFDN0IsV0FBVyxtREFBRyxhQUFhLGdEQUFJO0FBQy9CLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFx2NS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdjM1IGZyb20gJy4vdjM1LmpzJztcbmltcG9ydCBzaGExIGZyb20gJy4vc2hhMS5qcyc7XG5jb25zdCB2NSA9IHYzNSgndjUnLCAweDUwLCBzaGExKTtcbmV4cG9ydCBkZWZhdWx0IHY1OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/v5.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js":
/*!****************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7O0FBRS9CO0FBQ0EscUNBQXFDLGlEQUFLO0FBQzFDOztBQUVBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFx2YWxpZGF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5cbmZ1bmN0aW9uIHZhbGlkYXRlKHV1aWQpIHtcbiAgcmV0dXJuIHR5cGVvZiB1dWlkID09PSAnc3RyaW5nJyAmJiBSRUdFWC50ZXN0KHV1aWQpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/version.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next-auth/node_modules/uuid/dist/esm-node/version.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/validate.js\");\n\n\nfunction version(uuid) {\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.substr(14, 1), 16);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (version);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxQzs7QUFFckM7QUFDQSxPQUFPLHdEQUFRO0FBQ2Y7QUFDQTs7QUFFQTtBQUNBOztBQUVBLGlFQUFlLE9BQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxub2RlX21vZHVsZXNcXHV1aWRcXGRpc3RcXGVzbS1ub2RlXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB2YWxpZGF0ZSBmcm9tICcuL3ZhbGlkYXRlLmpzJztcblxuZnVuY3Rpb24gdmVyc2lvbih1dWlkKSB7XG4gIGlmICghdmFsaWRhdGUodXVpZCkpIHtcbiAgICB0aHJvdyBUeXBlRXJyb3IoJ0ludmFsaWQgVVVJRCcpO1xuICB9XG5cbiAgcmV0dXJuIHBhcnNlSW50KHV1aWQuc3Vic3RyKDE0LCAxKSwgMTYpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2ZXJzaW9uOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/node_modules/uuid/dist/esm-node/version.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = Credentials;\nfunction Credentials(options) {\n  return {\n    id: \"credentials\",\n    name: \"Credentials\",\n    type: \"credentials\",\n    credentials: {},\n    authorize: () => null,\n    options\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUI7QUFDbkI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxwcm92aWRlcnNcXGNyZWRlbnRpYWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gQ3JlZGVudGlhbHM7XG5mdW5jdGlvbiBDcmVkZW50aWFscyhvcHRpb25zKSB7XG4gIHJldHVybiB7XG4gICAgaWQ6IFwiY3JlZGVudGlhbHNcIixcbiAgICBuYW1lOiBcIkNyZWRlbnRpYWxzXCIsXG4gICAgdHlwZTogXCJjcmVkZW50aWFsc1wiLFxuICAgIGNyZWRlbnRpYWxzOiB7fSxcbiAgICBhdXRob3JpemU6ICgpID0+IG51bGwsXG4gICAgb3B0aW9uc1xuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/detect-origin.js":
/*!*******************************************************!*\
  !*** ./node_modules/next-auth/utils/detect-origin.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.detectOrigin = detectOrigin;\nfunction detectOrigin(forwardedHost, protocol) {\n  var _process$env$VERCEL;\n  if ((_process$env$VERCEL = process.env.VERCEL) !== null && _process$env$VERCEL !== void 0 ? _process$env$VERCEL : process.env.AUTH_TRUST_HOST) return `${protocol === \"http\" ? \"http\" : \"https\"}://${forwardedHost}`;\n  return process.env.NEXTAUTH_URL;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL2RldGVjdC1vcmlnaW4uanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQSwySkFBMkosdUNBQXVDLEtBQUssY0FBYztBQUNyTjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHBjXFxPbmVEcml2ZVxcRGVza3RvcFxcRGVza3RvcFxcU29seW50YV9XZWJzaXRlXFxmcm9udGVuZFxcbGVzc29uLXBsYXRmb3JtXFxub2RlX21vZHVsZXNcXG5leHQtYXV0aFxcdXRpbHNcXGRldGVjdC1vcmlnaW4uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRldGVjdE9yaWdpbiA9IGRldGVjdE9yaWdpbjtcbmZ1bmN0aW9uIGRldGVjdE9yaWdpbihmb3J3YXJkZWRIb3N0LCBwcm90b2NvbCkge1xuICB2YXIgX3Byb2Nlc3MkZW52JFZFUkNFTDtcbiAgaWYgKChfcHJvY2VzcyRlbnYkVkVSQ0VMID0gcHJvY2Vzcy5lbnYuVkVSQ0VMKSAhPT0gbnVsbCAmJiBfcHJvY2VzcyRlbnYkVkVSQ0VMICE9PSB2b2lkIDAgPyBfcHJvY2VzcyRlbnYkVkVSQ0VMIDogcHJvY2Vzcy5lbnYuQVVUSF9UUlVTVF9IT1NUKSByZXR1cm4gYCR7cHJvdG9jb2wgPT09IFwiaHR0cFwiID8gXCJodHRwXCIgOiBcImh0dHBzXCJ9Oi8vJHtmb3J3YXJkZWRIb3N0fWA7XG4gIHJldHVybiBwcm9jZXNzLmVudi5ORVhUQVVUSF9VUkw7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/detect-origin.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/logger.js":
/*!************************************************!*\
  !*** ./node_modules/next-auth/utils/logger.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nvar _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ \"(rsc)/./node_modules/@babel/runtime/helpers/interopRequireDefault.js\");\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nexports.proxyLogger = proxyLogger;\nexports.setLogger = setLogger;\nvar _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ \"(rsc)/./node_modules/@babel/runtime/regenerator/index.js\"));\nvar _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"(rsc)/./node_modules/@babel/runtime/helpers/defineProperty.js\"));\nvar _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ \"(rsc)/./node_modules/@babel/runtime/helpers/asyncToGenerator.js\"));\nvar _errors = __webpack_require__(/*! ../core/errors */ \"(rsc)/./node_modules/next-auth/core/errors.js\");\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0, _defineProperty2.default)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction formatError(o) {\n  if (o instanceof Error && !(o instanceof _errors.UnknownError)) {\n    return {\n      message: o.message,\n      stack: o.stack,\n      name: o.name\n    };\n  }\n  if (hasErrorProperty(o)) {\n    var _o$message;\n    o.error = formatError(o.error);\n    o.message = (_o$message = o.message) !== null && _o$message !== void 0 ? _o$message : o.error.message;\n  }\n  return o;\n}\nfunction hasErrorProperty(x) {\n  return !!(x !== null && x !== void 0 && x.error);\n}\nvar _logger = {\n  error: function error(code, metadata) {\n    metadata = formatError(metadata);\n    console.error(\"[next-auth][error][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/errors#\".concat(code.toLowerCase()), metadata.message, metadata);\n  },\n  warn: function warn(code) {\n    console.warn(\"[next-auth][warn][\".concat(code, \"]\"), \"\\nhttps://next-auth.js.org/warnings#\".concat(code.toLowerCase()));\n  },\n  debug: function debug(code, metadata) {\n    console.log(\"[next-auth][debug][\".concat(code, \"]\"), metadata);\n  }\n};\nfunction setLogger() {\n  var newLogger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var debug = arguments.length > 1 ? arguments[1] : undefined;\n  if (!debug) _logger.debug = function () {};\n  if (newLogger.error) _logger.error = newLogger.error;\n  if (newLogger.warn) _logger.warn = newLogger.warn;\n  if (newLogger.debug) _logger.debug = newLogger.debug;\n}\nvar _default = exports[\"default\"] = _logger;\nfunction proxyLogger() {\n  var logger = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _logger;\n  var basePath = arguments.length > 1 ? arguments[1] : undefined;\n  try {\n    if (typeof window === \"undefined\") {\n      return logger;\n    }\n    var clientLogger = {};\n    var _loop = function _loop(level) {\n      clientLogger[level] = function () {\n        var _ref = (0, _asyncToGenerator2.default)(_regenerator.default.mark(function _callee(code, metadata) {\n          var url, body;\n          return _regenerator.default.wrap(function _callee$(_context) {\n            while (1) switch (_context.prev = _context.next) {\n              case 0:\n                _logger[level](code, metadata);\n                if (level === \"error\") {\n                  metadata = formatError(metadata);\n                }\n                ;\n                metadata.client = true;\n                url = \"\".concat(basePath, \"/_log\");\n                body = new URLSearchParams(_objectSpread({\n                  level: level,\n                  code: code\n                }, metadata));\n                if (!navigator.sendBeacon) {\n                  _context.next = 8;\n                  break;\n                }\n                return _context.abrupt(\"return\", navigator.sendBeacon(url, body));\n              case 8:\n                _context.next = 10;\n                return fetch(url, {\n                  method: \"POST\",\n                  body: body,\n                  keepalive: true\n                });\n              case 10:\n                return _context.abrupt(\"return\", _context.sent);\n              case 11:\n              case \"end\":\n                return _context.stop();\n            }\n          }, _callee);\n        }));\n        return function (_x, _x2) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n    };\n    for (var level in logger) {\n      _loop(level);\n    }\n    return clientLogger;\n  } catch (_unused) {\n    return _logger;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/merge.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/utils/merge.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.merge = merge;\nfunction isObject(item) {\n  return item && typeof item === \"object\" && !Array.isArray(item);\n}\nfunction merge(target, ...sources) {\n  if (!sources.length) return target;\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (!target[key]) Object.assign(target, {\n          [key]: {}\n        });\n        merge(target[key], source[key]);\n      } else {\n        Object.assign(target, {\n          [key]: source[key]\n        });\n      }\n    }\n  }\n  return merge(target, ...sources);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL21lcmdlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwY1xcT25lRHJpdmVcXERlc2t0b3BcXERlc2t0b3BcXFNvbHludGFfV2Vic2l0ZVxcZnJvbnRlbmRcXGxlc3Nvbi1wbGF0Zm9ybVxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXHV0aWxzXFxtZXJnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMubWVyZ2UgPSBtZXJnZTtcbmZ1bmN0aW9uIGlzT2JqZWN0KGl0ZW0pIHtcbiAgcmV0dXJuIGl0ZW0gJiYgdHlwZW9mIGl0ZW0gPT09IFwib2JqZWN0XCIgJiYgIUFycmF5LmlzQXJyYXkoaXRlbSk7XG59XG5mdW5jdGlvbiBtZXJnZSh0YXJnZXQsIC4uLnNvdXJjZXMpIHtcbiAgaWYgKCFzb3VyY2VzLmxlbmd0aCkgcmV0dXJuIHRhcmdldDtcbiAgY29uc3Qgc291cmNlID0gc291cmNlcy5zaGlmdCgpO1xuICBpZiAoaXNPYmplY3QodGFyZ2V0KSAmJiBpc09iamVjdChzb3VyY2UpKSB7XG4gICAgZm9yIChjb25zdCBrZXkgaW4gc291cmNlKSB7XG4gICAgICBpZiAoaXNPYmplY3Qoc291cmNlW2tleV0pKSB7XG4gICAgICAgIGlmICghdGFyZ2V0W2tleV0pIE9iamVjdC5hc3NpZ24odGFyZ2V0LCB7XG4gICAgICAgICAgW2tleV06IHt9XG4gICAgICAgIH0pO1xuICAgICAgICBtZXJnZSh0YXJnZXRba2V5XSwgc291cmNlW2tleV0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgT2JqZWN0LmFzc2lnbih0YXJnZXQsIHtcbiAgICAgICAgICBba2V5XTogc291cmNlW2tleV1cbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIHJldHVybiBtZXJnZSh0YXJnZXQsIC4uLnNvdXJjZXMpO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/merge.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/utils/parse-url.js":
/*!***************************************************!*\
  !*** ./node_modules/next-auth/utils/parse-url.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = parseUrl;\nfunction parseUrl(url) {\n  var _url2;\n  const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n  if (url && !url.startsWith(\"http\")) {\n    url = `https://${url}`;\n  }\n  const _url = new URL((_url2 = url) !== null && _url2 !== void 0 ? _url2 : defaultUrl);\n  const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname).replace(/\\/$/, \"\");\n  const base = `${_url.origin}${path}`;\n  return {\n    origin: _url.origin,\n    host: _url.host,\n    path,\n    base,\n    toString: () => base\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3V0aWxzL3BhcnNlLXVybC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFlBQVksRUFBRSxLQUFLO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGNcXE9uZURyaXZlXFxEZXNrdG9wXFxEZXNrdG9wXFxTb2x5bnRhX1dlYnNpdGVcXGZyb250ZW5kXFxsZXNzb24tcGxhdGZvcm1cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFx1dGlsc1xccGFyc2UtdXJsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gcGFyc2VVcmw7XG5mdW5jdGlvbiBwYXJzZVVybCh1cmwpIHtcbiAgdmFyIF91cmwyO1xuICBjb25zdCBkZWZhdWx0VXJsID0gbmV3IFVSTChcImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGkvYXV0aFwiKTtcbiAgaWYgKHVybCAmJiAhdXJsLnN0YXJ0c1dpdGgoXCJodHRwXCIpKSB7XG4gICAgdXJsID0gYGh0dHBzOi8vJHt1cmx9YDtcbiAgfVxuICBjb25zdCBfdXJsID0gbmV3IFVSTCgoX3VybDIgPSB1cmwpICE9PSBudWxsICYmIF91cmwyICE9PSB2b2lkIDAgPyBfdXJsMiA6IGRlZmF1bHRVcmwpO1xuICBjb25zdCBwYXRoID0gKF91cmwucGF0aG5hbWUgPT09IFwiL1wiID8gZGVmYXVsdFVybC5wYXRobmFtZSA6IF91cmwucGF0aG5hbWUpLnJlcGxhY2UoL1xcLyQvLCBcIlwiKTtcbiAgY29uc3QgYmFzZSA9IGAke191cmwub3JpZ2lufSR7cGF0aH1gO1xuICByZXR1cm4ge1xuICAgIG9yaWdpbjogX3VybC5vcmlnaW4sXG4gICAgaG9zdDogX3VybC5ob3N0LFxuICAgIHBhdGgsXG4gICAgYmFzZSxcbiAgICB0b1N0cmluZzogKCkgPT4gYmFzZVxuICB9O1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/utils/parse-url.js\n");

/***/ })

};
;